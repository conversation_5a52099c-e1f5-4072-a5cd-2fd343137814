(()=>{var e={};e.id=974,e.ids=[974],e.modules={485:()=>{},699:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(8625),a=s(4996),i=s.n(a);s(9485);var n=s(1567),l=s.n(n);let o=({data:e,sortField:t,sortDirection:s,onSort:a})=>{let i=(e,t=2)=>e.toLocaleString("en-IN",{minimumFractionDigits:t,maximumFractionDigits:t}),n=e=>e>=1e7?`${(e/1e7).toFixed(1)}Cr`:e>=1e5?`${(e/1e5).toFixed(1)}L`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),o=({field:e,children:i,className:n=""})=>(0,r.jsx)("th",{className:`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors ${n}`,onClick:()=>a(e),children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:i}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("svg",{className:`w-3 h-3 ${t===e&&"asc"===s?"text-blue-600":"text-gray-400"}`,fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})}),(0,r.jsx)("svg",{className:`w-3 h-3 -mt-1 ${t===e&&"desc"===s?"text-blue-600":"text-gray-400"}`,fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})});return 0===e.length?(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)("div",{className:"text-lg font-medium",children:"No market data available"}),(0,r.jsx)("div",{className:"text-sm",children:"Waiting for real-time updates from the market feed..."})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)(o,{field:"ticker",children:"Symbol"}),(0,r.jsx)(o,{field:"exchange",children:"Exchange"}),(0,r.jsx)(o,{field:"ltp",className:"text-right",children:"LTP"}),(0,r.jsx)(o,{field:"change",className:"text-right",children:"Change"}),(0,r.jsx)(o,{field:"changePercent",className:"text-right",children:"Change %"}),(0,r.jsx)(o,{field:"volume",className:"text-right",children:"Volume"}),(0,r.jsx)(o,{field:"high",className:"text-right",children:"High"}),(0,r.jsx)(o,{field:"low",className:"text-right",children:"Low"}),(0,r.jsx)(o,{field:"open",className:"text-right",children:"Open"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,r.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(l(),{href:`/stock/${e.ticker}`,className:"text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline transition-colors cursor-pointer",children:e.ticker}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.securityId})]})}),(0,r.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.exchange})}),(0,r.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm font-medium text-gray-900",children:["₹",i(e.ltp)]}),(0,r.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm",children:(0,r.jsxs)("span",{className:`font-medium ${e.change>=0?"text-green-600":"text-red-600"}`,children:[e.change>=0?"+":"",i(e.change)]})}),(0,r.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm",children:(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e.changePercent>=0?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[e.changePercent>=0?"+":"",i(e.changePercent),"%"]})}),(0,r.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900",children:n(e.volume)}),(0,r.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900",children:["₹",i(e.high)]}),(0,r.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900",children:["₹",i(e.low)]}),(0,r.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900",children:["₹",i(e.open)]})]},e.securityId))})]})})},d=({currentPage:e,totalPages:t,onPageChange:s,itemsPerPage:a,onItemsPerPageChange:n,totalItems:l})=>{let o=(e-1)*a+1,d=Math.min(e*a,l);return t<=1?(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("label",{htmlFor:"itemsPerPage",className:"mr-2 text-sm text-gray-700",children:"Show:"}),(0,r.jsxs)("select",{id:"itemsPerPage",value:a,onChange:e=>n(Number(e.target.value)),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:10,children:"10"}),(0,r.jsx)("option",{value:25,children:"25"}),(0,r.jsx)("option",{value:50,children:"50"}),(0,r.jsx)("option",{value:100,children:"100"})]}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"per page"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",o," to ",d," of ",l," results"]})]}):(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("label",{htmlFor:"itemsPerPage",className:"mr-2 text-sm text-gray-700",children:"Show:"}),(0,r.jsxs)("select",{id:"itemsPerPage",value:a,onChange:e=>n(Number(e.target.value)),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:10,children:"10"}),(0,r.jsx)("option",{value:25,children:"25"}),(0,r.jsx)("option",{value:50,children:"50"}),(0,r.jsx)("option",{value:100,children:"100"})]}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"per page"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",o," to ",d," of ",l," results"]}),(0,r.jsxs)("nav",{className:"flex items-center space-x-1",children:[(0,r.jsx)("button",{type:"button",onClick:()=>s(e-1),disabled:1===e,className:`px-3 py-2 text-sm font-medium rounded-md ${1===e?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,children:"‹"}),(()=>{let s=[],r=[];for(let r=Math.max(2,e-2);r<=Math.min(t-1,e+2);r++)s.push(r);return e-2>2?r.push(1,"..."):r.push(1),r.push(...s),e+2<t-1?r.push("...",t):t>1&&r.push(t),r})().map((t,a)=>(0,r.jsx)(i().Fragment,{children:"..."===t?(0,r.jsx)("span",{className:"px-3 py-2 text-sm text-gray-500",children:"..."}):(0,r.jsx)("button",{type:"button",onClick:()=>s(t),className:`px-3 py-2 text-sm font-medium rounded-md ${e===t?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-100"}`,children:t})},a)),(0,r.jsx)("button",{type:"button",onClick:()=>s(e+1),disabled:e===t,className:`px-3 py-2 text-sm font-medium rounded-md ${e===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"}`,children:"›"})]})]})]})};function c({stats:e}){return(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Instruments"}),(0,r.jsx)("div",{className:"text-2xl font-semibold text-blue-600",children:e.instrumentCount})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Updates/sec"}),(0,r.jsx)("div",{className:"text-2xl font-semibold text-green-600",children:e.updateRate})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Latency"}),(0,r.jsxs)("div",{className:"text-2xl font-semibold text-purple-600",children:[e.latency,"ms"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Last Update"}),(0,r.jsx)("div",{className:"text-2xl font-semibold text-gray-600",children:e.lastUpdate})]})]})}let x=({isConnected:e})=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full mr-2 ${e?"bg-green-500":"bg-red-500"}`}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e?"Connected":"Disconnected"})]});function m(){let[e,t]=(0,a.useState)(null),[s,i]=(0,a.useState)(!1),[n,l]=(0,a.useState)(new Map),[m,u]=(0,a.useState)(""),[h,p]=(0,a.useState)(""),[g,f]=(0,a.useState)(1),[b,v]=(0,a.useState)(10),[j,y]=(0,a.useState)("ticker"),[N,w]=(0,a.useState)("asc"),[P,k]=(0,a.useState)({instrumentCount:0,updateRate:0,latency:0,lastUpdate:"Never"}),C=(0,a.useRef)(0),S=(0,a.useRef)(Date.now()),q=(0,a.useRef)(0);(0,a.useRef)(null);let M=e=>{let t=Date.now();C.current++,q.current++,t-S.current>=1e3&&(k(t=>({...t,updateRate:q.current,latency:e,lastUpdate:new Date().toLocaleTimeString()})),q.current=0,S.current=t)};(0,a.useCallback)(e=>{M(Date.now()-e.timestamp),l(t=>{let s=t.get(e.securityId);if(s&&s.timestamp>=e.timestamp)return t;let r=new Map(t);return r.set(e.securityId,e),r})},[]),(0,a.useCallback)(e=>{0!==e.length&&(M(Date.now()-e[0].timestamp),l(t=>{let s=new Map(t),r=!1;return e.forEach(e=>{let t=s.get(e.securityId);(!t||t.timestamp<e.timestamp)&&(s.set(e.securityId,e),r=!0)}),r?s:t}))},[]);let _=(0,a.useCallback)(e=>{j===e?w("asc"===N?"desc":"asc"):(y(e),w("asc")),f(1)},[j,N]),D=(0,a.useMemo)(()=>{let e=Array.from(n.values());if(m){let t=m.toLowerCase();e=e.filter(e=>e.ticker.toLowerCase().includes(t)||e.securityId.includes(t))}return h&&(e=e.filter(e=>e.exchange===h)),e.sort((e,t)=>{let s=e[j],r=t[j];if("string"==typeof s&&"string"==typeof r){let e=s.localeCompare(r);return"asc"===N?e:-e}if("number"==typeof s&&"number"==typeof r){let e=s-r;return"asc"===N?e:-e}return 0}),e},[n,m,h,j,N]),E=(0,a.useMemo)(()=>{let e=(g-1)*b,t=e+b;return D.slice(e,t)},[D,g,b]),F=Math.ceil(D.length/b);return(0,r.jsx)("main",{className:"min-h-screen bg-gray-50 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"⚡ Ultra-Fast Market Dashboard"}),(0,r.jsx)(x,{isConnected:s})]}),(0,r.jsx)(c,{stats:P}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Instruments"}),(0,r.jsx)("input",{type:"text",id:"search",placeholder:"Search by ticker or security ID...",value:m,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"md:w-48",children:[(0,r.jsx)("label",{htmlFor:"exchange",className:"block text-sm font-medium text-gray-700 mb-2",children:"Exchange"}),(0,r.jsxs)("select",{id:"exchange",value:h,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Exchanges"}),(0,r.jsx)("option",{value:"NSE_EQ",children:"NSE Equity"}),(0,r.jsx)("option",{value:"NSE_FNO",children:"NSE F&O"}),(0,r.jsx)("option",{value:"BSE_EQ",children:"BSE Equity"}),(0,r.jsx)("option",{value:"MCX_COMM",children:"MCX Commodity"})]})]}),(0,r.jsx)("div",{className:"md:w-32 flex items-end",children:(0,r.jsx)("button",{type:"button",onClick:()=>{u(""),p("")},className:"w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500",children:"Clear"})})]}),(0,r.jsxs)("div",{className:"mt-2 text-sm text-gray-600",children:["Showing ",E.length," of ",D.length," instruments",D.length!==n.size&&(0,r.jsxs)("span",{className:"text-gray-500",children:[" ","(filtered from ",n.size," total)"]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)(o,{data:E,sortField:j,sortDirection:N,onSort:_}),(0,r.jsx)(d,{currentPage:g,totalPages:F,onPageChange:f,itemsPerPage:b,onItemsPerPageChange:v,totalItems:D.length})]})]})})}},776:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1384:()=>{},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},1962:e=>{"use strict";e.exports=require("bufferutil")},3009:(e,t,s)=>{Promise.resolve().then(s.bind(s,776))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3281:(e,t,s)=>{Promise.resolve().then(s.bind(s,699))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4769:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9782,23)),Promise.resolve().then(s.t.bind(s,3552,23)),Promise.resolve().then(s.t.bind(s,708,23)),Promise.resolve().then(s.t.bind(s,7319,23)),Promise.resolve().then(s.t.bind(s,2079,23)),Promise.resolve().then(s.t.bind(s,868,23)),Promise.resolve().then(s.t.bind(s,5543,23)),Promise.resolve().then(s.t.bind(s,2241,23))},4936:()=>{},5347:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>a});var r=s(7307);s(6174),s(485);let a={title:"Dhan Market Dashboard",description:"Ultra-fast market data dashboard for Dhan trading platform"};function i({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{children:e})})}},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6206:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(5853),a=s(554),i=s(708),n=s.n(i),l=s(8067),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,776)),"D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,5347)),"D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,2192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,2137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,8358,23)),"next/dist/client/components/unauthorized-error"]}],c=["D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6809:e=>{"use strict";e.exports=require("utf-8-validate")},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")},9921:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,8416,23)),Promise.resolve().then(s.t.bind(s,7342,23)),Promise.resolve().then(s.t.bind(s,4078,23)),Promise.resolve().then(s.t.bind(s,4193,23)),Promise.resolve().then(s.t.bind(s,1573,23)),Promise.resolve().then(s.t.bind(s,5405,23)),Promise.resolve().then(s.t.bind(s,7301,23)),Promise.resolve().then(s.t.bind(s,6159,23))}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[625,425,410],()=>s(6206));module.exports=r})();