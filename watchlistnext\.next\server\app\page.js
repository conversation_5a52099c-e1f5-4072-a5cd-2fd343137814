/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cwebsocketdhan%5Cdhan-websocket-server%5Cwatchlistnext%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cwebsocketdhan%5Cdhan-websocket-server%5Cwatchlistnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cwebsocketdhan%5Cdhan-websocket-server%5Cwatchlistnext%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cwebsocketdhan%5Cdhan-websocket-server%5Cwatchlistnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?fc76\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cwebsocketdhan%5Cdhan-websocket-server%5Cwatchlistnext%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cwebsocketdhan%5Cdhan-websocket-server%5Cwatchlistnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDd2Vic29ja2V0ZGhhbiU1QyU1Q2RoYW4td2Vic29ja2V0LXNlcnZlciU1QyU1Q3dhdGNobGlzdG5leHQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWdIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFx3ZWJzb2NrZXRkaGFuXFxcXGRoYW4td2Vic29ja2V0LXNlcnZlclxcXFx3YXRjaGxpc3RuZXh0XFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c95fac3a1eb3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcd2Vic29ja2V0ZGhhblxcZGhhbi13ZWJzb2NrZXQtc2VydmVyXFx3YXRjaGxpc3RuZXh0XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjOTVmYWMzYTFlYjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Dhan Market Dashboard\",\n    description: \"Ultra-fast market data dashboard for Dhan trading platform\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBMEI7QUFDSDtBQUdoQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFBTUg7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIkQ6XFx3ZWJzb2NrZXRkaGFuXFxkaGFuLXdlYnNvY2tldC1zZXJ2ZXJcXHdhdGNobGlzdG5leHRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xyXG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6IFwiRGhhbiBNYXJrZXQgRGFzaGJvYXJkXCIsXHJcbiAgZGVzY3JpcHRpb246IFwiVWx0cmEtZmFzdCBtYXJrZXQgZGF0YSBkYXNoYm9hcmQgZm9yIERoYW4gdHJhZGluZyBwbGF0Zm9ybVwiLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDd2Vic29ja2V0ZGhhbiU1QyU1Q2RoYW4td2Vic29ja2V0LXNlcnZlciU1QyU1Q3dhdGNobGlzdG5leHQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWdIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFx3ZWJzb2NrZXRkaGFuXFxcXGRoYW4td2Vic29ja2V0LXNlcnZlclxcXFx3YXRjaGxpc3RuZXh0XFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cwebsocketdhan%5C%5Cdhan-websocket-server%5C%5Cwatchlistnext%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/../node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _components_EnhancedMarketDataTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/EnhancedMarketDataTable */ \"(ssr)/./src/components/EnhancedMarketDataTable.tsx\");\n/* harmony import */ var _components_Pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Pagination */ \"(ssr)/./src/components/Pagination.tsx\");\n/* harmony import */ var _components_Stats__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Stats */ \"(ssr)/./src/components/Stats.tsx\");\n/* harmony import */ var _components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ConnectionStatus */ \"(ssr)/./src/components/ConnectionStatus.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedExchange, setSelectedExchange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Pagination and sorting states\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ticker\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        instrumentCount: 0,\n        updateRate: 0,\n        latency: 0,\n        lastUpdate: \"Never\"\n    });\n    const updateCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const lastSecondRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(Date.now());\n    const updatesThisSecondRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const batchUpdateTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Helper function to update stats\n    const updateStats = (latency)=>{\n        const now = Date.now();\n        updateCountRef.current++;\n        updatesThisSecondRef.current++;\n        if (now - lastSecondRef.current >= 1000) {\n            setStats((prev)=>({\n                    ...prev,\n                    updateRate: updatesThisSecondRef.current,\n                    latency,\n                    lastUpdate: new Date().toLocaleTimeString()\n                }));\n            updatesThisSecondRef.current = 0;\n            lastSecondRef.current = now;\n        }\n    };\n    // Optimized helper function to update single market data\n    const updateMarketDataSingle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[updateMarketDataSingle]\": (data)=>{\n            const now = Date.now();\n            const latency = now - data.timestamp;\n            updateStats(latency);\n            setMarketData({\n                \"Home.useCallback[updateMarketDataSingle]\": (prev)=>{\n                    // Only update if data has actually changed\n                    const existing = prev.get(data.securityId);\n                    if (existing && existing.timestamp >= data.timestamp) {\n                        return prev; // Skip if we already have newer data\n                    }\n                    const newData = new Map(prev);\n                    newData.set(data.securityId, data);\n                    return newData;\n                }\n            }[\"Home.useCallback[updateMarketDataSingle]\"]);\n        }\n    }[\"Home.useCallback[updateMarketDataSingle]\"], []);\n    // Optimized helper function to update batch market data\n    const updateMarketDataBatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[updateMarketDataBatch]\": (batch)=>{\n            if (batch.length === 0) return;\n            const now = Date.now();\n            const latency = now - batch[0].timestamp;\n            updateStats(latency);\n            setMarketData({\n                \"Home.useCallback[updateMarketDataBatch]\": (prev)=>{\n                    const newData = new Map(prev);\n                    let hasChanges = false;\n                    batch.forEach({\n                        \"Home.useCallback[updateMarketDataBatch]\": (data)=>{\n                            const existing = newData.get(data.securityId);\n                            if (!existing || existing.timestamp < data.timestamp) {\n                                newData.set(data.securityId, data);\n                                hasChanges = true;\n                            }\n                        }\n                    }[\"Home.useCallback[updateMarketDataBatch]\"]);\n                    return hasChanges ? newData : prev; // Only return new map if there are changes\n                }\n            }[\"Home.useCallback[updateMarketDataBatch]\"]);\n        }\n    }[\"Home.useCallback[updateMarketDataBatch]\"], []);\n    // Sorting function\n    const handleSort = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Home.useCallback[handleSort]\": (field)=>{\n            if (sortField === field) {\n                setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\n            } else {\n                setSortField(field);\n                setSortDirection(\"asc\");\n            }\n            setCurrentPage(1); // Reset to first page when sorting\n        }\n    }[\"Home.useCallback[handleSort]\"], [\n        sortField,\n        sortDirection\n    ]);\n    // Memoized filter and sort function for better performance\n    const processedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Home.useMemo[processedData]\": ()=>{\n            let filtered = Array.from(marketData.values());\n            // Apply search filter\n            if (searchTerm) {\n                const search = searchTerm.toLowerCase();\n                filtered = filtered.filter({\n                    \"Home.useMemo[processedData]\": (item)=>item.ticker.toLowerCase().includes(search) || item.securityId.includes(search)\n                }[\"Home.useMemo[processedData]\"]);\n            }\n            // Apply exchange filter\n            if (selectedExchange) {\n                filtered = filtered.filter({\n                    \"Home.useMemo[processedData]\": (item)=>item.exchange === selectedExchange\n                }[\"Home.useMemo[processedData]\"]);\n            }\n            // Apply sorting\n            filtered.sort({\n                \"Home.useMemo[processedData]\": (a, b)=>{\n                    const aValue = a[sortField];\n                    const bValue = b[sortField];\n                    if (typeof aValue === \"string\" && typeof bValue === \"string\") {\n                        const comparison = aValue.localeCompare(bValue);\n                        return sortDirection === \"asc\" ? comparison : -comparison;\n                    }\n                    if (typeof aValue === \"number\" && typeof bValue === \"number\") {\n                        const comparison = aValue - bValue;\n                        return sortDirection === \"asc\" ? comparison : -comparison;\n                    }\n                    return 0;\n                }\n            }[\"Home.useMemo[processedData]\"]);\n            return filtered;\n        }\n    }[\"Home.useMemo[processedData]\"], [\n        marketData,\n        searchTerm,\n        selectedExchange,\n        sortField,\n        sortDirection\n    ]);\n    // Memoized pagination\n    const paginatedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Home.useMemo[paginatedData]\": ()=>{\n            const startIndex = (currentPage - 1) * itemsPerPage;\n            const endIndex = startIndex + itemsPerPage;\n            return processedData.slice(startIndex, endIndex);\n        }\n    }[\"Home.useMemo[paginatedData]\"], [\n        processedData,\n        currentPage,\n        itemsPerPage\n    ]);\n    // Calculate total pages\n    const totalPages = Math.ceil(processedData.length / itemsPerPage);\n    // Reset to first page when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setCurrentPage(1);\n        }\n    }[\"Home.useEffect\"], [\n        searchTerm,\n        selectedExchange,\n        itemsPerPage\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Connect to the WebSocket server running on port 8080\n            const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"http://localhost:8080\", {\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ],\n                upgrade: true,\n                rememberUpgrade: false,\n                timeout: 20000,\n                forceNew: true\n            });\n            socketInstance.on(\"initialData\", {\n                \"Home.useEffect\": (data)=>{\n                    setStats({\n                        \"Home.useEffect\": (prev)=>({\n                                ...prev,\n                                instrumentCount: data.instruments.length\n                            })\n                    }[\"Home.useEffect\"]);\n                    // Use functional update to avoid dependency on marketData\n                    setMarketData({\n                        \"Home.useEffect\": (prev)=>{\n                            const newMarketData = new Map(prev);\n                            data.liveData.forEach({\n                                \"Home.useEffect\": (item)=>{\n                                    newMarketData.set(item.securityId, item);\n                                }\n                            }[\"Home.useEffect\"]);\n                            return newMarketData;\n                        }\n                    }[\"Home.useEffect\"]);\n                }\n            }[\"Home.useEffect\"]);\n            // Handle individual market data updates (legacy support)\n            socketInstance.on(\"marketData\", {\n                \"Home.useEffect\": (data)=>{\n                    updateMarketDataSingle(data);\n                }\n            }[\"Home.useEffect\"]);\n            // Handle batch market data updates (optimized)\n            socketInstance.on(\"marketDataBatch\", {\n                \"Home.useEffect\": (batch)=>{\n                    updateMarketDataBatch(batch);\n                }\n            }[\"Home.useEffect\"]);\n            // Handle connection status\n            socketInstance.on(\"connect\", {\n                \"Home.useEffect\": ()=>{\n                    console.log(\"✅ Connected to WebSocket server\");\n                    setIsConnected(true);\n                }\n            }[\"Home.useEffect\"]);\n            socketInstance.on(\"disconnect\", {\n                \"Home.useEffect\": ()=>{\n                    console.log(\"❌ Disconnected from WebSocket server\");\n                    setIsConnected(false);\n                }\n            }[\"Home.useEffect\"]);\n            socketInstance.on(\"connect_error\", {\n                \"Home.useEffect\": (error)=>{\n                    console.error(\"❌ Connection error:\", error);\n                    setIsConnected(false);\n                }\n            }[\"Home.useEffect\"]);\n            setSocket(socketInstance);\n            // Fetch initial stats from the WebSocket server\n            fetch(\"http://localhost:8080/api/data\").then({\n                \"Home.useEffect\": (response)=>response.json()\n            }[\"Home.useEffect\"]).then({\n                \"Home.useEffect\": (data)=>{\n                    setStats({\n                        \"Home.useEffect\": (prev)=>({\n                                ...prev,\n                                instrumentCount: data.instruments?.length || 0\n                            })\n                    }[\"Home.useEffect\"]);\n                }\n            }[\"Home.useEffect\"]).catch({\n                \"Home.useEffect\": (error)=>console.error(\"Error fetching stats:\", error)\n            }[\"Home.useEffect\"]);\n            return ({\n                \"Home.useEffect\": ()=>{\n                    socketInstance.disconnect();\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"⚡ Ultra-Fast Market Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConnectionStatus__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            isConnected: isConnected\n                        }, void 0, false, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Stats__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    stats: stats\n                }, void 0, false, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"search\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Search Instruments\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"search\",\n                                            placeholder: \"Search by ticker or security ID...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:w-48\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"exchange\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Exchange\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"exchange\",\n                                            value: selectedExchange,\n                                            onChange: (e)=>setSelectedExchange(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"All Exchanges\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NSE_EQ\",\n                                                    children: \"NSE Equity\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NSE_FNO\",\n                                                    children: \"NSE F&O\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"BSE_EQ\",\n                                                    children: \"BSE Equity\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MCX_COMM\",\n                                                    children: \"MCX Commodity\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:w-32 flex items-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setSearchTerm(\"\");\n                                            setSelectedExchange(\"\");\n                                        },\n                                        className: \"w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500\",\n                                        children: \"Clear\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: [\n                                \"Showing \",\n                                paginatedData.length,\n                                \" of \",\n                                processedData.length,\n                                \" instruments\",\n                                processedData.length !== marketData.size && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: [\n                                        \" \",\n                                        \"(filtered from \",\n                                        marketData.size,\n                                        \" total)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EnhancedMarketDataTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            data: paginatedData,\n                            sortField: sortField,\n                            sortDirection: sortDirection,\n                            onSort: handleSort\n                        }, void 0, false, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Pagination__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            currentPage: currentPage,\n                            totalPages: totalPages,\n                            onPageChange: setCurrentPage,\n                            itemsPerPage: itemsPerPage,\n                            onItemsPerPageChange: setItemsPerPage,\n                            totalItems: processedData.length\n                        }, void 0, false, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 270,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ConnectionStatus.tsx":
/*!*********************************************!*\
  !*** ./src/components/ConnectionStatus.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ConnectionStatus = ({ isConnected })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `w-3 h-3 rounded-full mr-2 ${isConnected ? \"bg-green-500\" : \"bg-red-500\"}`\n            }, void 0, false, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: isConnected ? \"Connected\" : \"Disconnected\"\n            }, void 0, false, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\ConnectionStatus.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConnectionStatus);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Db25uZWN0aW9uU3RhdHVzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFNMUIsTUFBTUMsbUJBQW9ELENBQUMsRUFBRUMsV0FBVyxFQUFFO0lBQ3hFLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQ0NDLFdBQVcsQ0FBQywwQkFBMEIsRUFDcENGLGNBQWMsaUJBQWlCLGNBQy9COzs7Ozs7MEJBRUosOERBQUNHO2dCQUFLRCxXQUFVOzBCQUNiRixjQUFjLGNBQWM7Ozs7Ozs7Ozs7OztBQUlyQztBQUVBLGlFQUFlRCxnQkFBZ0JBLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFx3ZWJzb2NrZXRkaGFuXFxkaGFuLXdlYnNvY2tldC1zZXJ2ZXJcXHdhdGNobGlzdG5leHRcXHNyY1xcY29tcG9uZW50c1xcQ29ubmVjdGlvblN0YXR1cy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuaW50ZXJmYWNlIENvbm5lY3Rpb25TdGF0dXNQcm9wcyB7XHJcbiAgaXNDb25uZWN0ZWQ6IGJvb2xlYW47XHJcbn1cclxuXHJcbmNvbnN0IENvbm5lY3Rpb25TdGF0dXM6IFJlYWN0LkZDPENvbm5lY3Rpb25TdGF0dXNQcm9wcz4gPSAoeyBpc0Nvbm5lY3RlZCB9KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT17YHctMyBoLTMgcm91bmRlZC1mdWxsIG1yLTIgJHtcclxuICAgICAgICAgIGlzQ29ubmVjdGVkID8gXCJiZy1ncmVlbi01MDBcIiA6IFwiYmctcmVkLTUwMFwiXHJcbiAgICAgICAgfWB9XHJcbiAgICAgIC8+XHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxyXG4gICAgICAgIHtpc0Nvbm5lY3RlZCA/IFwiQ29ubmVjdGVkXCIgOiBcIkRpc2Nvbm5lY3RlZFwifVxyXG4gICAgICA8L3NwYW4+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgQ29ubmVjdGlvblN0YXR1cztcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ29ubmVjdGlvblN0YXR1cyIsImlzQ29ubmVjdGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ConnectionStatus.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/EnhancedMarketDataTable.tsx":
/*!****************************************************!*\
  !*** ./src/components/EnhancedMarketDataTable.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst EnhancedMarketDataTable = ({ data, sortField, sortDirection, onSort })=>{\n    const formatNumber = (num, decimals = 2)=>{\n        return num.toLocaleString(\"en-IN\", {\n            minimumFractionDigits: decimals,\n            maximumFractionDigits: decimals\n        });\n    };\n    const formatVolume = (volume)=>{\n        if (volume >= 10000000) {\n            return `${(volume / 10000000).toFixed(1)}Cr`;\n        } else if (volume >= 100000) {\n            return `${(volume / 100000).toFixed(1)}L`;\n        } else if (volume >= 1000) {\n            return `${(volume / 1000).toFixed(1)}K`;\n        }\n        return volume.toString();\n    };\n    const SortableHeader = ({ field, children, className = \"\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n            className: `px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors ${className}`,\n            onClick: ()=>onSort(field),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: `w-3 h-3 ${sortField === field && sortDirection === \"asc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: `w-3 h-3 -mt-1 ${sortField === field && sortDirection === \"desc\" ? \"text-blue-600\" : \"text-gray-400\"}`,\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n            lineNumber: 56,\n            columnNumber: 5\n        }, undefined);\n    if (data.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-lg font-medium\",\n                    children: \"No market data available\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: \"Waiting for real-time updates from the market feed...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    className: \"bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"ticker\",\n                                children: \"Symbol\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"exchange\",\n                                children: \"Exchange\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"ltp\",\n                                className: \"text-right\",\n                                children: \"LTP\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"change\",\n                                className: \"text-right\",\n                                children: \"Change\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"changePercent\",\n                                className: \"text-right\",\n                                children: \"Change %\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"volume\",\n                                className: \"text-right\",\n                                children: \"Volume\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"high\",\n                                className: \"text-right\",\n                                children: \"High\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"low\",\n                                className: \"text-right\",\n                                children: \"Low\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SortableHeader, {\n                                field: \"open\",\n                                className: \"text-right\",\n                                children: \"Open\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    className: \"bg-white divide-y divide-gray-200\",\n                    children: data.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: \"hover:bg-gray-50 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/stock/${item.ticker}`,\n                                                className: \"text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline transition-colors cursor-pointer\",\n                                                children: item.ticker\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: item.securityId\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                        children: item.exchange\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap text-right text-sm font-medium text-gray-900\",\n                                    children: [\n                                        \"₹\",\n                                        formatNumber(item.ltp)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap text-right text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `font-medium ${item.change >= 0 ? \"text-green-600\" : \"text-red-600\"}`,\n                                        children: [\n                                            item.change >= 0 ? \"+\" : \"\",\n                                            formatNumber(item.change)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap text-right text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${item.changePercent >= 0 ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                                        children: [\n                                            item.changePercent >= 0 ? \"+\" : \"\",\n                                            formatNumber(item.changePercent),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900\",\n                                    children: formatVolume(item.volume)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900\",\n                                    children: [\n                                        \"₹\",\n                                        formatNumber(item.high)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900\",\n                                    children: [\n                                        \"₹\",\n                                        formatNumber(item.low)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900\",\n                                    children: [\n                                        \"₹\",\n                                        formatNumber(item.open)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, item.securityId, true, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\EnhancedMarketDataTable.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedMarketDataTable);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/EnhancedMarketDataTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Pagination.tsx":
/*!***************************************!*\
  !*** ./src/components/Pagination.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Pagination = ({ currentPage, totalPages, onPageChange, itemsPerPage, onItemsPerPageChange, totalItems })=>{\n    const getVisiblePages = ()=>{\n        const delta = 2;\n        const range = [];\n        const rangeWithDots = [];\n        for(let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++){\n            range.push(i);\n        }\n        if (currentPage - delta > 2) {\n            rangeWithDots.push(1, \"...\");\n        } else {\n            rangeWithDots.push(1);\n        }\n        rangeWithDots.push(...range);\n        if (currentPage + delta < totalPages - 1) {\n            rangeWithDots.push(\"...\", totalPages);\n        } else if (totalPages > 1) {\n            rangeWithDots.push(totalPages);\n        }\n        return rangeWithDots;\n    };\n    const startItem = (currentPage - 1) * itemsPerPage + 1;\n    const endItem = Math.min(currentPage * itemsPerPage, totalItems);\n    if (totalPages <= 1) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"itemsPerPage\",\n                            className: \"mr-2 text-sm text-gray-700\",\n                            children: \"Show:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            id: \"itemsPerPage\",\n                            value: itemsPerPage,\n                            onChange: (e)=>onItemsPerPageChange(Number(e.target.value)),\n                            className: \"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: 10,\n                                    children: \"10\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: 25,\n                                    children: \"25\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: 50,\n                                    children: \"50\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: 100,\n                                    children: \"100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-sm text-gray-700\",\n                            children: \"per page\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-700\",\n                    children: [\n                        \"Showing \",\n                        startItem,\n                        \" to \",\n                        endItem,\n                        \" of \",\n                        totalItems,\n                        \" results\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"itemsPerPage\",\n                        className: \"mr-2 text-sm text-gray-700\",\n                        children: \"Show:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        id: \"itemsPerPage\",\n                        value: itemsPerPage,\n                        onChange: (e)=>onItemsPerPageChange(Number(e.target.value)),\n                        className: \"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 10,\n                                children: \"10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 25,\n                                children: \"25\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 50,\n                                children: \"50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 100,\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-sm text-gray-700\",\n                        children: \"per page\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700\",\n                        children: [\n                            \"Showing \",\n                            startItem,\n                            \" to \",\n                            endItem,\n                            \" of \",\n                            totalItems,\n                            \" results\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>onPageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                className: `px-3 py-2 text-sm font-medium rounded-md ${currentPage === 1 ? \"text-gray-400 cursor-not-allowed\" : \"text-gray-700 hover:bg-gray-100\"}`,\n                                children: \"‹\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            getVisiblePages().map((page, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                    children: page === \"...\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-2 text-sm text-gray-500\",\n                                        children: \"...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>onPageChange(page),\n                                        className: `px-3 py-2 text-sm font-medium rounded-md ${currentPage === page ? \"bg-blue-600 text-white\" : \"text-gray-700 hover:bg-gray-100\"}`,\n                                        children: page\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>onPageChange(currentPage + 1),\n                                disabled: currentPage === totalPages,\n                                className: `px-3 py-2 text-sm font-medium rounded-md ${currentPage === totalPages ? \"text-gray-400 cursor-not-allowed\" : \"text-gray-700 hover:bg-gray-100\"}`,\n                                children: \"›\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Pagination.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Pagination);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Pagination.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Stats.tsx":
/*!**********************************!*\
  !*** ./src/components/Stats.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Stats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Stats({ stats }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Instruments\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-semibold text-blue-600\",\n                        children: stats.instrumentCount\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Updates/sec\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-semibold text-green-600\",\n                        children: stats.updateRate\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Latency\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-semibold text-purple-600\",\n                        children: [\n                            stats.latency,\n                            \"ms\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: \"Last Update\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-semibold text-gray-600\",\n                        children: stats.lastUpdate\n                    }, void 0, false, {\n                        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\websocketdhan\\\\dhan-websocket-server\\\\watchlistnext\\\\src\\\\components\\\\Stats.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Stats.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "bufferutil":
/*!*****************************!*\
  !*** external "bufferutil" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("bufferutil");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "utf-8-validate":
/*!*********************************!*\
  !*** external "utf-8-validate" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("utf-8-validate");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cwebsocketdhan%5Cdhan-websocket-server%5Cwatchlistnext%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cwebsocketdhan%5Cdhan-websocket-server%5Cwatchlistnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();