"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Toolt<PERSON> } from "recharts";
import {
  ArrowUpIcon,
  ArrowDownIcon,
  TrendingUpIcon,
  TrendingDownIcon,
} from "lucide-react";
import { io } from "socket.io-client";
import indicesData from "../../data/indices-data.json";

interface IndexConstituent {
  companyName: string;
  symbol: string;
  weight: number;
  industry: string;
  series: string;
  isinCode: string;
  currentPrice?: number;
  change?: number;
  changePercent?: number;
}

interface IndexData {
  name: string;
  constituents: IndexConstituent[];
  totalWeight: number;
  indexValue: number;
  indexChange: number;
  indexChangePercent: number;
}

const IndexAnalysisPage: React.FC = () => {
  const [selectedIndex, setSelectedIndex] = useState<string>("BANKNIFTY");
  const [indexData, setIndexData] = useState<IndexData | null>(null);
  const [loading, setLoading] = useState(false);
  const [, setMarketData] = useState<Map<string, Record<string, unknown>>>(
    new Map()
  );

  // Load index data from database and merge with real market data
  const loadIndexData = async (indexKey: string) => {
    try {
      // First get the index instruments from database
      const instrumentsResponse = await fetch(
        `http://localhost:8080/api/websocket-instruments?action=by-index&index=${indexKey}`
      );
      if (!instrumentsResponse.ok) {
        throw new Error("Failed to fetch index instruments");
      }

      const instrumentsData = await instrumentsResponse.json();
      if (!instrumentsData.success) {
        throw new Error("Invalid instruments response");
      }

      // Get company details from database
      const symbols = instrumentsData.data.instruments
        .map((inst: Record<string, unknown>) => inst.symbol)
        .filter(Boolean);
      const companiesResponse = await fetch(
        "http://localhost:8080/api/companies",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ symbols }),
        }
      );

      let companies = [];
      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        companies = companiesData.success ? companiesData.data : [];
      }

      // Fetch real market data
      const marketResponse = await fetch("http://localhost:8080/api/data");
      let liveData = [];
      if (marketResponse.ok) {
        const marketData = await marketResponse.json();
        liveData = marketData.latestData || [];
      }

      // Create enriched constituents
      const enrichedConstituents = instrumentsData.data.instruments.map(
        (instrument: Record<string, unknown>) => {
          const company = companies.find(
            (comp: Record<string, unknown>) =>
              comp.nse_symbol === instrument.symbol
          );
          const liveStock = liveData.find(
            (stock: Record<string, unknown>) =>
              stock.ticker === instrument.symbol ||
              stock.securityId === instrument.SecurityId
          );

          return {
            companyName:
              company?.company_name ||
              instrument.companyName ||
              instrument.symbol,
            symbol:
              instrument.symbol ||
              `${instrument.ExchangeSegment}_${instrument.SecurityId}`,
            weight: Math.random() * 20 + 1, // Placeholder weight - would come from index definition
            industry:
              company?.industry_name || instrument.industry || "Unknown",
            sector: company?.sector_name || instrument.sector || "Unknown",
            series: "EQ",
            isinCode: company?.isin_no || "N/A",
            currentPrice: liveStock?.ltp || 0,
            change: liveStock?.change || 0,
            changePercent: liveStock?.changePercent || 0,
          };
        }
      );

      // Calculate total weight and normalize if needed
      const totalWeight = enrichedConstituents.reduce(
        (sum: number, constituent: Record<string, unknown>) =>
          sum + (constituent.weight as number),
        0
      );
      const normalizedConstituents = enrichedConstituents.map(
        (constituent: Record<string, unknown>) => ({
          ...constituent,
          weight: ((constituent.weight as number) / totalWeight) * 100,
        })
      );

      return {
        name: getIndexDisplayName(indexKey),
        indexValue: 51245.8, // Placeholder - would come from index calculation
        indexChange: -285.4,
        indexChangePercent: -0.55,
        totalWeight: 100,
        constituents: normalizedConstituents,
      };
    } catch (error) {
      console.error("Error loading index data:", error);
      // Fallback to static data
      const baseData = (indicesData as Record<string, unknown>)[indexKey];
      return baseData || null;
    }
  };

  const getIndexDisplayName = (indexKey: string): string => {
    const names: { [key: string]: string } = {
      BANKNIFTY: "Bank Nifty",
      NIFTYIT: "Nifty IT",
      NIFTYAUTO: "Nifty Auto",
      NIFTYPHARMA: "Nifty Pharma",
      NIFTYFMCG: "Nifty FMCG",
      NIFTYMETAL: "Nifty Metal",
      NIFTYREALTY: "Nifty Realty",
    };
    return names[indexKey] || indexKey;
  };

  const availableIndices = [
    { value: "BANKNIFTY", label: "Bank Nifty" },
    { value: "NIFTYIT", label: "Nifty IT" },
    { value: "NIFTYAUTO", label: "Nifty Auto" },
    { value: "NIFTYPHARMA", label: "Nifty Pharma" },
    { value: "NIFTYFMCG", label: "Nifty FMCG" },
    { value: "NIFTYMETAL", label: "Nifty Metal" },
    { value: "NIFTYREALTY", label: "Nifty Realty" },
  ];

  useEffect(() => {
    // Load index data with real market data
    const fetchData = async () => {
      setLoading(true);
      const data = await loadIndexData(selectedIndex);
      setIndexData(data);
      setLoading(false);
    };

    fetchData();

    // Setup websocket connection for real-time updates
    const socketInstance = io("http://localhost:8080", {
      transports: ["websocket", "polling"],
      upgrade: true,
      rememberUpgrade: false,
      timeout: 20000,
      forceNew: true,
    });

    socketInstance.on("connect", () => {
      console.log("Index Analysis: Connected to websocket server");
    });

    socketInstance.on("marketData", (data: Record<string, unknown>) => {
      setMarketData((prev) => {
        const newMap = new Map(prev);
        newMap.set(data.securityId || data.ticker, data);
        return newMap;
      });

      // Update index data with new market data
      setIndexData((prevIndex) => {
        if (!prevIndex) return prevIndex;

        const updatedConstituents = prevIndex.constituents.map(
          (constituent) => {
            if (
              constituent.symbol === data.ticker ||
              constituent.symbol === data.securityId
            ) {
              return {
                ...constituent,
                currentPrice: data.ltp || constituent.currentPrice,
                change: data.change || constituent.change,
                changePercent: data.changePercent || constituent.changePercent,
              };
            }
            return constituent;
          }
        );

        return {
          ...prevIndex,
          constituents: updatedConstituents,
        };
      });
    });

    return () => {
      socketInstance.disconnect();
    };
  }, [selectedIndex]);

  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884D8",
    "#82CA9D",
    "#FFC658",
    "#FF7C7C",
    "#8DD1E1",
    "#D084D0",
  ];

  const formatCurrency = (value: number) => {
    return `₹${value.toLocaleString("en-IN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const getTopGainers = () => {
    if (!indexData) return [];
    return indexData.constituents
      .filter((stock) => stock.changePercent && stock.changePercent > 0)
      .sort((a, b) => (b.changePercent || 0) - (a.changePercent || 0))
      .slice(0, 5);
  };

  const getTopLosers = () => {
    if (!indexData) return [];
    return indexData.constituents
      .filter((stock) => stock.changePercent && stock.changePercent < 0)
      .sort((a, b) => (a.changePercent || 0) - (b.changePercent || 0))
      .slice(0, 5);
  };

  const pieChartData =
    indexData?.constituents.map((constituent, index) => ({
      name: constituent.symbol,
      value: constituent.weight,
      color: COLORS[index % COLORS.length],
      fullName: constituent.companyName,
    })) || [];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading index data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Index Analysis
          </h1>

          {/* Index Selector */}
          <div className="flex items-center space-x-4 mb-6">
            <label className="text-sm font-medium text-gray-700">
              Select Index:
            </label>
            <select
              value={selectedIndex}
              onChange={(e) => setSelectedIndex(e.target.value)}
              className="bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {availableIndices.map((index) => (
                <option key={index.value} value={index.value}>
                  {index.label}
                </option>
              ))}
            </select>
          </div>

          {/* Index Overview */}
          {indexData && (
            <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {indexData.name}
                  </h2>
                  <p className="text-sm text-gray-600">
                    Real-time index composition and performance
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-gray-900">
                    {formatCurrency(indexData.indexValue)}
                  </div>
                  <div
                    className={`flex items-center justify-end text-lg font-semibold ${
                      indexData.indexChange >= 0
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {indexData.indexChange >= 0 ? (
                      <ArrowUpIcon className="w-5 h-5 mr-1" />
                    ) : (
                      <ArrowDownIcon className="w-5 h-5 mr-1" />
                    )}
                    {formatChange(
                      indexData.indexChange,
                      indexData.indexChangePercent
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Content Grid */}
        {indexData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Pie Chart - Index Composition */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Index Composition by Weight
              </h3>
              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(
                        value: number,
                        name: string,
                        props: Record<string, unknown>
                      ) => [
                        `${value.toFixed(2)}%`,
                        (props.payload as Record<string, unknown>).fullName,
                      ]}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Legend */}
              <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
                {pieChartData.slice(0, 10).map((entry, index) => (
                  <div key={index} className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: entry.color }}
                    ></div>
                    <span className="truncate">
                      {entry.name} ({entry.value.toFixed(1)}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Gainers & Losers */}
            <div className="space-y-6">
              {/* Top Gainers */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <TrendingUpIcon className="w-5 h-5 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Top Gainers
                  </h3>
                </div>
                <div className="space-y-3">
                  {getTopGainers().map((stock) => (
                    <div
                      key={stock.symbol}
                      className="flex items-center justify-between p-3 bg-green-50 rounded-lg"
                    >
                      <div>
                        <div className="font-medium text-gray-900">
                          {stock.symbol}
                        </div>
                        <div className="text-sm text-gray-600">
                          Weight: {stock.weight.toFixed(2)}%
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900">
                          {formatCurrency(stock.currentPrice || 0)}
                        </div>
                        <div className="text-sm font-medium text-green-600">
                          +{stock.changePercent?.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Top Losers */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <TrendingDownIcon className="w-5 h-5 text-red-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Top Losers
                  </h3>
                </div>
                <div className="space-y-3">
                  {getTopLosers().map((stock) => (
                    <div
                      key={stock.symbol}
                      className="flex items-center justify-between p-3 bg-red-50 rounded-lg"
                    >
                      <div>
                        <div className="font-medium text-gray-900">
                          {stock.symbol}
                        </div>
                        <div className="text-sm text-gray-600">
                          Weight: {stock.weight.toFixed(2)}%
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900">
                          {formatCurrency(stock.currentPrice || 0)}
                        </div>
                        <div className="text-sm font-medium text-red-600">
                          {stock.changePercent?.toFixed(2)}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Detailed Constituents Table */}
        {indexData && (
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900">
                Index Constituents
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Complete list of stocks in {indexData.name}
              </p>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Company
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Symbol
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Weight (%)
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Current Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Change
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ISIN Code
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {indexData.constituents.map((constituent, index) => (
                    <tr key={constituent.symbol} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {constituent.companyName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {constituent.industry}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {constituent.symbol}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900">
                            {constituent.weight.toFixed(2)}%
                          </div>
                          <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{
                                width: `${(constituent.weight / 30) * 100}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(constituent.currentPrice || 0)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div
                          className={`text-sm font-medium ${
                            (constituent.changePercent || 0) >= 0
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {constituent.change && constituent.changePercent
                            ? formatChange(
                                constituent.change,
                                constituent.changePercent
                              )
                            : "N/A"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {constituent.isinCode}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IndexAnalysisPage;
