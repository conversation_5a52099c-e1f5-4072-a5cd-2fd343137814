import React, { useEffect, useState } from "react";
import { MarketData as MarketDataType } from "../types";

interface MarketDataProps {
  symbol: string;
  data: MarketDataType;
}

const MarketData: React.FC<MarketDataProps> = ({ symbol, data }) => {
  const [priceChange, setPriceChange] = useState<number>(0);
  const [isPositive, setIsPositive] = useState<boolean>(true);

  useEffect(() => {
    if (data.change !== undefined) {
      setPriceChange(data.change);
      setIsPositive(data.change >= 0);
    }
  }, [data.change]);

  return (
    <div className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-semibold text-gray-800">{symbol}</h3>
        <span className="text-sm text-gray-500">{data.exchange}</span>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-600">Last Price</p>
          <p className="text-xl font-bold">{data.lastPrice.toFixed(2)}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Change</p>
          <p
            className={`text-xl font-bold ${
              isPositive ? "text-green-600" : "text-red-600"
            }`}
          >
            {priceChange.toFixed(2)} (
            {((priceChange / data.lastPrice) * 100).toFixed(2)}%)
          </p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Volume</p>
          <p className="text-lg font-semibold">
            {data.volume.toLocaleString()}
          </p>
        </div>

        <div>
          <p className="text-sm text-gray-600">High/Low</p>
          <p className="text-lg font-semibold">
            {data.high.toFixed(2)} / {data.low.toFixed(2)}
          </p>
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        Last updated: {new Date(data.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
};

export default MarketData;
