"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = require("dotenv");
const fs_1 = require("fs");
const path_1 = require("path");
const stats_1 = require("../lib/stats");
const ws_1 = __importDefault(require("ws"));
const express_1 = __importDefault(require("express"));
const multer_1 = __importDefault(require("multer"));
const cors_1 = __importDefault(require("cors"));
const error_1 = require("../middleware/error");
const xlsx = __importStar(require("xlsx"));
const database_1 = require("../services/database");
const websocket_subscription_1 = __importDefault(require("../services/websocket-subscription"));
// Load environment variables
(0, dotenv_1.config)();
// Create uploads directory if it doesn't exist
const uploadsDir = (0, path_1.join)(process.cwd(), "uploads");
if (!(0, fs_1.existsSync)(uploadsDir)) {
    (0, fs_1.mkdirSync)(uploadsDir, { recursive: true });
}
const MARKET_FEED_URL = "wss://api-feed.dhan.co";
const EXCHANGE_SEGMENTS = {
    IDX_I: 0,
    NSE_EQ: 1,
    NSE_FNO: 2,
    NSE_CURRENCY: 3,
    BSE_EQ: 4,
    MCX_COMM: 5,
    BSE_CURRENCY: 7,
    BSE_FNO: 8,
};
const SUBSCRIPTION_TYPES = {
    ticker: 15,
    quote: 17,
    full: 21,
};
class DhanMarketFeedServer {
    constructor() {
        // Batching for performance optimization
        this.updateBatch = [];
        this.batchTimeout = null;
        this.BATCH_SIZE = 50;
        this.BATCH_TIMEOUT = 100; // ms
        this.accessToken = process.env.ACCESS_TOKEN?.trim() || "";
        this.clientId = process.env.CLIENT_ID?.trim() || "";
        this.subscriptionType = process.env.SUBSCRIPTION_TYPE?.trim() || "quote";
        this.instruments = [];
        this.marketData = new Map();
        this.previousCloseData = new Map();
        this.wsConnections = [];
        this.isConnected = false;
        this.mockDataInterval = null;
        this.updateBatch = [];
        this.batchTimeout = null;
        // Express and Socket.IO setup
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.io = new socket_io_1.Server(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"],
            },
        });
        // Setup multer for file uploads
        this.upload = (0, multer_1.default)({
            dest: "uploads/",
            fileFilter: (_req, file, cb) => {
                const ext = (0, path_1.extname)(file.originalname).toLowerCase();
                if (ext === ".xlsx" || ext === ".xls") {
                    cb(null, true);
                }
                else {
                    cb(new Error("Only Excel files are allowed!"));
                }
            },
        });
        this.setupWebServer();
    }
    async loadInstrumentsFromDatabase() {
        try {
            console.log("🔄 Loading instruments from database...");
            // Test database connectivity first
            const db = database_1.DatabaseService.getInstance();
            try {
                const testStats = await db.getStats();
                console.log(`📊 Database connected - ${testStats.watchlists} watchlists available`);
            }
            catch (dbError) {
                console.warn("⚠️ Database connection failed, continuing without database features:", dbError);
            }
            // Get configuration from environment
            const instrumentConfig = process.env.INSTRUMENTS || "";
            const maxInstruments = parseInt(process.env.MAX_INSTRUMENTS || "25000"); // Support up to 5 connections × 5000 each
            const preferredExchange = process.env.PREFERRED_EXCHANGE || "NSE";
            const sectors = process.env.SECTORS?.split(",") || [];
            let instruments = [];
            // First, try to load indices from our processed index data
            try {
                const indexInstruments = this.loadIndexInstruments();
                if (indexInstruments.length > 0) {
                    console.log(`📊 Loaded ${indexInstruments.length} index instruments`);
                    instruments = [...instruments, ...indexInstruments];
                }
            }
            catch (indexError) {
                console.warn("⚠️ Could not load index instruments:", indexError);
            }
            if (instrumentConfig) {
                // Parse manual instrument configuration (backward compatibility)
                console.log("📋 Using manual instrument configuration from .env");
                const manualInstruments = this.parseManualInstruments(instrumentConfig);
                instruments = [...instruments, ...manualInstruments];
            }
            else {
                // Load ALL instruments from database (both NSE and BSE)
                console.log("🎯 Loading ALL instruments from database (NSE + BSE)");
                const wsInstruments = await websocket_subscription_1.default.getWebSocketInstruments({
                    exchanges: ["NSE", "BSE"], // Load both NSE and BSE instruments
                });
                const dbInstruments = this.convertWebSocketInstruments(wsInstruments);
                instruments = [...instruments, ...dbInstruments];
            }
            // Remove duplicates based on securityId
            const uniqueInstruments = instruments.filter((instrument, index, self) => index === self.findIndex(i => i.securityId === instrument.securityId));
            // Apply limit
            if (uniqueInstruments.length > maxInstruments) {
                console.log(`⚠️ Limiting instruments to ${maxInstruments} (found ${uniqueInstruments.length})`);
                instruments = uniqueInstruments.slice(0, maxInstruments);
            }
            else {
                instruments = uniqueInstruments;
            }
            this.instruments = instruments;
            console.log(`✅ Successfully loaded ${this.instruments.length} instruments from database`);
            if (this.instruments.length === 0) {
                console.log("⚠️ No instruments loaded from database. Using fallback defaults.");
                this.setDefaultInstruments();
            }
        }
        catch (error) {
            console.error("❌ Error loading instruments from database:", error);
            console.log("🔄 Falling back to Excel file loading...");
            try {
                this.loadInstrumentsFromExcel();
            }
            catch (excelError) {
                console.error("❌ Excel file loading also failed:", excelError);
                console.log("🔄 Using default instruments as final fallback...");
                this.setDefaultInstruments();
            }
        }
    }
    loadIndexInstruments() {
        try {
            const indexDataPath = (0, path_1.join)(process.cwd(), "indexdata_cleaned.json");
            if (!(0, fs_1.existsSync)(indexDataPath)) {
                console.log("📊 Index data file not found, skipping index instruments");
                return [];
            }
            const indexData = JSON.parse((0, fs_1.readFileSync)(indexDataPath, 'utf8'));
            const instruments = [];
            indexData.forEach((index) => {
                // Map exchange segment to our EXCHANGE_SEGMENTS
                let exchangeSegment = "IDX_I";
                // All indices use IDX_I segment
                if (index.SEM_SEGMENT === "IDX_I") {
                    exchangeSegment = "IDX_I";
                }
                instruments.push({
                    ticker: index.SEM_TRADING_SYMBOL || `${index.SEM_EXM_EXCH_ID}_${index.SEM_SMST_SECURITY_ID}`,
                    exchange: exchangeSegment,
                    exchangeCode: EXCHANGE_SEGMENTS[exchangeSegment],
                    securityId: index.SEM_SMST_SECURITY_ID.toString(),
                });
            });
            console.log(`📊 Loaded ${instruments.length} index instruments from indexdata_cleaned.json`);
            return instruments;
        }
        catch (error) {
            console.error("❌ Error loading index instruments:", error);
            return [];
        }
    }
    parseManualInstruments(instrumentConfig) {
        const instruments = [];
        const entries = instrumentConfig.split(",");
        for (const entry of entries) {
            const [exchange, securityId] = entry.trim().split(":");
            if (exchange && securityId) {
                const exchangeKey = exchange;
                if (exchangeKey in EXCHANGE_SEGMENTS) {
                    instruments.push({
                        ticker: `${exchange}_${securityId}`,
                        exchange: exchangeKey,
                        exchangeCode: EXCHANGE_SEGMENTS[exchangeKey],
                        securityId: securityId,
                    });
                }
            }
        }
        return instruments;
    }
    convertWebSocketInstruments(wsInstruments) {
        return wsInstruments.map((wsInstrument) => ({
            ticker: wsInstrument.symbol ||
                `${wsInstrument.ExchangeSegment}_${wsInstrument.SecurityId}`,
            exchange: wsInstrument.ExchangeSegment,
            exchangeCode: EXCHANGE_SEGMENTS[wsInstrument.ExchangeSegment],
            securityId: wsInstrument.SecurityId,
        }));
    }
    loadInstrumentsFromExcel() {
        try {
            // Try multiple possible locations for the Excel file
            const possiblePaths = [
                (0, path_1.join)(process.cwd(), "webdata.xlsx"),
                (0, path_1.join)(process.cwd(), "data", "webdata.xlsx"),
                (0, path_1.join)(process.cwd(), "..", "webdata.xlsx"),
                process.env.EXCEL_FILE_PATH || "",
            ].filter(Boolean);
            let excelPath = "";
            for (const path of possiblePaths) {
                if ((0, fs_1.existsSync)(path)) {
                    excelPath = path;
                    break;
                }
            }
            if (!excelPath) {
                console.log("Excel file not found in any of the expected locations. Using default instruments.");
                this.setDefaultInstruments();
                return;
            }
            console.log(`Loading instruments from: ${excelPath}`);
            // This check is redundant now, but keeping for safety
            if (!(0, fs_1.existsSync)(excelPath)) {
                this.setDefaultInstruments();
                return;
            }
            const workbook = xlsx.readFile(excelPath);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const data = xlsx.utils.sheet_to_json(worksheet);
            const instruments = [];
            let validCount = 0;
            let invalidCount = 0;
            data.forEach((row, index) => {
                try {
                    const ticker = row.Ticker || row.ticker || row.TICKER;
                    const securityId = row.SecurityId ||
                        row.securityId ||
                        row.SecurityID ||
                        row.SECURITYID;
                    const exchangeSegment = row.ExchangeSegment ||
                        row.exchangeSegment ||
                        row.Exchange ||
                        row.EXCHANGE;
                    if (!securityId || !exchangeSegment) {
                        invalidCount++;
                        return;
                    }
                    let standardExchange = exchangeSegment;
                    if (exchangeSegment === "1" || exchangeSegment === 1) {
                        standardExchange = "NSE_EQ";
                    }
                    else if (exchangeSegment === "2" || exchangeSegment === 2) {
                        standardExchange = "NSE_FNO";
                    }
                    else if (exchangeSegment === "3" || exchangeSegment === 3) {
                        standardExchange = "NSE_CURRENCY";
                    }
                    else if (exchangeSegment === "4" || exchangeSegment === 4) {
                        standardExchange = "BSE_EQ";
                    }
                    else if (exchangeSegment === "5" || exchangeSegment === 5) {
                        standardExchange = "MCX_COMM";
                    }
                    if (!(standardExchange in EXCHANGE_SEGMENTS)) {
                        invalidCount++;
                        return;
                    }
                    instruments.push({
                        ticker: ticker || `${standardExchange}_${securityId}`,
                        exchange: standardExchange,
                        exchangeCode: EXCHANGE_SEGMENTS[standardExchange],
                        securityId: securityId.toString(),
                    });
                    validCount++;
                }
                catch (error) {
                    invalidCount++;
                }
            });
            this.instruments = instruments;
            console.log(`Successfully loaded ${this.instruments.length} instruments (${validCount} valid, ${invalidCount} invalid)`);
            if (this.instruments.length === 0) {
                console.log("No valid instruments found in Excel file. Using default instruments.");
                this.setDefaultInstruments();
            }
        }
        catch (error) {
            console.error("Error loading instruments from Excel:", error);
            this.setDefaultInstruments();
        }
    }
    setDefaultInstruments() {
        this.instruments = [
            {
                ticker: "HDFCBANK",
                exchange: "NSE_EQ",
                exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                securityId: "1333",
            },
            {
                ticker: "RELIANCE",
                exchange: "NSE_EQ",
                exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                securityId: "3045",
            },
            {
                ticker: "INFY",
                exchange: "NSE_EQ",
                exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                securityId: "1594",
            },
            {
                ticker: "TCS",
                exchange: "NSE_EQ",
                exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                securityId: "11536",
            },
        ];
        console.log(`Using ${this.instruments.length} default instruments`);
    }
    validateConfig() {
        // Don't override instruments if they were already loaded successfully
        if (!this.instruments.length) {
            console.warn("⚠️ No instruments loaded - this should not happen after loading process");
            console.log("🔄 Using default instruments as final fallback...");
            this.setDefaultInstruments();
        }
        else {
            console.log(`✅ Instruments validated - ${this.instruments.length} instruments ready`);
        }
        if (!(this.subscriptionType in SUBSCRIPTION_TYPES)) {
            throw new Error(`Invalid subscription type: ${this.subscriptionType}`);
        }
        // Warn if credentials are missing but don't throw error (allow demo mode)
        if (!this.accessToken) {
            console.warn("ACCESS_TOKEN not provided - running in demo mode");
        }
        if (!this.clientId) {
            console.warn("CLIENT_ID not provided - running in demo mode");
        }
        console.log(`✅ Configuration validated - ${this.instruments.length} instruments loaded`);
    }
    setupWebServer() {
        // Enable CORS for all routes
        this.app.use((0, cors_1.default)({
            origin: [
                "http://localhost:3000",
                "http://localhost:3001",
                "http://localhost:3002",
            ],
            methods: ["GET", "POST"],
            credentials: true,
        }));
        // Parse JSON bodies
        this.app.use(express_1.default.json());
        // Serve static files
        this.app.use(express_1.default.static("public"));
        // API Routes
        this.app.get("/api/data", (req, res) => {
            const { search, exchange, limit = 50 } = req.query;
            let filteredData = Array.from(this.marketData.values());
            // Apply search filter
            if (search) {
                const searchTerm = search.toString().toLowerCase();
                filteredData = filteredData.filter((item) => item.ticker.toLowerCase().includes(searchTerm) ||
                    item.securityId.includes(searchTerm));
            }
            // Apply exchange filter
            if (exchange) {
                filteredData = filteredData.filter((item) => item.exchange === exchange);
            }
            // Apply limit
            const limitNum = parseInt(limit.toString()) || 50;
            filteredData = filteredData.slice(0, limitNum);
            res.json({
                connected: this.isConnected,
                instruments: this.instruments.length,
                subscriptionType: this.subscriptionType,
                latestData: filteredData,
                totalInstruments: this.instruments.length,
                activeInstruments: this.marketData.size,
            });
        });
        // Get specific instrument data
        this.app.get("/api/instrument/:securityId", (req, res) => {
            const { securityId } = req.params;
            const data = this.marketData.get(securityId);
            if (data) {
                res.json(data);
            }
            else {
                res.status(404).json({ error: "Instrument not found" });
            }
        });
        // Get indices data specifically
        this.app.get("/api/indices", (req, res) => {
            const { limit = 50 } = req.query;
            // Filter for index instruments (IDX_I exchange)
            const indexInstruments = this.instruments.filter(instrument => instrument.exchange === 'IDX_I');
            // Get market data for indices
            const indexData = indexInstruments.map(instrument => {
                const marketInfo = this.marketData.get(instrument.securityId) || {};
                // Generate mock data if no real data available
                let mockData = {};
                if (!marketInfo.ltp || marketInfo.ltp === 0) {
                    const basePrice = instrument.ticker === 'SENSEX' ? 82000 :
                        instrument.ticker === 'NIFTY' ? 25000 :
                            instrument.ticker === 'BANKNIFTY' ? 52000 :
                                instrument.ticker === 'FINNIFTY' ? 24000 :
                                    instrument.ticker.includes('BANK') ? 45000 :
                                        instrument.ticker.includes('IT') ? 35000 :
                                            Math.random() * 10000 + 5000;
                    const changePercent = (Math.random() - 0.5) * 4; // -2% to +2%
                    const change = (basePrice * changePercent) / 100;
                    mockData = {
                        ticker: instrument.ticker,
                        securityId: instrument.securityId,
                        exchange: instrument.exchange,
                        exchangeCode: instrument.exchangeCode,
                        ltp: parseFloat((basePrice + change).toFixed(2)),
                        change: parseFloat(change.toFixed(2)),
                        changePercent: parseFloat(changePercent.toFixed(2)),
                        volume: Math.floor(Math.random() * 1000000) + 100000,
                        high: parseFloat((basePrice + Math.abs(change) + Math.random() * 100).toFixed(2)),
                        low: parseFloat((basePrice - Math.abs(change) - Math.random() * 100).toFixed(2)),
                        open: parseFloat(basePrice.toFixed(2)),
                        close: parseFloat(basePrice.toFixed(2)),
                        timestamp: Date.now()
                    };
                }
                const finalData = marketInfo.ltp ? marketInfo : mockData;
                return {
                    ...instrument,
                    ...finalData
                };
            });
            // Apply limit
            const limitNum = parseInt(limit.toString()) || 50;
            const limitedData = indexData.slice(0, limitNum);
            res.json({
                connected: this.isConnected,
                indices: limitedData,
                totalIndices: indexInstruments.length,
                activeIndices: indexData.filter(item => item.ltp > 0).length
            });
        });
        // Get instruments list with pagination
        this.app.get("/api/instruments", (req, res) => {
            const { page = 1, limit = 50, search } = req.query;
            const pageNum = parseInt(page.toString()) || 1;
            const limitNum = parseInt(limit.toString()) || 50;
            const offset = (pageNum - 1) * limitNum;
            let filteredInstruments = this.instruments;
            if (search) {
                const searchTerm = search.toString().toLowerCase();
                filteredInstruments = this.instruments.filter((instrument) => instrument.ticker.toLowerCase().includes(searchTerm) ||
                    instrument.securityId.includes(searchTerm));
            }
            const paginatedInstruments = filteredInstruments.slice(offset, offset + limitNum);
            res.json({
                instruments: paginatedInstruments,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total: filteredInstruments.length,
                    pages: Math.ceil(filteredInstruments.length / limitNum),
                },
            });
        });
        // API endpoint to reload instruments from database
        this.app.post("/api/reload-instruments", async (req, res) => {
            try {
                console.log("🔄 Reloading instruments from database...");
                await this.loadInstrumentsFromDatabase();
                // Reconnect to market feed with new instruments
                this.wsConnections.forEach((ws) => {
                    if (ws.readyState === ws_1.default.OPEN) {
                        ws.close();
                    }
                });
                this.connectToMarketFeed();
                res.json({
                    success: true,
                    message: "Instruments reloaded successfully",
                    instrumentCount: this.instruments.length,
                });
            }
            catch (error) {
                console.error("Error reloading instruments:", error);
                res.status(500).json({
                    success: false,
                    error: "Failed to reload instruments",
                    message: error instanceof Error ? error.message : "Unknown error",
                });
            }
        });
        // API endpoint to get database statistics
        this.app.get("/api/database-stats", async (req, res) => {
            try {
                const stats = await database_1.DatabaseService.getInstance().getStats();
                const sectorDistribution = await database_1.DatabaseService.getInstance().getSectorDistribution();
                res.json({
                    success: true,
                    data: {
                        ...stats,
                        sectorDistribution: sectorDistribution.slice(0, 10), // Top 10 sectors
                    },
                });
            }
            catch (error) {
                console.error("Error fetching database stats:", error);
                res.status(500).json({
                    success: false,
                    error: "Failed to fetch database statistics",
                });
            }
        });
        // API endpoint to get available sectors for subscription
        this.app.get("/api/sectors", async (req, res) => {
            try {
                const sectors = await database_1.DatabaseService.getInstance().getSectors();
                res.json({
                    success: true,
                    data: sectors,
                });
            }
            catch (error) {
                console.error("Error fetching sectors:", error);
                res.status(500).json({
                    success: false,
                    error: "Failed to fetch sectors",
                });
            }
        });
        // API endpoint to get individual stock data by ticker
        this.app.get("/api/stock/:ticker", (req, res) => {
            const ticker = req.params.ticker?.toUpperCase();
            if (!ticker) {
                return res.status(400).json({ error: "Ticker parameter is required" });
            }
            // Find the stock by ticker
            const stock = Array.from(this.marketData.values()).find((item) => item.ticker?.toUpperCase() === ticker);
            if (!stock) {
                return res.status(404).json({ error: "Stock not found" });
            }
            // Enhance the stock data with additional calculated fields
            const enhancedStock = {
                ...stock,
                dayRange: `₹${stock.low?.toFixed(2) || "0.00"} - ₹${stock.high?.toFixed(2) || "0.00"}`,
                weekRange52: `₹${(stock.low * 0.8)?.toFixed(2) || "0.00"} - ₹${(stock.high * 1.2)?.toFixed(2) || "0.00"}`,
                marketCap: this.calculateMarketCap(stock.ltp, stock.volume),
                sector: this.getSector(stock.ticker),
                industry: this.getIndustry(stock.ticker),
            };
            res.json(enhancedStock);
        });
        // Add health check endpoint
        this.app.get("/health", async (req, res) => {
            try {
                const stats = await database_1.DatabaseService.getInstance().getStats();
                const distribution = await database_1.DatabaseService.getInstance().getSectorDistribution();
                res.json({
                    status: "healthy",
                    timestamp: new Date().toISOString(),
                    database: {
                        connected: true,
                        stats,
                    },
                    websocket: {
                        connected: this.isConnected,
                        subscriptions: 0, // placeholder
                        lastHeartbeat: new Date().toISOString(),
                    },
                    server: {
                        uptime: process.uptime(),
                        exchange: process.env.PREFERRED_EXCHANGE || "NSE",
                    },
                    sectorDistribution: distribution.slice(0, 10), // Top 10 sectors
                });
            }
            catch (error) {
                console.error("❌ Health check failed:", error);
                res.status(500).json({
                    status: "unhealthy",
                    error: error instanceof Error ? error.message : "Unknown error",
                });
            }
        });
        // Add sectors endpoint
        this.app.get("/sectors", async (req, res) => {
            try {
                const sectors = await database_1.DatabaseService.getInstance().getSectors();
                res.json({
                    success: true,
                    data: sectors,
                    count: sectors.length,
                });
            }
            catch (error) {
                console.error("❌ Error fetching sectors:", error);
                res.status(500).json({
                    success: false,
                    error: error instanceof Error ? error.message : "Failed to fetch sectors",
                });
            }
        });
        // Error handling
        this.app.use(error_1.notFoundHandler);
        this.app.use(error_1.errorHandler);
        // Socket.IO connection handler
        this.io.on("connection", (socket) => {
            console.log("Client connected");
            // Send initial data
            socket.emit("initialData", {
                instruments: this.instruments,
                liveData: Array.from(this.marketData.values()),
            });
            socket.on("disconnect", () => {
                console.log("Client disconnected");
            });
        });
    }
    async start(port) {
        // Load instruments first before starting server
        await this.loadInstrumentsFromDatabase();
        this.validateConfig();
        this.server.listen(port, () => {
            console.log(`Server running on port ${port}`);
            this.connectToMarketFeed();
        });
        // Graceful shutdown handling
        process.on("SIGINT", () => {
            console.log("Shutting down gracefully...");
            this.flushBatch(); // Ensure any pending batches are sent
            process.exit(0);
        });
    }
    connectToMarketFeed() {
        console.log("Attempting to connect to Dhan market feed...");
        // Skip connection if no access token (for testing)
        if (!this.accessToken) {
            console.log("No access token provided. Running in demo mode with mock data.");
            this.startMockDataGeneration();
            return;
        }
        // Split instruments into chunks of 5000 for multiple connections
        const maxInstrumentsPerConnection = 5000;
        const instrumentChunks = [];
        for (let i = 0; i < this.instruments.length; i += maxInstrumentsPerConnection) {
            instrumentChunks.push(this.instruments.slice(i, i + maxInstrumentsPerConnection));
        }
        console.log(`📡 Creating ${instrumentChunks.length} WebSocket connections for ${this.instruments.length} instruments`);
        // Close existing connections
        this.wsConnections.forEach((ws) => {
            if (ws.readyState === ws_1.default.OPEN) {
                ws.close();
            }
        });
        this.wsConnections = [];
        // Create multiple WebSocket connections
        instrumentChunks.forEach((chunk, index) => {
            this.createWebSocketConnection(chunk, index + 1);
        });
    }
    createWebSocketConnection(instruments, connectionId) {
        // Construct WebSocket URL with authentication parameters (Dhan API v2)
        const wsUrl = `${MARKET_FEED_URL}?version=2&token=${this.accessToken}&clientId=${this.clientId}&authType=2`;
        console.log(`🔗 Connection ${connectionId}: Connecting with ${instruments.length} instruments`);
        const ws = new ws_1.default(wsUrl);
        this.wsConnections.push(ws);
        ws.on("open", () => {
            console.log(`✅ Connection ${connectionId}: Connected to market feed`);
            if (connectionId === 1) {
                this.isConnected = true;
            }
            this.subscribeToInstrumentsForConnection(ws, instruments, connectionId);
        });
        ws.on("message", (data) => {
            try {
                // Dhan sends binary data, need to parse it properly
                if (data.length > 0) {
                    // Try to parse as JSON first (for text messages)
                    try {
                        const textData = data.toString("utf8");
                        const message = JSON.parse(textData);
                        // Only log important JSON messages
                        if (message.type === "error" || message.type === "status") {
                            console.log(`📄 Connection ${connectionId} JSON message:`, message);
                        }
                    }
                    catch (jsonError) {
                        // Binary data - this is likely the market data format
                        this.handleBinaryMarketData(data);
                    }
                }
            }
            catch (error) {
                console.error(`❌ Connection ${connectionId} error processing message:`, error);
            }
        });
        ws.on("close", () => {
            console.log(`🔌 Connection ${connectionId}: Disconnected from market feed`);
            if (connectionId === 1) {
                this.isConnected = false;
            }
            // Retry connection after 5 seconds
            setTimeout(() => this.createWebSocketConnection(instruments, connectionId), 5000);
        });
        ws.on("error", (error) => {
            console.error(`❌ Connection ${connectionId} WebSocket error:`, error.message);
            if (connectionId === 1) {
                this.isConnected = false;
                // Start mock data if primary connection fails
                console.log("Starting mock data generation due to connection failure...");
                this.startMockDataGeneration();
            }
        });
        // Start mock data generation after 10 seconds if no real data is received
        if (connectionId === 1) {
            setTimeout(() => {
                if (this.marketData.size === 0) {
                    console.log("No market data received after 10 seconds. Starting mock data generation...");
                    this.startMockDataGeneration();
                }
            }, 10000);
        }
    }
    getRealisticPrice(ticker) {
        // Realistic base prices for common stocks (approximate current market prices)
        const stockPrices = {
            HDFCBANK: 1650,
            RELIANCE: 2850,
            INFY: 1800,
            TCS: 4200,
            ICICIBANK: 1200,
            SBIN: 820,
            BHARTIARTL: 1150,
            ITC: 460,
            HINDUNILVR: 2650,
            KOTAKBANK: 1750,
            LT: 3600,
            ASIANPAINT: 3200,
            MARUTI: 11500,
            BAJFINANCE: 7200,
            HCLTECH: 1650,
            WIPRO: 550,
            ULTRACEMCO: 11000,
            TITAN: 3400,
            NESTLEIND: 2200,
            POWERGRID: 320,
        };
        // If ticker exists in our price map, use it; otherwise generate based on ticker hash
        if (stockPrices[ticker]) {
            return stockPrices[ticker];
        }
        // For unknown tickers, generate consistent price based on ticker name
        let hash = 0;
        for (let i = 0; i < ticker.length; i++) {
            hash = ((hash << 5) - hash + ticker.charCodeAt(i)) & 0xffffffff;
        }
        return Math.abs(hash % 5000) + 100; // Price between 100-5100
    }
    // Helper function to calculate market cap (mock calculation)
    calculateMarketCap(ltp, volume) {
        // This is a simplified calculation - in reality, you'd need shares outstanding
        const mockShares = volume * 1000; // Mock shares outstanding
        const marketCap = ltp * mockShares;
        if (marketCap >= ********000) {
            // 1000 Cr
            return `₹${(marketCap / ********).toFixed(2)} Cr`;
        }
        else if (marketCap >= ********0) {
            // 10 Cr
            return `₹${(marketCap / ********).toFixed(2)} Cr`;
        }
        else {
            return `₹${(marketCap / 100000).toFixed(2)} L`;
        }
    }
    // Helper function to get sector (mock data)
    getSector(ticker) {
        const sectorMap = {
            RELIANCE: "Energy",
            TCS: "Information Technology",
            INFY: "Information Technology",
            HDFCBANK: "Financial Services",
            ICICIBANK: "Financial Services",
            BHARTIARTL: "Telecommunication",
            ITC: "FMCG",
            SBIN: "Financial Services",
            LT: "Construction",
            HCLTECH: "Information Technology",
            MARUTI: "Automobile",
            BAJFINANCE: "Financial Services",
            ASIANPAINT: "Paints",
            NESTLEIND: "FMCG",
            KOTAKBANK: "Financial Services",
        };
        return sectorMap[ticker.toUpperCase()] || "Others";
    }
    // Helper function to get industry (mock data)
    getIndustry(ticker) {
        const industryMap = {
            RELIANCE: "Oil & Gas",
            TCS: "IT Services",
            INFY: "IT Services",
            HDFCBANK: "Private Bank",
            ICICIBANK: "Private Bank",
            BHARTIARTL: "Telecom Services",
            ITC: "Tobacco & FMCG",
            SBIN: "Public Bank",
            LT: "Engineering & Construction",
            HCLTECH: "IT Services",
            MARUTI: "Auto Manufacturer",
            BAJFINANCE: "NBFC",
            ASIANPAINT: "Paints & Coatings",
            NESTLEIND: "Food Products",
            KOTAKBANK: "Private Bank",
        };
        return industryMap[ticker.toUpperCase()] || "Others";
    }
    startMockDataGeneration() {
        // Prevent multiple instances
        if (this.mockDataInterval) {
            console.log("Mock data generation already running");
            return;
        }
        console.log("Starting realistic market data simulation...");
        this.isConnected = true;
        // Limit to top 50 instruments for better performance
        const activeInstruments = this.instruments.slice(0, 50);
        console.log(`Using ${activeInstruments.length} active instruments for real-time updates`);
        // Batch updates to reduce Socket.IO load
        const batchSize = 10;
        let currentBatch = 0;
        this.mockDataInterval = setInterval(() => {
            const batch = [];
            const startIndex = currentBatch * batchSize;
            const endIndex = Math.min(startIndex + batchSize, activeInstruments.length);
            for (let i = startIndex; i < endIndex; i++) {
                const instrument = activeInstruments[i];
                // Use the realistic price method for this specific instrument
                const basePrice = this.getRealisticPrice(instrument.ticker);
                // Get existing data to maintain price continuity
                const existingData = this.marketData.get(instrument.securityId);
                const previousPrice = existingData?.ltp || basePrice;
                // Generate realistic price movement (±1% max change per update)
                const maxChange = previousPrice * 0.01;
                const change = (Math.random() - 0.5) * maxChange * 2;
                const ltp = Math.max(1, previousPrice + change);
                // Calculate change from base price (previous day's close)
                const totalChange = ltp - basePrice;
                const marketData = {
                    ticker: instrument.ticker,
                    securityId: instrument.securityId,
                    exchange: instrument.exchange,
                    exchangeCode: instrument.exchangeCode,
                    ltp: Math.round(ltp * 100) / 100,
                    change: Math.round(totalChange * 100) / 100,
                    changePercent: Math.round((totalChange / basePrice) * 10000) / 100,
                    volume: Math.floor(Math.random() * 1000000) + 10000, // 10K to 1M volume
                    high: Math.round((Math.max(ltp, previousPrice) + Math.random() * (ltp * 0.01)) *
                        100) / 100,
                    low: Math.round((Math.min(ltp, previousPrice) - Math.random() * (ltp * 0.01)) *
                        100) / 100,
                    open: Math.round((basePrice + (Math.random() - 0.5) * (basePrice * 0.005)) * 100) / 100,
                    close: basePrice, // Previous day's close price
                    timestamp: Date.now(),
                };
                this.marketData.set(marketData.securityId, marketData);
                batch.push(marketData);
            }
            // Send batch update instead of individual updates
            if (batch.length > 0) {
                this.io.emit("marketDataBatch", batch);
            }
            // Move to next batch
            currentBatch =
                (currentBatch + 1) % Math.ceil(activeInstruments.length / batchSize);
            (0, stats_1.updateStats)(0, this.instruments);
        }, 2000); // 2 second intervals for better performance
    }
    handleBinaryMarketData(data) {
        try {
            if (data.length < 8)
                return; // Need at least 8 bytes for header
            // Parse binary message header (8 bytes)
            const responseCode = data.readUInt8(0);
            const exchangeSegment = data.readUInt8(3);
            const securityId = data.readUInt32LE(4);
            const exchangeName = this.getExchangeSegmentName(exchangeSegment);
            const timestamp = Date.now();
            const instrumentKey = `${exchangeSegment}-${securityId}`;
            // Find ticker for this instrument
            const instrument = this.instruments.find((inst) => inst.securityId == securityId.toString() &&
                EXCHANGE_SEGMENTS[inst.exchange] == exchangeSegment);
            const ticker = instrument ? instrument.ticker : `UNKNOWN_${securityId}`;
            let marketData = {
                timestamp: timestamp,
                responseCode: responseCode,
                exchangeSegment: exchangeSegment,
                exchangeName: exchangeName,
                securityId: securityId.toString(),
                dataLength: data.length,
                ticker: ticker,
            };
            // Parse different response types based on response code
            switch (responseCode) {
                case 2: // Ticker packet
                    if (data.length >= 16) {
                        marketData.type = "ticker";
                        marketData.ltp = data.readFloatLE(8);
                        marketData.ltt = data.readUInt32LE(12);
                    }
                    break;
                case 4: // Quote packet
                    if (data.length >= 50) {
                        marketData.type = "quote";
                        marketData.ltp = data.readFloatLE(8);
                        marketData.ltq = data.readUInt16LE(12);
                        marketData.ltt = data.readUInt32LE(14);
                        marketData.atp = data.readFloatLE(18);
                        marketData.volume = data.readUInt32LE(22);
                        marketData.totalSellQuantity = data.readUInt32LE(26);
                        marketData.totalBuyQuantity = data.readUInt32LE(30);
                        marketData.dayOpen = data.readFloatLE(34);
                        marketData.dayClose = data.readFloatLE(38);
                        marketData.dayHigh = data.readFloatLE(42);
                        marketData.dayLow = data.readFloatLE(46);
                    }
                    break;
                case 6: // Previous close packet
                    if (data.length >= 16) {
                        marketData.type = "previousClose";
                        marketData.previousClose = data.readFloatLE(8);
                        marketData.previousOI = data.readUInt32LE(12);
                        // Store previous close data for change calculation
                        this.previousCloseData.set(instrumentKey, {
                            previousClose: marketData.previousClose,
                            previousOI: marketData.previousOI,
                        });
                    }
                    break;
                case 8: // Full packet (with market depth)
                    if (data.length >= 162) {
                        marketData.type = "full";
                        marketData.ltp = data.readFloatLE(8);
                        marketData.ltq = data.readUInt16LE(12);
                        marketData.ltt = data.readUInt32LE(14);
                        marketData.atp = data.readFloatLE(18);
                        marketData.volume = data.readUInt32LE(22);
                        marketData.totalSellQuantity = data.readUInt32LE(26);
                        marketData.totalBuyQuantity = data.readUInt32LE(30);
                        marketData.openInterest = data.readUInt32LE(34);
                        marketData.dayOpen = data.readFloatLE(46);
                        marketData.dayClose = data.readFloatLE(50);
                        marketData.dayHigh = data.readFloatLE(54);
                        marketData.dayLow = data.readFloatLE(58);
                    }
                    break;
                default:
                    marketData.type = "unknown";
            }
            // Convert to our MarketData format
            const formattedData = {
                ticker: marketData.ticker,
                securityId: marketData.securityId,
                exchange: marketData.exchangeName,
                exchangeCode: marketData.exchangeSegment,
                ltp: marketData.ltp || 0,
                change: 0, // Will calculate below
                changePercent: 0, // Will calculate below
                volume: marketData.volume || 0,
                high: marketData.dayHigh || 0,
                low: marketData.dayLow || 0,
                open: marketData.dayOpen || 0,
                close: marketData.dayClose || 0,
                timestamp: marketData.timestamp,
            };
            // Calculate change using dayClose (previous day's close) from the market data
            if (formattedData.ltp && formattedData.close && formattedData.close > 0) {
                formattedData.change = formattedData.ltp - formattedData.close;
                formattedData.changePercent =
                    (formattedData.change / formattedData.close) * 100;
                // Round to 2 decimal places
                formattedData.change = Math.round(formattedData.change * 100) / 100;
                formattedData.changePercent =
                    Math.round(formattedData.changePercent * 100) / 100;
            }
            // Store and batch the data for efficient emission
            this.marketData.set(formattedData.securityId, formattedData);
            this.addToBatch(formattedData);
            // Data processed silently for better performance
        }
        catch (error) {
            console.error("❌ Error handling binary data:", error);
        }
    }
    // Optimized batching methods for better performance
    addToBatch(data) {
        this.updateBatch.push(data);
        // If batch is full, emit immediately
        if (this.updateBatch.length >= this.BATCH_SIZE) {
            this.flushBatch();
        }
        else {
            // Set timeout to flush batch if it's not full
            if (!this.batchTimeout) {
                this.batchTimeout = setTimeout(() => {
                    this.flushBatch();
                }, this.BATCH_TIMEOUT);
            }
        }
    }
    flushBatch() {
        if (this.updateBatch.length === 0)
            return;
        // Clear timeout
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
            this.batchTimeout = null;
        }
        // Emit batch or individual updates based on batch size
        if (this.updateBatch.length === 1) {
            // Single update - use legacy event for compatibility
            this.io.emit("marketData", this.updateBatch[0]);
        }
        else {
            // Batch update - use optimized event
            this.io.emit("marketDataBatch", this.updateBatch);
        }
        // Clear batch
        this.updateBatch = [];
    }
    getExchangeSegmentName(code) {
        const reverseMap = Object.fromEntries(Object.entries(EXCHANGE_SEGMENTS).map(([key, value]) => [value, key]));
        return reverseMap[code] || `Unknown(${code})`;
    }
    async subscribeToInstrumentsForConnection(ws, instruments, connectionId) {
        if (!ws || ws.readyState !== ws_1.default.OPEN) {
            console.log(`Connection ${connectionId}: WebSocket not ready for subscription`);
            return;
        }
        console.log(`📡 Connection ${connectionId}: Subscribing to ${instruments.length} instruments...`);
        // Get subscription type code
        const requestCode = SUBSCRIPTION_TYPES[this.subscriptionType] || SUBSCRIPTION_TYPES.quote;
        // Prepare instrument list in Dhan format - Use STRING values like working market-feed.js
        const instrumentList = instruments.map((instrument) => ({
            ExchangeSegment: instrument.exchange, // ✅ Use STRING like working market-feed.js
            SecurityId: instrument.securityId, // ✅ Use STRING like working market-feed.js
        }));
        // Split into chunks of 100 instruments (Dhan API limit)
        const chunkSize = 100;
        for (let i = 0; i < instrumentList.length; i += chunkSize) {
            const chunk = instrumentList.slice(i, i + chunkSize);
            const subscriptionMessage = {
                RequestCode: requestCode,
                InstrumentCount: chunk.length,
                InstrumentList: chunk,
            };
            // Only log first and last chunks to reduce spam
            const chunkNumber = Math.floor(i / chunkSize) + 1;
            const totalChunks = Math.ceil(instrumentList.length / chunkSize);
            if (chunkNumber === 1 || chunkNumber === totalChunks) {
                console.log(`📤 Connection ${connectionId}: Sending subscription chunk ${chunkNumber}/${totalChunks}`);
            }
            ws.send(JSON.stringify(subscriptionMessage));
            // Small delay between chunks to avoid overwhelming the server
            if (i + chunkSize < instrumentList.length) {
                await new Promise((resolve) => setTimeout(resolve, 200));
            }
        }
    }
    // Legacy method for backward compatibility
    async subscribeToInstruments() {
        // This method is no longer used but kept for compatibility
        console.log("⚠️ Legacy subscribeToInstruments called - using new multi-connection approach");
    }
}
const server = new DhanMarketFeedServer();
const port = Number(process.env.PORT) || 8080;
console.log(`Starting Dhan Market Feed Server on port ${port}`);
// Start server with async initialization
server.start(port).catch((error) => {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
});
