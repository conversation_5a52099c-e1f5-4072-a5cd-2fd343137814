import React, { useState, useEffect, useCallback } from "react";
import { io, Socket } from "socket.io-client";
import { MarketData, IndexData, KeyIndicesProps } from "@/types";
import indicesConfig from "../data/indices-config.json";

interface IndexConfig {
  name: string;
  securityId: string;
  exchangeSegment: string;
}

const KeyIndices: React.FC<KeyIndicesProps> = ({
  className = "",
  exchange = "NSE",
  limit = 200,
  refreshInterval = 2000,
}) => {
  const [indices, setIndices] = useState<IndexData[]>([]);
  const [selectedExchange, setSelectedExchange] = useState<string>(exchange);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [socket, setSocket] = useState<Socket | null>(null);

  // Load indices configuration from JSON
  const getIndicesConfig = useCallback((): IndexConfig[] => {
    return indicesConfig[selectedExchange as keyof typeof indicesConfig] || [];
  }, [selectedExchange]);

  // Fetch real market data from Next.js API route (optimized with error handling)
  const fetchAllMarketData = useCallback(async (): Promise<MarketData[]> => {
    try {
      setError(null);
      const response = await fetch(`/api/indices?limit=${limit}`, {
        headers: {
          "Cache-Control": "no-cache",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.indices || [];
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setError(`Failed to fetch market data: ${errorMessage}`);
      console.error("Error fetching market data:", error);
      return [];
    }
  }, [limit]);

  // Load indices data with optimized error handling
  const loadIndicesData = useCallback(async () => {
    try {
      setIsLoading(true);
      const config = getIndicesConfig();
      const allMarketData = await fetchAllMarketData();

      if (config.length === 0) {
        setError("No indices configuration found for selected exchange");
        return;
      }

      const indicesWithData: IndexData[] = config.map((indexConfig) => {
        const marketData = allMarketData.find(
          (item: MarketData) =>
            item.securityId === indexConfig.securityId ||
            item.securityId === indexConfig.securityId.toString()
        );

        if (marketData) {
          return {
            name: indexConfig.name,
            securityId: indexConfig.securityId,
            value: marketData.ltp || 0,
            change: marketData.change || 0,
            changePercent: marketData.changePercent || 0,
            lastUpdated: new Date().toLocaleString("en-IN", {
              day: "2-digit",
              month: "short",
              year: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            }),
          };
        } else {
          return {
            name: indexConfig.name,
            securityId: indexConfig.securityId,
            value: 0,
            change: 0,
            changePercent: 0,
            lastUpdated: "No data",
          };
        }
      });

      setIndices(indicesWithData);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setError(`Failed to load indices data: ${errorMessage}`);
      console.error("Error loading indices data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [getIndicesConfig, fetchAllMarketData]);

  // Optimized market data update handler (moved outside useEffect to avoid dependency issues)
  const updateMarketData = useCallback((data: MarketData) => {
    setIndices((prevIndices) =>
      prevIndices.map((index) => {
        if (index.securityId === data.securityId) {
          return {
            ...index,
            value: data.ltp || index.value,
            change: data.change || index.change,
            changePercent: data.changePercent || index.changePercent,
            lastUpdated: new Date().toLocaleString("en-IN", {
              day: "2-digit",
              month: "short",
              year: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            }),
          };
        }
        return index;
      })
    );
  }, []);

  // Setup WebSocket connection with optimized handling
  useEffect(() => {
    // Create optimized WebSocket connection (Fixed URL and options)
    const socketInstance = io("http://localhost:8080", {
      transports: ["websocket", "polling"],
      upgrade: true,
      rememberUpgrade: false,
      timeout: 20000,
      forceNew: false, // Reuse connection if possible
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    setSocket(socketInstance);

    socketInstance.on("connect", () => {
      console.log("KeyIndices: Connected to websocket server successfully");
      setError(null);
    });

    socketInstance.on("disconnect", (reason) => {
      console.log("KeyIndices: Disconnected from websocket server:", reason);
      if (reason === "io server disconnect") {
        // Server disconnected, try to reconnect
        socketInstance.connect();
      }
    });

    socketInstance.on("connect_error", (error) => {
      console.error("KeyIndices: Connection error:", error);
      setError(`WebSocket connection failed: ${error.message || error}`);
    });

    socketInstance.on("reconnect", (attemptNumber) => {
      console.log("KeyIndices: Reconnected after", attemptNumber, "attempts");
      setError(null);
    });

    socketInstance.on("reconnect_error", (error) => {
      console.error("KeyIndices: Reconnection error:", error);
      setError("Failed to reconnect to WebSocket");
    });

    // Handle real-time market data updates
    socketInstance.on("marketData", updateMarketData);

    // Handle batch market data updates (optimized)
    socketInstance.on("marketDataBatch", (batch: MarketData[]) => {
      setIndices((prevIndices) => {
        const updatedIndices = [...prevIndices];

        batch.forEach((data) => {
          const indexToUpdate = updatedIndices.findIndex(
            (index) => index.securityId === data.securityId
          );

          if (indexToUpdate !== -1) {
            updatedIndices[indexToUpdate] = {
              ...updatedIndices[indexToUpdate],
              value: data.ltp || updatedIndices[indexToUpdate].value,
              change: data.change || updatedIndices[indexToUpdate].change,
              changePercent:
                data.changePercent ||
                updatedIndices[indexToUpdate].changePercent,
              lastUpdated: new Date().toLocaleString("en-IN", {
                day: "2-digit",
                month: "short",
                year: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              }),
            };
          }
        });

        return updatedIndices;
      });
    });

    // Initial load
    loadIndicesData();

    return () => {
      socketInstance.off("marketData", updateMarketData);
      socketInstance.disconnect();
      setSocket(null);
    };
  }, [selectedExchange, loadIndicesData, updateMarketData]);

  // Periodic refresh fallback
  useEffect(() => {
    const interval = setInterval(() => {
      if (!socket?.connected) {
        loadIndicesData();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [socket, loadIndicesData, refreshInterval]);

  const formatValue = (value: number) => {
    return value.toLocaleString("en-IN", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  // Loading component
  if (isLoading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}
      >
        <div className="flex items-center justify-between p-3 border-b border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900">Key Indices</h3>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        </div>
        <div className="p-4 text-center text-gray-500">
          <div className="animate-pulse">Loading indices...</div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <h3 className="text-sm font-semibold text-gray-900">Key Indices</h3>
        <div className="flex items-center space-x-1">
          <select
            value={selectedExchange}
            onChange={(e) => setSelectedExchange(e.target.value)}
            className="bg-gray-800 text-white px-2 py-1 rounded text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={isLoading}
          >
            <option value="NSE">NSE</option>
            <option value="BSE">BSE</option>
          </select>
          <span className="text-xs text-gray-500">
            {socket?.connected ? (
              <span className="text-green-500">●</span>
            ) : (
              <span className="text-red-500">●</span>
            )}
          </span>
          <button
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            onClick={loadIndicesData}
            disabled={isLoading}
            title="Refresh data"
          >
            <svg
              className={`w-3 h-3 ${isLoading ? "animate-spin" : ""}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="p-3 bg-red-50 border-b border-red-200">
          <div className="flex items-center">
            <svg
              className="w-4 h-4 text-red-500 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Indices List */}
      <div className="divide-y divide-gray-50">
        {indices.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <div className="text-sm">No indices data available</div>
            <div className="text-xs mt-1">
              {selectedExchange} indices not found or no market data
            </div>
          </div>
        ) : (
          indices.map((index) => (
            <div
              key={`${index.name}-${index.securityId}`}
              className="px-3 py-2 hover:bg-gray-25 transition-colors"
            >
              <div className="flex items-center justify-between">
                {/* Left side - Index name and timestamp */}
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 text-sm truncate">
                    {index.name}
                  </h4>
                  <p className="text-xs text-gray-500">
                    {index.lastUpdated === "No data" ? (
                      <span className="text-red-500">No data</span>
                    ) : (
                      index.lastUpdated
                    )}
                  </p>
                </div>

                {/* Right side - Value and change */}
                <div className="text-right ml-2">
                  <div className="font-semibold text-gray-900 text-sm">
                    {index.value === 0 ? (
                      <span className="text-gray-400">--</span>
                    ) : (
                      formatValue(index.value)
                    )}
                  </div>
                  <div
                    className={`text-xs font-medium ${
                      index.value === 0
                        ? "text-gray-400"
                        : index.change >= 0
                          ? "text-green-600"
                          : "text-red-600"
                    }`}
                  >
                    {index.value === 0
                      ? "--"
                      : formatChange(index.change, index.changePercent)}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Footer */}
      <div className="px-3 py-2 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            {socket?.connected ? (
              <span className="text-green-600">● Live</span>
            ) : (
              <span className="text-red-500">● Offline</span>
            )}
          </span>
          <span>
            {indices.length} indices •{" "}
            {new Date().toLocaleTimeString("en-IN", {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </span>
        </div>
      </div>
    </div>
  );
};

export default KeyIndices;
