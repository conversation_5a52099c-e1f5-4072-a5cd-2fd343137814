import React, { useState, useEffect } from "react";
import { io } from "socket.io-client";
import indicesConfig from "../data/indices-config.json";

interface IndexConfig {
  name: string;
  securityId: string;
  exchangeSegment: string;
}

interface IndexData {
  name: string;
  securityId: string;
  value: number;
  change: number;
  changePercent: number;
  lastUpdated: string;
}

interface KeyIndicesProps {
  className?: string;
}

const KeyIndices: React.FC<KeyIndicesProps> = ({ className = "" }) => {
  const [indices, setIndices] = useState<IndexData[]>([]);
  const [selectedExchange, setSelectedExchange] = useState<string>("NSE");

  // Load indices configuration from JSON
  const getIndicesConfig = (): IndexConfig[] => {
    return indicesConfig[selectedExchange as keyof typeof indicesConfig] || [];
  };

  // Fetch real market data from port 8080 (your subscribed data)
  const fetchAllMarketData = async () => {
    try {
      const response = await fetch("http://localhost:8080/api/data");
      if (response.ok) {
        const data = await response.json();
        return data.latestData || [];
      }
    } catch (error) {
      console.error("Error fetching market data:", error);
    }
    return [];
  };

  useEffect(() => {
    // Load real market data from port 8080 (your subscribed data)
    const loadIndicesData = async () => {
      const config = getIndicesConfig();
      const allMarketData = await fetchAllMarketData();
      const indicesWithData: IndexData[] = [];

      for (const indexConfig of config) {
        const marketData = allMarketData.find(
          (item: Record<string, unknown>) =>
            item.securityId === indexConfig.securityId
        );

        if (marketData) {
          indicesWithData.push({
            name: indexConfig.name,
            securityId: indexConfig.securityId,
            value: marketData.ltp || 0,
            change: marketData.change || 0,
            changePercent: marketData.changePercent || 0,
            lastUpdated: new Date().toLocaleString("en-IN", {
              day: "2-digit",
              month: "short",
              year: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            }),
          });
        } else {
          // Fallback if no data available for this security ID
          indicesWithData.push({
            name: indexConfig.name,
            securityId: indexConfig.securityId,
            value: 0,
            change: 0,
            changePercent: 0,
            lastUpdated: "No data",
          });
        }
      }

      setIndices(indicesWithData);
    };

    // Setup websocket connection for real-time updates (same as other components)
    const socketInstance = io("http://localhost:8080", {
      transports: ["websocket", "polling"],
      upgrade: true,
      rememberUpgrade: false,
      timeout: 20000,
      forceNew: true,
    });

    socketInstance.on("connect", () => {
      console.log("KeyIndices: Connected to websocket server");
    });

    socketInstance.on("disconnect", () => {
      console.log("KeyIndices: Disconnected from websocket server");
    });

    // Handle real-time market data updates
    socketInstance.on("marketData", (data: Record<string, unknown>) => {
      setIndices((prevIndices) =>
        prevIndices.map((index) => {
          if (index.securityId === data.securityId) {
            return {
              ...index,
              value: (data.ltp as number) || index.value,
              change: (data.change as number) || index.change,
              changePercent:
                (data.changePercent as number) || index.changePercent,
              lastUpdated: new Date().toLocaleString("en-IN", {
                day: "2-digit",
                month: "short",
                year: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              }),
            };
          }
          return index;
        })
      );
    });

    // Handle batch market data updates
    socketInstance.on("marketDataBatch", (batch: Record<string, unknown>[]) => {
      setIndices((prevIndices) =>
        prevIndices.map((index) => {
          const relevantData = batch.find(
            (item) => item.securityId === index.securityId
          );
          if (relevantData) {
            return {
              ...index,
              value: (relevantData.ltp as number) || index.value,
              change: (relevantData.change as number) || index.change,
              changePercent:
                (relevantData.changePercent as number) || index.changePercent,
              lastUpdated: new Date().toLocaleString("en-IN", {
                day: "2-digit",
                month: "short",
                year: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              }),
            };
          }
          return index;
        })
      );
    });

    // Initial load
    loadIndicesData();

    return () => {
      socketInstance.disconnect();
    };
  }, [selectedExchange]);

  const formatValue = (value: number) => {
    return value.toLocaleString("en-IN", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <h3 className="text-sm font-semibold text-gray-900">Key Indices</h3>
        <div className="flex items-center space-x-1">
          <select
            value={selectedExchange}
            onChange={(e) => setSelectedExchange(e.target.value)}
            className="bg-gray-800 text-white px-2 py-1 rounded text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="NSE">NSE</option>
            <option value="BSE">BSE</option>
          </select>
          <span className="text-xs text-gray-500">FTD</span>
          <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
            <svg
              className="w-3 h-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Indices List */}
      <div className="divide-y divide-gray-50">
        {indices.map((index) => (
          <div
            key={index.name}
            className="px-3 py-2 hover:bg-gray-25 transition-colors"
          >
            <div className="flex items-center justify-between">
              {/* Left side - Index name and timestamp */}
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 text-sm truncate">
                  {index.name}
                </h4>
                <p className="text-xs text-gray-500">{index.lastUpdated}</p>
              </div>

              {/* Right side - Value and change */}
              <div className="text-right ml-2">
                <div className="font-semibold text-gray-900 text-sm">
                  {formatValue(index.value)}
                </div>
                <div
                  className={`text-xs font-medium ${
                    index.change >= 0 ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {formatChange(index.change, index.changePercent)}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="px-3 py-2 bg-gray-50 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          Live •{" "}
          {new Date().toLocaleTimeString("en-IN", {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </p>
      </div>
    </div>
  );
};

export default KeyIndices;
