import { Pool, QueryResult, QueryResultRow } from "pg";
import { appConfig } from "../config";
import { databaseError } from "../middleware/error";

export class DatabaseService {
  private static instance: DatabaseService;
  private pool: Pool;
  private isInitialized: boolean = false;

  private constructor() {
    // Skip database connection during build
    if (
      process.env.NODE_ENV === "development" &&
      process.env.NEXT_PHASE === "phase-production-build"
    ) {
      this.pool = {} as Pool; // Mock pool during build
      return;
    }

    this.pool = new Pool({
      connectionString: appConfig.database.url,
      ssl:
        process.env.NODE_ENV === "production"
          ? { rejectUnauthorized: false }
          : false,
    });
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public async initialize(): Promise<void> {
    // Skip initialization during build
    if (
      process.env.NODE_ENV === "development" &&
      process.env.NEXT_PHASE === "phase-production-build"
    ) {
      console.log("⏭️ Skipping database initialization during build");
      return;
    }

    if (this.isInitialized) return;

    try {
      const client = await this.pool.connect();
      client.release();

      // Create tables if they don't exist
      await this.query(`
        CREATE TABLE IF NOT EXISTS watchlists (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          type VARCHAR(50) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS sector_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          sector_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS industry_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          industry_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS sub_sector_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          sub_sector_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS micro_category_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          micro_category_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);

      this.isInitialized = true;
    } catch (error) {
      console.error("Error initializing database:", error);
      // Don't throw error during build time
      if (process.env.NODE_ENV !== "production") {
        console.warn(
          "⚠️ Database initialization failed, continuing without database features"
        );
      } else {
        throw error;
      }
    }
  }

  private async query<T extends QueryResultRow = QueryResultRow>(
    text: string,
    params?: any[]
  ): Promise<QueryResult<T>> {
    // Return mock result during build
    if (
      process.env.NODE_ENV === "development" &&
      process.env.NEXT_PHASE === "phase-production-build"
    ) {
      return {
        rows: [],
        rowCount: 0,
        command: "",
        oid: 0,
        fields: [],
      } as QueryResult<T>;
    }

    try {
      return await this.pool.query<T>(text, params);
    } catch (error) {
      console.error("Error executing query:", error);
      throw error;
    }
  }

  // Company lookup methods
  async getCompanyBySymbol(symbol: string): Promise<any> {
    try {
      const result = await this.query(
        "SELECT * FROM companies WHERE nse_symbol = $1 LIMIT 1",
        [symbol]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error("Error fetching company by symbol:", error);
      throw error;
    }
  }

  async getCompanyByISIN(isin: string): Promise<any> {
    try {
      const result = await this.query(
        "SELECT * FROM companies WHERE isin_no = $1 LIMIT 1",
        [isin]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error("Error fetching company by ISIN:", error);
      throw error;
    }
  }

  async getCompanyBySecurityId(securityId: string): Promise<any> {
    try {
      const result = await this.query(
        "SELECT * FROM companies WHERE bse_security_id = $1 OR nse_security_id = $1 LIMIT 1",
        [securityId]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error("Error fetching company by security ID:", error);
      throw error;
    }
  }

  async searchCompanies(filters: any): Promise<any> {
    try {
      const limit = filters.limit || 50;
      const offset = filters.offset || 0;
      let whereConditions = [];
      let queryParams = [];
      let paramIndex = 1;

      if (filters.sector) {
        whereConditions.push(`sector_name ILIKE $${paramIndex}`);
        queryParams.push(`%${filters.sector}%`);
        paramIndex++;
      }

      if (filters.industry) {
        whereConditions.push(`industry_name ILIKE $${paramIndex}`);
        queryParams.push(`%${filters.industry}%`);
        paramIndex++;
      }

      if (filters.search) {
        whereConditions.push(`(
          company_name ILIKE $${paramIndex} OR 
          nse_symbol ILIKE $${paramIndex} OR 
          bse_security_id ILIKE $${paramIndex} OR
          nse_security_id ILIKE $${paramIndex}
        )`);
        queryParams.push(`%${filters.search}%`);
        paramIndex++;
      }

      const whereClause =
        whereConditions.length > 0
          ? `WHERE ${whereConditions.join(" AND ")}`
          : "";

      const countQuery = `SELECT COUNT(*) FROM companies ${whereClause}`;
      const countResult = await this.query<{ count: string }>(
        countQuery,
        queryParams
      );
      const total = parseInt(countResult.rows[0]?.count || "0");

      const dataQuery = `
        SELECT * FROM companies 
        ${whereClause}
        ORDER BY company_name 
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      queryParams.push(limit, offset);

      const dataResult = await this.query(dataQuery, queryParams);

      return {
        companies: dataResult.rows,
        total,
        page: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error("Error searching companies:", error);
      throw error;
    }
  }

  async getSectors(): Promise<string[]> {
    try {
      const result = await this.query<{ sector_name: string }>(
        "SELECT DISTINCT sector_name FROM companies WHERE sector_name IS NOT NULL ORDER BY sector_name"
      );
      return result.rows.map((row) => row.sector_name);
    } catch (error) {
      console.error("Error fetching sectors:", error);
      throw error;
    }
  }

  async getIndustries(sector?: string): Promise<string[]> {
    try {
      let query =
        "SELECT DISTINCT industry_name FROM companies WHERE industry_name IS NOT NULL";
      let params: any[] = [];

      if (sector) {
        query += " AND sector_name = $1";
        params.push(sector);
      }

      query += " ORDER BY industry_name";

      const result = await this.query<{ industry_name: string }>(query, params);
      return result.rows.map((row) => row.industry_name);
    } catch (error) {
      console.error("Error fetching industries:", error);
      throw error;
    }
  }

  async getCompaniesBySector(sector: string): Promise<any[]> {
    try {
      const result = await this.query(
        "SELECT * FROM companies WHERE sector_name = $1 ORDER BY company_name",
        [sector]
      );
      return result.rows;
    } catch (error) {
      console.error("Error fetching companies by sector:", error);
      throw error;
    }
  }

  async getSectorDistribution(): Promise<any[]> {
    try {
      const result = await this.query<{ sector_name: string; count: string }>(
        "SELECT sector_name, COUNT(*) as count FROM companies WHERE sector_name IS NOT NULL GROUP BY sector_name ORDER BY count DESC"
      );
      return result.rows.map((row) => ({
        sector: row.sector_name,
        count: parseInt(row.count),
      }));
    } catch (error) {
      console.error("Error fetching sector distribution:", error);
      throw error;
    }
  }

  // Watchlist methods
  async createWatchlist(name: string, type: string): Promise<number> {
    const result = await this.query<{ id: number }>(
      "INSERT INTO watchlists (name, type) VALUES ($1, $2) RETURNING id",
      [name, type]
    );
    return result.rows[0]?.id || 0;
  }

  async getWatchlists(): Promise<{ id: number; name: string; type: string }[]> {
    const result = await this.query<{ id: number; name: string; type: string }>(
      "SELECT id, name, type FROM watchlists ORDER BY created_at DESC"
    );
    return result.rows;
  }

  async deleteWatchlist(id: number): Promise<void> {
    await this.query("DELETE FROM watchlists WHERE id = $1", [id]);
  }

  // Sector watchlist methods
  async addToSectorWatchlist(
    watchlistId: number,
    sectorName: string
  ): Promise<void> {
    await this.query(
      "INSERT INTO sector_watchlist (watchlist_id, sector_name) VALUES ($1, $2)",
      [watchlistId, sectorName]
    );
  }

  async getSectorWatchlist(watchlistId: number): Promise<string[]> {
    const result = await this.query<{ sector_name: string }>(
      "SELECT sector_name FROM sector_watchlist WHERE watchlist_id = $1",
      [watchlistId]
    );
    return result.rows.map((row) => row.sector_name);
  }

  // Industry watchlist methods
  async addToIndustryWatchlist(
    watchlistId: number,
    industryName: string
  ): Promise<void> {
    await this.query(
      "INSERT INTO industry_watchlist (watchlist_id, industry_name) VALUES ($1, $2)",
      [watchlistId, industryName]
    );
  }

  async getIndustryWatchlist(watchlistId: number): Promise<string[]> {
    const result = await this.query<{ industry_name: string }>(
      "SELECT industry_name FROM industry_watchlist WHERE watchlist_id = $1",
      [watchlistId]
    );
    return result.rows.map((row) => row.industry_name);
  }

  // Sub-sector watchlist methods
  async addToSubSectorWatchlist(
    watchlistId: number,
    subSectorName: string
  ): Promise<void> {
    await this.query(
      "INSERT INTO sub_sector_watchlist (watchlist_id, sub_sector_name) VALUES ($1, $2)",
      [watchlistId, subSectorName]
    );
  }

  async getSubSectorWatchlist(watchlistId: number): Promise<string[]> {
    const result = await this.query<{ sub_sector_name: string }>(
      "SELECT sub_sector_name FROM sub_sector_watchlist WHERE watchlist_id = $1",
      [watchlistId]
    );
    return result.rows.map((row) => row.sub_sector_name);
  }

  // Micro category watchlist methods
  async addToMicroCategoryWatchlist(
    watchlistId: number,
    microCategoryName: string
  ): Promise<void> {
    await this.query(
      "INSERT INTO micro_category_watchlist (watchlist_id, micro_category_name) VALUES ($1, $2)",
      [watchlistId, microCategoryName]
    );
  }

  async getMicroCategoryWatchlist(watchlistId: number): Promise<string[]> {
    const result = await this.query<{ micro_category_name: string }>(
      "SELECT micro_category_name FROM micro_category_watchlist WHERE watchlist_id = $1",
      [watchlistId]
    );
    return result.rows.map((row) => row.micro_category_name);
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.query("SELECT 1");
      return result.rowCount === 1;
    } catch (error) {
      console.error("Error checking database health:", error);
      return false;
    }
  }

  // Get database statistics
  async getStats() {
    try {
      const [
        watchlistsCount,
        sectorWatchlistCount,
        industryWatchlistCount,
        subSectorWatchlistCount,
        microCategoryWatchlistCount,
      ] = await Promise.all([
        this.query<{ count: string }>(
          "SELECT COUNT(*) as count FROM watchlists"
        ),
        this.query<{ count: string }>(
          "SELECT COUNT(*) as count FROM sector_watchlist"
        ),
        this.query<{ count: string }>(
          "SELECT COUNT(*) as count FROM industry_watchlist"
        ),
        this.query<{ count: string }>(
          "SELECT COUNT(*) as count FROM sub_sector_watchlist"
        ),
        this.query<{ count: string }>(
          "SELECT COUNT(*) as count FROM micro_category_watchlist"
        ),
      ]);

      return {
        watchlists: parseInt(watchlistsCount.rows[0]?.count || "0"),
        sectorWatchlist: parseInt(sectorWatchlistCount.rows[0]?.count || "0"),
        industryWatchlist: parseInt(
          industryWatchlistCount.rows[0]?.count || "0"
        ),
        subSectorWatchlist: parseInt(
          subSectorWatchlistCount.rows[0]?.count || "0"
        ),
        microCategoryWatchlist: parseInt(
          microCategoryWatchlistCount.rows[0]?.count || "0"
        ),
      };
    } catch (error) {
      console.error("Error getting database stats:", error);
      throw error;
    }
  }
}

// Don't initialize during build
export default DatabaseService;
