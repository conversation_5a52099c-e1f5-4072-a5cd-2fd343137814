import { Client, Pool } from "pg";

// Database connection pool for better performance
const pool = new Pool({
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Accept self-signed certificates for DigitalOcean
  },
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
});

export interface Company {
  id: number;
  face_value?: number;
  company_name: string;
  group_bse?: string;
  isin_no?: string;
  instrument?: string;
  sector_name?: string;
  industry_name?: string;
  sub_sector?: string;
  micro_category?: string;
  bse_security_id?: string;
  nse_security_id?: string;
  nse_symbol?: string;
  nse_series?: string;
  created_at: Date;
  updated_at: Date;
}

export interface CompanySearchFilters {
  sector?: string;
  industry?: string;
  nse_symbol?: string;
  isin_no?: string;
  search?: string; // General search across company name, symbol
  limit?: number;
  offset?: number;
}

export class DatabaseService {
  // Get company by NSE symbol
  static async getCompanyBySymbol(symbol: string): Promise<Company | null> {
    try {
      const query = "SELECT * FROM companies WHERE nse_symbol = $1 LIMIT 1";
      const result = await pool.query(query, [symbol]);
      return result.rows[0] || null;
    } catch (error) {
      console.error("Error fetching company by symbol:", error);
      throw error;
    }
  }

  // Get company by ISIN
  static async getCompanyByISIN(isin: string): Promise<Company | null> {
    try {
      const query = "SELECT * FROM companies WHERE isin_no = $1 LIMIT 1";
      const result = await pool.query(query, [isin]);
      return result.rows[0] || null;
    } catch (error) {
      console.error("Error fetching company by ISIN:", error);
      throw error;
    }
  }

  // Get companies by security ID (BSE or NSE)
  static async getCompanyBySecurityId(
    securityId: string
  ): Promise<Company | null> {
    try {
      const query = `
        SELECT * FROM companies 
        WHERE bse_security_id = $1 OR nse_security_id = $1 
        LIMIT 1
      `;
      const result = await pool.query(query, [securityId]);
      return result.rows[0] || null;
    } catch (error) {
      console.error("Error fetching company by security ID:", error);
      throw error;
    }
  }

  // Search companies with filters
  static async searchCompanies(filters: CompanySearchFilters): Promise<{
    companies: Company[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const limit = filters.limit || 50;
      const offset = filters.offset || 0;
      const page = Math.floor(offset / limit) + 1;

      let whereConditions: string[] = [];
      let queryParams: any[] = [];
      let paramIndex = 1;

      // Build WHERE conditions
      if (filters.sector) {
        whereConditions.push(`sector_name ILIKE $${paramIndex}`);
        queryParams.push(`%${filters.sector}%`);
        paramIndex++;
      }

      if (filters.industry) {
        whereConditions.push(`industry_name ILIKE $${paramIndex}`);
        queryParams.push(`%${filters.industry}%`);
        paramIndex++;
      }

      if (filters.nse_symbol) {
        whereConditions.push(`nse_symbol ILIKE $${paramIndex}`);
        queryParams.push(`%${filters.nse_symbol}%`);
        paramIndex++;
      }

      if (filters.isin_no) {
        whereConditions.push(`isin_no = $${paramIndex}`);
        queryParams.push(filters.isin_no);
        paramIndex++;
      }

      if (filters.search) {
        whereConditions.push(`(
          company_name ILIKE $${paramIndex} OR 
          nse_symbol ILIKE $${paramIndex} OR 
          bse_security_id ILIKE $${paramIndex} OR
          nse_security_id ILIKE $${paramIndex}
        )`);
        queryParams.push(`%${filters.search}%`);
        paramIndex++;
      }

      const whereClause =
        whereConditions.length > 0
          ? `WHERE ${whereConditions.join(" AND ")}`
          : "";

      // Get total count
      const countQuery = `SELECT COUNT(*) FROM companies ${whereClause}`;
      const countResult = await pool.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].count);

      // Get paginated results
      const dataQuery = `
        SELECT * FROM companies 
        ${whereClause}
        ORDER BY company_name ASC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      queryParams.push(limit, offset);

      const dataResult = await pool.query(dataQuery, queryParams);
      const totalPages = Math.ceil(total / limit);

      return {
        companies: dataResult.rows,
        total,
        page,
        totalPages,
      };
    } catch (error) {
      console.error("Error searching companies:", error);
      throw error;
    }
  }

  // Get all sectors
  static async getSectors(): Promise<string[]> {
    try {
      const query = `
        SELECT DISTINCT sector_name 
        FROM companies 
        WHERE sector_name IS NOT NULL 
        ORDER BY sector_name ASC
      `;
      const result = await pool.query(query);
      return result.rows.map((row) => row.sector_name);
    } catch (error) {
      console.error("Error fetching sectors:", error);
      throw error;
    }
  }

  // Get all industries
  static async getIndustries(sector?: string): Promise<string[]> {
    try {
      let query = `
        SELECT DISTINCT industry_name 
        FROM companies 
        WHERE industry_name IS NOT NULL
      `;
      const params: string[] = [];

      if (sector) {
        query += ` AND sector_name = $1`;
        params.push(sector);
      }

      query += ` ORDER BY industry_name ASC`;

      const result = await pool.query(query, params);
      return result.rows.map((row) => row.industry_name);
    } catch (error) {
      console.error("Error fetching industries:", error);
      throw error;
    }
  }

  // Get companies by sector for index analysis
  static async getCompaniesBySector(sector: string): Promise<Company[]> {
    try {
      const query = `
        SELECT * FROM companies 
        WHERE sector_name = $1 
        ORDER BY company_name ASC
      `;
      const result = await pool.query(query, [sector]);
      return result.rows;
    } catch (error) {
      console.error("Error fetching companies by sector:", error);
      throw error;
    }
  }

  // Get sector distribution for pie charts
  static async getSectorDistribution(): Promise<
    {
      sector: string;
      count: number;
      percentage: number;
    }[]
  > {
    try {
      const query = `
        SELECT 
          sector_name as sector,
          COUNT(*) as count,
          ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM companies)), 2) as percentage
        FROM companies 
        WHERE sector_name IS NOT NULL
        GROUP BY sector_name
        ORDER BY count DESC
      `;
      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      console.error("Error fetching sector distribution:", error);
      throw error;
    }
  }

  // Get database statistics
  static async getStats(): Promise<{
    totalCompanies: number;
    totalSectors: number;
    totalIndustries: number;
    companiesWithNSESymbol: number;
    companiesWithBSEId: number;
  }> {
    try {
      const queries = [
        "SELECT COUNT(*) as total_companies FROM companies",
        "SELECT COUNT(DISTINCT sector_name) as total_sectors FROM companies WHERE sector_name IS NOT NULL",
        "SELECT COUNT(DISTINCT industry_name) as total_industries FROM companies WHERE industry_name IS NOT NULL",
        "SELECT COUNT(*) as companies_with_nse FROM companies WHERE nse_symbol IS NOT NULL",
        "SELECT COUNT(*) as companies_with_bse FROM companies WHERE bse_security_id IS NOT NULL",
      ];

      const results = await Promise.all(
        queries.map((query) => pool.query(query))
      );

      return {
        totalCompanies: parseInt(results[0].rows[0].total_companies),
        totalSectors: parseInt(results[1].rows[0].total_sectors),
        totalIndustries: parseInt(results[2].rows[0].total_industries),
        companiesWithNSESymbol: parseInt(results[3].rows[0].companies_with_nse),
        companiesWithBSEId: parseInt(results[4].rows[0].companies_with_bse),
      };
    } catch (error) {
      console.error("Error fetching database stats:", error);
      throw error;
    }
  }

  // Close database connection pool
  static async close(): Promise<void> {
    await pool.end();
  }
}

export default DatabaseService;
