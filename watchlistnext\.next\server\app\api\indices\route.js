(()=>{var e={};e.id=697,e.ids=[697],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>a,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var n={};r.r(n),r.d(n,{GET:()=>i});var o=r(6559),s=r(8088),c=r(7719);async function i(e){try{let{searchParams:t}=new URL(e.url),r=`http://localhost:8080/api/indices?${t.toString()}`;console.log("Proxying request to:",r);let n=await fetch(r);if(!n.ok)throw Error(`WebSocket server responded with ${n.status}`);let o=await n.json();return new Response(JSON.stringify(o),{headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}catch(e){return console.error("Error proxying to WebSocket server:",e),new Response(JSON.stringify({connected:!1,indices:[{ticker:"SENSEX",exchange:"IDX_I",exchangeCode:0,securityId:"51",ltp:82150.45,change:245.3,changePercent:.3,volume:125e3,high:82350.2,low:81950.1,open:82e3,close:81905.15,timestamp:Date.now()},{ticker:"NIFTY",exchange:"IDX_I",exchangeCode:0,securityId:"13",ltp:25125.8,change:85.45,changePercent:.34,volume:89e3,high:25200.3,low:25050.2,open:25100,close:25040.35,timestamp:Date.now()},{ticker:"BANKNIFTY",exchange:"IDX_I",exchangeCode:0,securityId:"25",ltp:52345.6,change:-125.4,changePercent:-.24,volume:45e3,high:52500.8,low:52200.3,open:52471,close:52471,timestamp:Date.now()},{ticker:"FINNIFTY",exchange:"IDX_I",exchangeCode:0,securityId:"27",ltp:24125.3,change:45.2,changePercent:.19,volume:32e3,high:24200.5,low:24050.1,open:24080.1,close:24080.1,timestamp:Date.now()},{ticker:"BANKEX",exchange:"IDX_I",exchangeCode:0,securityId:"69",ltp:45230.75,change:-85.25,changePercent:-.19,volume:28e3,high:45400.2,low:45150.3,open:45316,close:45316,timestamp:Date.now()}],totalIndices:101,activeIndices:5}),{status:200,headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"}})}}let a=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/indices/route",pathname:"/api/indices",filename:"route",bundlePath:"app/api/indices/route"},resolvedPagePath:"D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\api\\indices\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:d}=a;function u(){return(0,c.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},6487:()=>{},6559:(e,t,r)=>{"use strict";e.exports=r(4870)},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[719],()=>r(6457));module.exports=n})();