import { NextResponse } from "next/server";
import WebSocketSubscriptionService from "../../../services/websocket-subscription";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    const action = searchParams.get("action");
    const sectors = searchParams.get("sectors")?.split(",");
    const symbols = searchParams.get("symbols")?.split(",");
    const exchange = (searchParams.get("exchange") as "NSE" | "BSE") || "NSE";
    const indexName = searchParams.get("index");
    const limit = parseInt(searchParams.get("limit") || "5000");

    switch (action) {
      case "by-sectors":
        if (!sectors || sectors.length === 0) {
          return new Response(
            JSON.stringify({
              success: false,
              error: "Sectors parameter is required",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
        }

        const sectorInstruments =
          await WebSocketSubscriptionService.getInstrumentsBySectors(
            sectors,
            exchange
          );
        const sectorConnections =
          WebSocketSubscriptionService.splitIntoConnections(sectorInstruments);

        return NextResponse.json({
          success: true,
          data: {
            instruments: sectorInstruments,
            connections: sectorConnections,
            totalInstruments: sectorInstruments.length,
            totalConnections: sectorConnections.length,
          },
        });

      case "by-index":
        if (!indexName) {
          return new Response(
            JSON.stringify({
              success: false,
              error: "Index name is required",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
        }

        const indexInstruments =
          await WebSocketSubscriptionService.getPredefinedIndexInstruments(
            indexName
          );

        return NextResponse.json({
          success: true,
          data: {
            index: indexName,
            instruments: indexInstruments,
            totalInstruments: indexInstruments.length,
            subscriptionMessage:
              WebSocketSubscriptionService.generateSubscriptionMessage(
                indexInstruments
              ),
          },
        });

      case "by-symbols":
        if (!symbols || symbols.length === 0) {
          return new Response(
            JSON.stringify({
              success: false,
              error: "Symbols parameter is required",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
        }

        const symbolInstruments =
          await WebSocketSubscriptionService.getInstrumentsForIndex(
            symbols,
            exchange
          );

        return NextResponse.json({
          success: true,
          data: {
            instruments: symbolInstruments,
            totalInstruments: symbolInstruments.length,
            subscriptionMessage:
              WebSocketSubscriptionService.generateSubscriptionMessage(
                symbolInstruments
              ),
          },
        });

      case "sector-distribution":
        const distribution =
          await WebSocketSubscriptionService.getSectorDistribution();

        return NextResponse.json({
          success: true,
          data: distribution,
        });

      case "all":
      default:
        const filters = {
          sectors: sectors,
          exchanges: exchange ? [exchange] : undefined,
          limit: limit,
        };

        const allInstruments =
          await WebSocketSubscriptionService.getWebSocketInstruments(filters);
        const allConnections =
          WebSocketSubscriptionService.splitIntoConnections(allInstruments);

        return NextResponse.json({
          success: true,
          data: {
            instruments: allInstruments,
            connections: allConnections,
            totalInstruments: allInstruments.length,
            totalConnections: allConnections.length,
            maxInstrumentsPerConnection: 5000,
            maxConnections: 5,
          },
        });
    }
  } catch (error) {
    console.error("WebSocket instruments API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { symbols, sectors, exchange = "NSE", requestCode = 17 } = body;

    let instruments = [];

    if (symbols && Array.isArray(symbols)) {
      instruments = await WebSocketSubscriptionService.getInstrumentsForIndex(
        symbols,
        exchange
      );
    } else if (sectors && Array.isArray(sectors)) {
      instruments = await WebSocketSubscriptionService.getInstrumentsBySectors(
        sectors,
        exchange
      );
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Either symbols or sectors array is required",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const connections =
      WebSocketSubscriptionService.splitIntoConnections(instruments);
    const subscriptionMessages = connections.map((conn) =>
      WebSocketSubscriptionService.generateSubscriptionMessage(
        conn.instruments,
        requestCode
      )
    );

    return NextResponse.json({
      success: true,
      data: {
        instruments,
        connections,
        subscriptionMessages,
        totalInstruments: instruments.length,
        totalConnections: connections.length,
      },
    });
  } catch (error) {
    console.error("WebSocket instruments POST API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
