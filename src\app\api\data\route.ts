import { getStats } from "@/lib/stats";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // If no parameters, return local stats
    if (searchParams.toString() === "") {
      const stats = getStats();
      return new Response(JSON.stringify(stats), {
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // Otherwise, proxy to the WebSocket server (Fixed port and endpoint)
    const wsServerUrl = `http://localhost:8080/api/data?${searchParams.toString()}`;

    const response = await fetch(wsServerUrl);

    if (!response.ok) {
      throw new Error(`WebSocket server responded with ${response.status}`);
    }

    const data = await response.json();
    return new Response(JSON.stringify(data), {
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Error proxying to WebSocket server:", error);
    return new Response(
      JSON.stringify({
        error: "Failed to fetch market data",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }
}
