(()=>{var e={};e.id=309,e.ids=[309],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2054:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>u});var o={};r.r(o),r.d(o,{GET:()=>s});var i=r(6559),n=r(8088),a=r(7719);async function s(e,{params:t}){try{let e=(await t).ticker;if(!e)return new Response(JSON.stringify({error:"Ticker parameter is required"}),{status:400,headers:{"Content-Type":"application/json"}});let r=await fetch("http://localhost:8080/api/data");if(!r.ok)throw Error("Failed to fetch market data");let o=await r.json(),i=o.latestData?.find(t=>t.ticker?.toLowerCase()===e.toLowerCase());if(!i)return new Response(JSON.stringify({error:"Stock not found"}),{status:404,headers:{"Content-Type":"application/json"}});let n={...i,dayRange:`₹${i.low?.toFixed(2)||"0.00"} - ₹${i.high?.toFixed(2)||"0.00"}`,weekRange52:`₹${(.8*i.low)?.toFixed(2)||"0.00"} - ₹${(1.2*i.high)?.toFixed(2)||"0.00"}`,marketCap:function(e,t){let r=1e3*t*e;return r>=1e10||r>=1e8?`₹${(r/1e7).toFixed(2)} Cr`:`₹${(r/1e5).toFixed(2)} L`}(i.ltp,i.volume),sector:{RELIANCE:"Energy",TCS:"Information Technology",INFY:"Information Technology",HDFCBANK:"Financial Services",ICICIBANK:"Financial Services",BHARTIARTL:"Telecommunication",ITC:"FMCG",SBIN:"Financial Services",LT:"Construction",HCLTECH:"Information Technology",MARUTI:"Automobile",BAJFINANCE:"Financial Services",ASIANPAINT:"Paints",NESTLEIND:"FMCG",KOTAKBANK:"Financial Services"}[i.ticker.toUpperCase()]||"Others",industry:{RELIANCE:"Oil & Gas",TCS:"IT Services",INFY:"IT Services",HDFCBANK:"Private Bank",ICICIBANK:"Private Bank",BHARTIARTL:"Telecom Services",ITC:"Tobacco & FMCG",SBIN:"Public Bank",LT:"Engineering & Construction",HCLTECH:"IT Services",MARUTI:"Auto Manufacturer",BAJFINANCE:"NBFC",ASIANPAINT:"Paints & Coatings",NESTLEIND:"Food Products",KOTAKBANK:"Private Bank"}[i.ticker.toUpperCase()]||"Others"};return new Response(JSON.stringify(n),{headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Error fetching stock detail:",e),new Response(JSON.stringify({error:"Internal server error"}),{status:500,headers:{"Content-Type":"application/json"}})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/stock/[ticker]/route",pathname:"/api/stock/[ticker]",filename:"route",bundlePath:"app/api/stock/[ticker]/route"},resolvedPagePath:"D:\\websocketdhan\\dhan-websocket-server\\watchlistnext\\src\\app\\api\\stock\\[ticker]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:u,serverHooks:d}=c;function l(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:u})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},6559:(e,t,r)=>{"use strict";e.exports=r(4870)},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[719],()=>r(2054));module.exports=o})();