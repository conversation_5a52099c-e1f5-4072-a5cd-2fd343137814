"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[161],{2664:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isLocalURL",{enumerable:!0,get:function(){return i}});let s=r(9991),n=r(7102);function i(t){if(!(0,s.isAbsoluteUrl)(t))return!0;try{let e=(0,s.getLocationOrigin)(),r=new URL(t,e);return r.origin===e&&(0,n.hasBasePath)(r.pathname)}catch(t){return!1}}},2757:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{formatUrl:function(){return i},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let s=r(6966)._(r(8859)),n=/https?|ftp|gopher|file/;function i(t){let{auth:e,hostname:r}=t,i=t.protocol||"",o=t.pathname||"",a=t.hash||"",h=t.query||"",c=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?c=e+t.host:r&&(c=e+(~r.indexOf(":")?"["+r+"]":r),t.port&&(c+=":"+t.port)),h&&"object"==typeof h&&(h=String(s.urlQueryToSearchParams(h)));let u=t.search||h&&"?"+h||"";return i&&!i.endsWith(":")&&(i+=":"),t.slashes||(!i||n.test(i))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),a&&"#"!==a[0]&&(a="#"+a),u&&"?"!==u[0]&&(u="?"+u),""+i+c+(o=o.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return i(t)}},3180:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"errorOnce",{enumerable:!0,get:function(){return r}});let r=t=>{}},4298:(t,e,r)=>{let s,n;r.d(e,{io:()=>tT});var i,o={};r.r(o),r.d(o,{Decoder:()=>tm,Encoder:()=>ty,PacketType:()=>i,protocol:()=>td});let a=Object.create(null);a.open="0",a.close="1",a.ping="2",a.pong="3",a.message="4",a.upgrade="5",a.noop="6";let h=Object.create(null);Object.keys(a).forEach(t=>{h[a[t]]=t});let c={type:"error",data:"parser error"},u="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),l="function"==typeof ArrayBuffer,p=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,f=({type:t,data:e},r,s)=>{if(u&&e instanceof Blob)if(r)return s(e);else return d(e,s);if(l&&(e instanceof ArrayBuffer||p(e)))if(r)return s(e);else return d(new Blob([e]),s);return s(a[t]+(e||""))},d=(t,e)=>{let r=new FileReader;return r.onload=function(){e("b"+(r.result.split(",")[1]||""))},r.readAsDataURL(t)};function y(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}let g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",m="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let t=0;t<g.length;t++)m[g.charCodeAt(t)]=t;let _=t=>{let e=.75*t.length,r=t.length,s,n=0,i,o,a,h;"="===t[t.length-1]&&(e--,"="===t[t.length-2]&&e--);let c=new ArrayBuffer(e),u=new Uint8Array(c);for(s=0;s<r;s+=4)i=m[t.charCodeAt(s)],o=m[t.charCodeAt(s+1)],a=m[t.charCodeAt(s+2)],h=m[t.charCodeAt(s+3)],u[n++]=i<<2|o>>4,u[n++]=(15&o)<<4|a>>2,u[n++]=(3&a)<<6|63&h;return c},b="function"==typeof ArrayBuffer,v=(t,e)=>{if("string"!=typeof t)return{type:"message",data:w(t,e)};let r=t.charAt(0);return"b"===r?{type:"message",data:k(t.substring(1),e)}:h[r]?t.length>1?{type:h[r],data:t.substring(1)}:{type:h[r]}:c},k=(t,e)=>b?w(_(t),e):{base64:!0,data:t},w=(t,e)=>"blob"===e?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer,E=(t,e)=>{let r=t.length,s=Array(r),n=0;t.forEach((t,i)=>{f(t,!1,t=>{s[i]=t,++n===r&&e(s.join("\x1e"))})})},O=(t,e)=>{let r=t.split("\x1e"),s=[];for(let t=0;t<r.length;t++){let n=v(r[t],e);if(s.push(n),"error"===n.type)break}return s};function T(t){return t.reduce((t,e)=>t+e.length,0)}function A(t,e){if(t[0].length===e)return t.shift();let r=new Uint8Array(e),s=0;for(let n=0;n<e;n++)r[n]=t[0][s++],s===t[0].length&&(t.shift(),s=0);return t.length&&s<t[0].length&&(t[0]=t[0].slice(s)),r}function R(t){if(t){var e=t;for(var r in R.prototype)e[r]=R.prototype[r];return e}}R.prototype.on=R.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},R.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},R.prototype.off=R.prototype.removeListener=R.prototype.removeAllListeners=R.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,s=this._callbacks["$"+t];if(!s)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var n=0;n<s.length;n++)if((r=s[n])===e||r.fn===e){s.splice(n,1);break}return 0===s.length&&delete this._callbacks["$"+t],this},R.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=Array(arguments.length-1),r=this._callbacks["$"+t],s=1;s<arguments.length;s++)e[s-1]=arguments[s];if(r){r=r.slice(0);for(var s=0,n=r.length;s<n;++s)r[s].apply(this,e)}return this},R.prototype.emitReserved=R.prototype.emit,R.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},R.prototype.hasListeners=function(t){return!!this.listeners(t).length};let C="function"==typeof Promise&&"function"==typeof Promise.resolve?t=>Promise.resolve().then(t):(t,e)=>e(t,0),x="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function S(t,...e){return e.reduce((e,r)=>(t.hasOwnProperty(r)&&(e[r]=t[r]),e),{})}let N=x.setTimeout,B=x.clearTimeout;function L(t,e){e.useNativeTimers?(t.setTimeoutFn=N.bind(x),t.clearTimeoutFn=B.bind(x)):(t.setTimeoutFn=x.setTimeout.bind(x),t.clearTimeoutFn=x.clearTimeout.bind(x))}function P(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class j extends Error{constructor(t,e,r){super(t),this.description=e,this.context=r,this.type="TransportError"}}class q extends R{constructor(t){super(),this.writable=!1,L(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,e,r){return super.emitReserved("error",new j(t,e,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(t){"open"===this.readyState&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){let e=v(t,this.socket.binaryType);this.onPacket(e)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,e={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(e)}_hostname(){let t=this.opts.hostname;return -1===t.indexOf(":")?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(t){let e=function(t){let e="";for(let r in t)t.hasOwnProperty(r)&&(e.length&&(e+="&"),e+=encodeURIComponent(r)+"="+encodeURIComponent(t[r]));return e}(t);return e.length?"?"+e:""}}class U extends q{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";let e=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let t=0;this._polling&&(t++,this.once("pollComplete",function(){--t||e()})),this.writable||(t++,this.once("drain",function(){--t||e()}))}else e()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){O(t,this.socket.binaryType).forEach(t=>{if("opening"===this.readyState&&"open"===t.type&&this.onOpen(),"close"===t.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(t)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){let t=()=>{this.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}write(t){this.writable=!1,E(t,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let t=this.opts.secure?"https":"http",e=this.query||{};return!1!==this.opts.timestampRequests&&(e[this.opts.timestampParam]=P()),this.supportsBinary||e.sid||(e.b64=1),this.createUri(t,e)}}let I=!1;try{I="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}let D=I;function M(){}class F extends U{constructor(t){if(super(t),"undefined"!=typeof location){let e="https:"===location.protocol,r=location.port;r||(r=e?"443":"80"),this.xd="undefined"!=typeof location&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,e){let r=this.request({method:"POST",data:t});r.on("success",e),r.on("error",(t,e)=>{this.onError("xhr post error",t,e)})}doPoll(){let t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(t,e)=>{this.onError("xhr poll error",t,e)}),this.pollXhr=t}}class V extends R{constructor(t,e,r){super(),this.createRequest=t,L(this,r),this._opts=r,this._method=r.method||"GET",this._uri=e,this._data=void 0!==r.data?r.data:null,this._create()}_create(){var t;let e=S(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this._opts.xd;let r=this._xhr=this.createRequest(e);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let t in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(t)&&r.setRequestHeader(t,this._opts.extraHeaders[t])}catch(t){}if("POST"===this._method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{r.setRequestHeader("Accept","*/*")}catch(t){}null==(t=this._opts.cookieJar)||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var t;3===r.readyState&&(null==(t=this._opts.cookieJar)||t.parseCookies(r.getResponseHeader("set-cookie"))),4===r.readyState&&(200===r.status||1223===r.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof r.status?r.status:0)},0))},r.send(this._data)}catch(t){this.setTimeoutFn(()=>{this._onError(t)},0);return}"undefined"!=typeof document&&(this._index=V.requestsCount++,V.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=M,t)try{this._xhr.abort()}catch(t){}"undefined"!=typeof document&&delete V.requests[this._index],this._xhr=null}}_onLoad(){let t=this._xhr.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function K(){for(let t in V.requests)V.requests.hasOwnProperty(t)&&V.requests[t].abort()}V.requestsCount=0,V.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",K):"function"==typeof addEventListener&&addEventListener("onpagehide"in x?"pagehide":"unload",K,!1));let H=function(){let t=z({xdomain:!1});return t&&null!==t.responseType}();class W extends F{constructor(t){super(t);let e=t&&t.forceBase64;this.supportsBinary=H&&!e}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new V(z,this.uri(),t)}}function z(t){let e=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!e||D))return new XMLHttpRequest}catch(t){}if(!e)try{return new x[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(t){}}let Y="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class J extends q{get name(){return"websocket"}doOpen(){let t=this.uri(),e=this.opts.protocols,r=Y?{}:S(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,e,r)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let r=t[e],s=e===t.length-1;f(r,this.supportsBinary,t=>{try{this.doWrite(r,t)}catch(t){}s&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let t=this.opts.secure?"wss":"ws",e=this.query||{};return this.opts.timestampRequests&&(e[this.opts.timestampParam]=P()),this.supportsBinary||(e.b64=1),this.createUri(t,e)}}let Q=x.WebSocket||x.MozWebSocket;class $ extends J{createSocket(t,e,r){return Y?new Q(t,e,r):e?new Q(t,e):new Q(t)}doWrite(t,e){this.ws.send(e)}}class X extends q{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{let e=function(t,e){n||(n=new TextDecoder);let r=[],s=0,i=-1,o=!1;return new TransformStream({transform(a,h){for(r.push(a);;){if(0===s){if(1>T(r))break;let t=A(r,1);o=(128&t[0])==128,s=(i=127&t[0])<126?3:126===i?1:2}else if(1===s){if(2>T(r))break;let t=A(r,2);i=new DataView(t.buffer,t.byteOffset,t.length).getUint16(0),s=3}else if(2===s){if(8>T(r))break;let t=A(r,8),e=new DataView(t.buffer,t.byteOffset,t.length),n=e.getUint32(0);if(n>2097151){h.enqueue(c);break}i=0x100000000*n+e.getUint32(4),s=3}else{if(T(r)<i)break;let t=A(r,i);h.enqueue(v(o?t:n.decode(t),e)),s=0}if(0===i||i>t){h.enqueue(c);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(e).getReader(),i=new TransformStream({transform(t,e){var r;r=r=>{let s,n=r.length;if(n<126)new DataView((s=new Uint8Array(1)).buffer).setUint8(0,n);else if(n<65536){let t=new DataView((s=new Uint8Array(3)).buffer);t.setUint8(0,126),t.setUint16(1,n)}else{let t=new DataView((s=new Uint8Array(9)).buffer);t.setUint8(0,127),t.setBigUint64(1,BigInt(n))}t.data&&"string"!=typeof t.data&&(s[0]|=128),e.enqueue(s),e.enqueue(r)},u&&t.data instanceof Blob?t.data.arrayBuffer().then(y).then(r):l&&(t.data instanceof ArrayBuffer||p(t.data))?r(y(t.data)):f(t,!1,t=>{s||(s=new TextEncoder),r(s.encode(t))})}});i.readable.pipeTo(t.writable),this._writer=i.writable.getWriter();let o=()=>{r.read().then(({done:t,value:e})=>{t||(this.onPacket(e),o())}).catch(t=>{})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let r=t[e],s=e===t.length-1;this._writer.write(r).then(()=>{s&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;null==(t=this._transport)||t.close()}}let G={websocket:$,webtransport:X,polling:W},Z=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,tt=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function te(t){if(t.length>8e3)throw"URI too long";let e=t,r=t.indexOf("["),s=t.indexOf("]");-1!=r&&-1!=s&&(t=t.substring(0,r)+t.substring(r,s).replace(/:/g,";")+t.substring(s,t.length));let n=Z.exec(t||""),i={},o=14;for(;o--;)i[tt[o]]=n[o]||"";return -1!=r&&-1!=s&&(i.source=e,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=function(t,e){let r=e.replace(/\/{2,9}/g,"/").split("/");return("/"==e.slice(0,1)||0===e.length)&&r.splice(0,1),"/"==e.slice(-1)&&r.splice(r.length-1,1),r}(0,i.path),i.queryKey=function(t,e){let r={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(t,e,s){e&&(r[e]=s)}),r}(0,i.query),i}let tr="function"==typeof addEventListener&&"function"==typeof removeEventListener,ts=[];tr&&addEventListener("offline",()=>{ts.forEach(t=>t())},!1);class tn extends R{constructor(t,e){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&"object"==typeof t&&(e=t,t=null),t){let r=te(t);e.hostname=r.host,e.secure="https"===r.protocol||"wss"===r.protocol,e.port=r.port,r.query&&(e.query=r.query)}else e.host&&(e.hostname=te(e.host).host);L(this,e),this.secure=null!=e.secure?e.secure:"undefined"!=typeof location&&"https:"===location.protocol,e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.hostname=e.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=e.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},e.transports.forEach(t=>{let e=t.prototype.name;this.transports.push(e),this._transportsByName[e]=t}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},e),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(t){let e={},r=t.split("&");for(let t=0,s=r.length;t<s;t++){let s=r[t].split("=");e[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return e}(this.opts.query)),tr&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ts.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){let e=Object.assign({},this.opts.query);e.EIO=4,e.transport=t,this.id&&(e.sid=this.id);let r=Object.assign({},this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);let t=this.opts.rememberUpgrade&&tn.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let e=this.createTransport(t);e.open(),this.setTransport(e)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",tn.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let e=Error("server error");e.code=t.data,this._onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let e=0;e<this.writeBuffer.length;e++){let r=this.writeBuffer[e].data;if(r&&(t+="string"==typeof r?function(t){let e=0,r=0;for(let s=0,n=t.length;s<n;s++)(e=t.charCodeAt(s))<128?r+=1:e<2048?r+=2:e<55296||e>=57344?r+=3:(s++,r+=4);return r}(r):Math.ceil(1.33*(r.byteLength||r.size))),e>0&&t>this._maxPayload)return this.writeBuffer.slice(0,e);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,C(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,e,r){return this._sendPacket("message",t,e,r),this}send(t,e,r){return this._sendPacket("message",t,e,r),this}_sendPacket(t,e,r,s){if("function"==typeof e&&(s=e,e=void 0),"function"==typeof r&&(s=r,r=null),"closing"===this.readyState||"closed"===this.readyState)return;(r=r||{}).compress=!1!==r.compress;let n={type:t,data:e,options:r};this.emitReserved("packetCreate",n),this.writeBuffer.push(n),s&&this.once("flush",s),this.flush()}close(){let t=()=>{this._onClose("forced close"),this.transport.close()},e=()=>{this.off("upgrade",e),this.off("upgradeError",e),t()},r=()=>{this.once("upgrade",e),this.once("upgradeError",e)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(tn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),tr&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let t=ts.indexOf(this._offlineEventListener);-1!==t&&ts.splice(t,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this._prevBufferLen=0}}}tn.protocol=4;class ti extends tn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let e=this.createTransport(t),r=!1;tn.priorWebsocketSuccess=!1;let s=()=>{r||(e.send([{type:"ping",data:"probe"}]),e.once("packet",t=>{if(!r)if("pong"===t.type&&"probe"===t.data){if(this.upgrading=!0,this.emitReserved("upgrading",e),!e)return;tn.priorWebsocketSuccess="websocket"===e.name,this.transport.pause(()=>{r||"closed"!==this.readyState&&(c(),this.setTransport(e),e.send([{type:"upgrade"}]),this.emitReserved("upgrade",e),e=null,this.upgrading=!1,this.flush())})}else{let t=Error("probe error");t.transport=e.name,this.emitReserved("upgradeError",t)}}))};function n(){r||(r=!0,c(),e.close(),e=null)}let i=t=>{let r=Error("probe error: "+t);r.transport=e.name,n(),this.emitReserved("upgradeError",r)};function o(){i("transport closed")}function a(){i("socket closed")}function h(t){e&&t.name!==e.name&&n()}let c=()=>{e.removeListener("open",s),e.removeListener("error",i),e.removeListener("close",o),this.off("close",a),this.off("upgrading",h)};e.once("open",s),e.once("error",i),e.once("close",o),this.once("close",a),this.once("upgrading",h),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn(()=>{r||e.open()},200):e.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){let e=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&e.push(t[r]);return e}}class to extends ti{constructor(t,e={}){let r="object"==typeof t?t:e;(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(t=>G[t]).filter(t=>!!t)),super(t,r)}}to.protocol;let ta="function"==typeof ArrayBuffer,th=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer,tc=Object.prototype.toString,tu="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===tc.call(Blob),tl="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===tc.call(File);function tp(t){return ta&&(t instanceof ArrayBuffer||th(t))||tu&&t instanceof Blob||tl&&t instanceof File}let tf=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],td=5;!function(t){t[t.CONNECT=0]="CONNECT",t[t.DISCONNECT=1]="DISCONNECT",t[t.EVENT=2]="EVENT",t[t.ACK=3]="ACK",t[t.CONNECT_ERROR=4]="CONNECT_ERROR",t[t.BINARY_EVENT=5]="BINARY_EVENT",t[t.BINARY_ACK=6]="BINARY_ACK"}(i||(i={}));class ty{constructor(t){this.replacer=t}encode(t){return(t.type===i.EVENT||t.type===i.ACK)&&function t(e,r){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let r=0,s=e.length;r<s;r++)if(t(e[r]))return!0;return!1}if(tp(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1==arguments.length)return t(e.toJSON(),!0);for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return!0;return!1}(t)?this.encodeAsBinary({type:t.type===i.EVENT?i.BINARY_EVENT:i.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let e=""+t.type;return(t.type===i.BINARY_EVENT||t.type===i.BINARY_ACK)&&(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data&&(e+=JSON.stringify(t.data,this.replacer)),e}encodeAsBinary(t){let e=function(t){let e=[],r=t.data;return t.data=function t(e,r){if(!e)return e;if(tp(e)){let t={_placeholder:!0,num:r.length};return r.push(e),t}if(Array.isArray(e)){let s=Array(e.length);for(let n=0;n<e.length;n++)s[n]=t(e[n],r);return s}if("object"==typeof e&&!(e instanceof Date)){let s={};for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(s[n]=t(e[n],r));return s}return e}(r,e),t.attachments=e.length,{packet:t,buffers:e}}(t),r=this.encodeAsString(e.packet),s=e.buffers;return s.unshift(r),s}}function tg(t){return"[object Object]"===Object.prototype.toString.call(t)}class tm extends R{constructor(t){super(),this.reviver=t}add(t){let e;if("string"==typeof t){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let r=(e=this.decodeString(t)).type===i.BINARY_EVENT;r||e.type===i.BINARY_ACK?(e.type=r?i.EVENT:i.ACK,this.reconstructor=new t_(e),0===e.attachments&&super.emitReserved("decoded",e)):super.emitReserved("decoded",e)}else if(tp(t)||t.base64)if(this.reconstructor)(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,super.emitReserved("decoded",e));else throw Error("got binary data when not reconstructing a packet");else throw Error("Unknown type: "+t)}decodeString(t){let e=0,r={type:Number(t.charAt(0))};if(void 0===i[r.type])throw Error("unknown packet type "+r.type);if(r.type===i.BINARY_EVENT||r.type===i.BINARY_ACK){let s=e+1;for(;"-"!==t.charAt(++e)&&e!=t.length;);let n=t.substring(s,e);if(n!=Number(n)||"-"!==t.charAt(e))throw Error("Illegal attachments");r.attachments=Number(n)}if("/"===t.charAt(e+1)){let s=e+1;for(;++e&&","!==t.charAt(e)&&e!==t.length;);r.nsp=t.substring(s,e)}else r.nsp="/";let s=t.charAt(e+1);if(""!==s&&Number(s)==s){let s=e+1;for(;++e;){let r=t.charAt(e);if(null==r||Number(r)!=r){--e;break}if(e===t.length)break}r.id=Number(t.substring(s,e+1))}if(t.charAt(++e)){let s=this.tryParse(t.substr(e));if(tm.isPayloadValid(r.type,s))r.data=s;else throw Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch(t){return!1}}static isPayloadValid(t,e){switch(t){case i.CONNECT:return tg(e);case i.DISCONNECT:return void 0===e;case i.CONNECT_ERROR:return"string"==typeof e||tg(e);case i.EVENT:case i.BINARY_EVENT:return Array.isArray(e)&&("number"==typeof e[0]||"string"==typeof e[0]&&-1===tf.indexOf(e[0]));case i.ACK:case i.BINARY_ACK:return Array.isArray(e)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class t_{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){var e,r;let t=(e=this.reconPack,r=this.buffers,e.data=function t(e,r){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<r.length)return r[e.num];throw Error("illegal attachments")}if(Array.isArray(e))for(let s=0;s<e.length;s++)e[s]=t(e[s],r);else if("object"==typeof e)for(let s in e)Object.prototype.hasOwnProperty.call(e,s)&&(e[s]=t(e[s],r));return e}(e.data,r),delete e.attachments,e);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function tb(t,e,r){return t.on(e,r),function(){t.off(e,r)}}let tv=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class tk extends R{constructor(t,e,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=e,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let t=this.io;this.subs=[tb(t,"open",this.onopen.bind(this)),tb(t,"packet",this.onpacket.bind(this)),tb(t,"error",this.onerror.bind(this)),tb(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...e){var r,s,n;if(tv.hasOwnProperty(t))throw Error('"'+t.toString()+'" is a reserved event name');if(e.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(e),this;let o={type:i.EVENT,data:e};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof e[e.length-1]){let t=this.ids++,r=e.pop();this._registerAckCallback(t,r),o.id=t}let a=null==(s=null==(r=this.io.engine)?void 0:r.transport)?void 0:s.writable,h=this.connected&&!(null==(n=this.io.engine)?void 0:n._hasPingExpired());return this.flags.volatile&&!a||(h?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(t,e){var r;let s=null!=(r=this.flags.timeout)?r:this._opts.ackTimeout;if(void 0===s){this.acks[t]=e;return}let n=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let e=0;e<this.sendBuffer.length;e++)this.sendBuffer[e].id===t&&this.sendBuffer.splice(e,1);e.call(this,Error("operation has timed out"))},s),i=(...t)=>{this.io.clearTimeoutFn(n),e.apply(this,t)};i.withError=!0,this.acks[t]=i}emitWithAck(t,...e){return new Promise((r,s)=>{let n=(t,e)=>t?s(t):r(e);n.withError=!0,e.push(n),this.emit(t,...e)})}_addToQueue(t){let e;"function"==typeof t[t.length-1]&&(e=t.pop());let r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((t,...s)=>{if(r===this._queue[0])return null!==t?r.tryCount>this._opts.retries&&(this._queue.shift(),e&&e(t)):(this._queue.shift(),e&&e(null,...s)),r.pending=!1,this._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||0===this._queue.length)return;let e=this._queue[0];(!e.pending||t)&&(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){"function"==typeof this.auth?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:i.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(e=>String(e.id)===t)){let e=this.acks[t];delete this.acks[t],e.withError&&e.call(this,Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case i.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case i.EVENT:case i.BINARY_EVENT:this.onevent(t);break;case i.ACK:case i.BINARY_ACK:this.onack(t);break;case i.DISCONNECT:this.ondisconnect();break;case i.CONNECT_ERROR:this.destroy();let e=Error(t.data.message);e.data=t.data.data,this.emitReserved("connect_error",e)}}onevent(t){let e=t.data||[];null!=t.id&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length)for(let e of this._anyListeners.slice())e.apply(this,t);super.emit.apply(this,t),this._pid&&t.length&&"string"==typeof t[t.length-1]&&(this._lastOffset=t[t.length-1])}ack(t){let e=this,r=!1;return function(...s){r||(r=!0,e.packet({type:i.ACK,id:t,data:s}))}}onack(t){let e=this.acks[t.id];"function"==typeof e&&(delete this.acks[t.id],e.withError&&t.data.unshift(null),e.apply(this,t.data))}onconnect(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:i.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){let e=this._anyListeners;for(let r=0;r<e.length;r++)if(t===e[r]){e.splice(r,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){let e=this._anyOutgoingListeners;for(let r=0;r<e.length;r++)if(t===e[r]){e.splice(r,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let e of this._anyOutgoingListeners.slice())e.apply(this,t.data)}}function tw(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}tw.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),r=Math.floor(e*this.jitter*t);t=(1&Math.floor(10*e))==0?t-r:t+r}return 0|Math.min(t,this.max)},tw.prototype.reset=function(){this.attempts=0},tw.prototype.setMin=function(t){this.ms=t},tw.prototype.setMax=function(t){this.max=t},tw.prototype.setJitter=function(t){this.jitter=t};class tE extends R{constructor(t,e){var r;super(),this.nsps={},this.subs=[],t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.opts=e,L(this,e),this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(null!=(r=e.randomizationFactor)?r:.5),this.backoff=new tw({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this._readyState="closed",this.uri=t;let s=e.parser||o;this.encoder=new s.Encoder,this.decoder=new s.Decoder,this._autoConnect=!1!==e.autoConnect,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return void 0===t?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var e;return void 0===t?this._reconnectionDelay:(this._reconnectionDelay=t,null==(e=this.backoff)||e.setMin(t),this)}randomizationFactor(t){var e;return void 0===t?this._randomizationFactor:(this._randomizationFactor=t,null==(e=this.backoff)||e.setJitter(t),this)}reconnectionDelayMax(t){var e;return void 0===t?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,null==(e=this.backoff)||e.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new to(this.uri,this.opts);let e=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;let s=tb(e,"open",function(){r.onopen(),t&&t()}),n=e=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",e),t?t(e):this.maybeReconnectOnOpen()},i=tb(e,"error",n);if(!1!==this._timeout){let t=this._timeout,r=this.setTimeoutFn(()=>{s(),n(Error("timeout")),e.close()},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}return this.subs.push(s),this.subs.push(i),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let t=this.engine;this.subs.push(tb(t,"ping",this.onping.bind(this)),tb(t,"data",this.ondata.bind(this)),tb(t,"error",this.onerror.bind(this)),tb(t,"close",this.onclose.bind(this)),tb(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(t){this.onclose("parse error",t)}}ondecoded(t){C(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,e){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new tk(this,t,e),this.nsps[t]=r),r}_destroy(t){for(let t of Object.keys(this.nsps))if(this.nsps[t].active)return;this._close()}_packet(t){let e=this.encoder.encode(t);for(let r=0;r<e.length;r++)this.engine.write(e[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,e){var r;this.cleanup(),null==(r=this.engine)||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let e=this.backoff.duration();this._reconnecting=!0;let r=this.setTimeoutFn(()=>{!t.skipReconnect&&(this.emitReserved("reconnect_attempt",t.backoff.attempts),t.skipReconnect||t.open(e=>{e?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",e)):t.onreconnect()}))},e);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){let t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}let tO={};function tT(t,e){let r;"object"==typeof t&&(e=t,t=void 0);let s=function(t,e="",r){let s=t;r=r||"undefined"!=typeof location&&location,null==t&&(t=r.protocol+"//"+r.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?r.protocol+t:r.host+t),/^(https?|wss?):\/\//.test(t)||(t=void 0!==r?r.protocol+"//"+t:"https://"+t),s=te(t)),!s.port&&(/^(http|ws)$/.test(s.protocol)?s.port="80":/^(http|ws)s$/.test(s.protocol)&&(s.port="443")),s.path=s.path||"/";let n=-1!==s.host.indexOf(":")?"["+s.host+"]":s.host;return s.id=s.protocol+"://"+n+":"+s.port+e,s.href=s.protocol+"://"+n+(r&&r.port===s.port?"":":"+s.port),s}(t,(e=e||{}).path||"/socket.io"),n=s.source,i=s.id,o=s.path,a=tO[i]&&o in tO[i].nsps;return e.forceNew||e["force new connection"]||!1===e.multiplex||a?r=new tE(n,e):(tO[i]||(tO[i]=new tE(n,e)),r=tO[i]),s.query&&!e.query&&(e.query=s.queryKey),r.socket(s.path,e)}Object.assign(tT,{Manager:tE,Socket:tk,io:tT,connect:tT})},6654:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return n}});let s=r(2115);function n(t,e){let r=(0,s.useRef)(null),n=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let t=r.current;t&&(r.current=null,t());let e=n.current;e&&(n.current=null,e())}else t&&(r.current=i(t,s)),e&&(n.current=i(e,s))},[t,e])}function i(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let r=t(e);return"function"==typeof r?r:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6874:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{default:function(){return g},useLinkStatus:function(){return _}});let s=r(6966),n=r(5155),i=s._(r(2115)),o=r(2757),a=r(5227),h=r(9818),c=r(6654),u=r(9991),l=r(5929);r(3230);let p=r(4930),f=r(2664),d=r(6634);function y(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}function g(t){let e,r,s,[o,g]=(0,i.useOptimistic)(p.IDLE_LINK_STATUS),_=(0,i.useRef)(null),{href:b,as:v,children:k,prefetch:w=null,passHref:E,replace:O,shallow:T,scroll:A,onClick:R,onMouseEnter:C,onTouchStart:x,legacyBehavior:S=!1,onNavigate:N,ref:B,unstable_dynamicOnHover:L,...P}=t;e=k,S&&("string"==typeof e||"number"==typeof e)&&(e=(0,n.jsx)("a",{children:e}));let j=i.default.useContext(a.AppRouterContext),q=!1!==w,U=null===w?h.PrefetchKind.AUTO:h.PrefetchKind.FULL,{href:I,as:D}=i.default.useMemo(()=>{let t=y(b);return{href:t,as:v?y(v):t}},[b,v]);S&&(r=i.default.Children.only(e));let M=S?r&&"object"==typeof r&&r.ref:B,F=i.default.useCallback(t=>(null!==j&&(_.current=(0,p.mountLinkInstance)(t,I,j,U,q,g)),()=>{_.current&&((0,p.unmountLinkForCurrentNavigation)(_.current),_.current=null),(0,p.unmountPrefetchableInstance)(t)}),[q,I,j,U,g]),V={ref:(0,c.useMergedRef)(F,M),onClick(t){S||"function"!=typeof R||R(t),S&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(t),j&&(t.defaultPrevented||function(t,e,r,s,n,o,a){let{nodeName:h}=t.currentTarget;if(!("A"===h.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t)||t.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(e)){n&&(t.preventDefault(),location.replace(e));return}t.preventDefault(),i.default.startTransition(()=>{if(a){let t=!1;if(a({preventDefault:()=>{t=!0}}),t)return}(0,d.dispatchNavigateAction)(r||e,n?"replace":"push",null==o||o,s.current)})}}(t,I,D,_,O,A,N))},onMouseEnter(t){S||"function"!=typeof C||C(t),S&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(t),j&&q&&(0,p.onNavigationIntent)(t.currentTarget,!0===L)},onTouchStart:function(t){S||"function"!=typeof x||x(t),S&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(t),j&&q&&(0,p.onNavigationIntent)(t.currentTarget,!0===L)}};return(0,u.isAbsoluteUrl)(D)?V.href=D:S&&!E&&("a"!==r.type||"href"in r.props)||(V.href=(0,l.addBasePath)(D)),s=S?i.default.cloneElement(r,V):(0,n.jsx)("a",{...P,...V,children:e}),(0,n.jsx)(m.Provider,{value:o,children:s})}r(3180);let m=(0,i.createContext)(p.IDLE_LINK_STATUS),_=()=>(0,i.useContext)(m);("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},8859:(t,e)=>{function r(t){let e={};for(let[r,s]of t.entries()){let t=e[r];void 0===t?e[r]=s:Array.isArray(t)?t.push(s):e[r]=[t,s]}return e}function s(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function n(t){let e=new URLSearchParams;for(let[r,n]of Object.entries(t))if(Array.isArray(n))for(let t of n)e.append(r,s(t));else e.set(r,s(n));return e}function i(t){for(var e=arguments.length,r=Array(e>1?e-1:0),s=1;s<e;s++)r[s-1]=arguments[s];for(let e of r){for(let r of e.keys())t.delete(r);for(let[r,s]of e.entries())t.append(r,s)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return n}})},9991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{DecodeError:function(){return d},MiddlewareNotFoundError:function(){return _},MissingStaticPage:function(){return m},NormalizeError:function(){return y},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return s},getDisplayName:function(){return h},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return l},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function s(t){let e,r=!1;return function(){for(var s=arguments.length,n=Array(s),i=0;i<s;i++)n[i]=arguments[i];return r||(r=!0,e=t(...n)),e}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=t=>n.test(t);function o(){let{protocol:t,hostname:e,port:r}=window.location;return t+"//"+e+(r?":"+r:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function h(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function c(t){return t.finished||t.headersSent}function u(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function l(t,e){let r=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await l(e.Component,e.ctx)}:{};let s=await t.getInitialProps(e);if(r&&c(r))return s;if(!s)throw Object.defineProperty(Error('"'+h(t)+'.getInitialProps()" should resolve to an object. But found "'+s+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s}let p="undefined"!=typeof performance,f=p&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class d extends Error{}class y extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class m extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class _ extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(t){return JSON.stringify({message:t.message,stack:t.stack})}}}]);