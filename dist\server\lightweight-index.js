"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const ws_1 = __importDefault(require("ws"));
const cors_1 = __importDefault(require("cors"));
const xlsx = __importStar(require("xlsx"));
const path_1 = require("path");
const fs_1 = require("fs");
const dotenv_1 = require("dotenv");
// Load environment variables
(0, dotenv_1.config)();
// Constants
const EXCHANGE_SEGMENTS = {
    NSE_EQ: 1,
    NSE_FNO: 2,
    BSE_EQ: 11,
    MCX_COMM: 51,
};
const SUBSCRIPTION_TYPES = {
    ticker: 15,
    quote: 17,
    full: 21,
};
class LightweightMarketFeedServer {
    constructor() {
        this.ws = null;
        this.instruments = [];
        this.marketData = new Map();
        this.subscriptionType = "quote";
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.io = new socket_io_1.Server(this.server, {
            cors: { origin: "*", methods: ["GET", "POST"] },
        });
        this.accessToken = process.env.ACCESS_TOKEN || "";
        this.clientId = process.env.CLIENT_ID || "";
        this.loadInstruments();
        this.setupWebServer();
        this.validateConfig();
        this.connectToMarketFeed();
    }
    loadInstruments() {
        try {
            // Try to load from Excel file first
            const excelPath = (0, path_1.join)(process.cwd(), "webdata.xlsx");
            if ((0, fs_1.existsSync)(excelPath)) {
                console.log("📊 Loading instruments from Excel file...");
                this.loadFromExcel(excelPath);
            }
            else {
                console.log("📋 Using default instruments...");
                this.setDefaultInstruments();
            }
        }
        catch (error) {
            console.error("❌ Error loading instruments:", error);
            this.setDefaultInstruments();
        }
    }
    loadFromExcel(filePath) {
        try {
            const workbook = xlsx.readFile(filePath);
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const data = xlsx.utils.sheet_to_json(worksheet);
            console.log(`📊 Found ${data.length} rows in Excel file`);
            // Debug: Check first row to see column names
            if (data.length > 0) {
                console.log("📋 Excel columns:", Object.keys(data[0]));
            }
            const instruments = [];
            // Use the actual column names from the Excel file
            data.forEach((row, index) => {
                const ticker = row["Ticker"];
                const securityId = row["SecurityId"];
                const exchangeSegment = row["ExchangeSegment"];
                // Only add NSE_EQ instruments for better performance
                if (ticker && securityId && exchangeSegment === "NSE_EQ") {
                    instruments.push({
                        ticker: ticker.toString().trim(),
                        exchange: "NSE_EQ",
                        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                        securityId: securityId.toString().trim(),
                    });
                }
                // Limit to 100 instruments for good performance
                if (instruments.length >= 100)
                    return;
            });
            this.instruments = instruments;
            console.log(`✅ Loaded ${this.instruments.length} instruments from Excel`);
            if (this.instruments.length === 0) {
                console.log("⚠️ No valid instruments found in Excel, using defaults");
                this.setDefaultInstruments();
            }
        }
        catch (error) {
            console.error("❌ Excel loading failed:", error);
            this.setDefaultInstruments();
        }
    }
    setDefaultInstruments() {
        this.instruments = [
            {
                ticker: "HDFCBANK",
                exchange: "NSE_EQ",
                exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                securityId: "1333",
            },
            {
                ticker: "SBIN",
                exchange: "NSE_EQ",
                exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                securityId: "3045",
            },
            {
                ticker: "INFY",
                exchange: "NSE_EQ",
                exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                securityId: "1594",
            },
            {
                ticker: "TCS",
                exchange: "NSE_EQ",
                exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
                securityId: "11536",
            },
        ];
        console.log(`📋 Using ${this.instruments.length} default instruments`);
    }
    validateConfig() {
        if (!this.instruments.length) {
            console.warn("⚠️ No instruments loaded");
        }
        console.log(`✅ Configuration validated - ${this.instruments.length} instruments loaded`);
    }
    setupWebServer() {
        this.app.use((0, cors_1.default)());
        this.app.use(express_1.default.json());
        // Simple API endpoints
        this.app.get("/api/data", (req, res) => {
            res.json({
                connected: this.ws?.readyState === ws_1.default.OPEN,
                instruments: this.instruments.length,
                subscriptionType: this.subscriptionType,
                activeInstruments: this.marketData.size,
                latestData: Array.from(this.marketData.values()).slice(0, 20),
            });
        });
        this.app.get("/api/instruments", (req, res) => {
            res.json({
                instruments: this.instruments.slice(0, 50),
                total: this.instruments.length,
            });
        });
        // Socket.IO setup
        this.io.on("connection", (socket) => {
            console.log("📱 Client connected");
            // Send initial data
            socket.emit("initialData", {
                instruments: this.instruments,
                liveData: Array.from(this.marketData.values()),
            });
            socket.on("disconnect", () => {
                console.log("📱 Client disconnected");
            });
        });
    }
    connectToMarketFeed() {
        // Always try to connect to real Dhan API first
        this.accessToken = process.env.ACCESS_TOKEN || "";
        this.clientId = process.env.CLIENT_ID || "";
        if (!this.accessToken || !this.clientId) {
            console.warn("⚠️ Missing credentials in .env file");
            console.log("📋 Please add ACCESS_TOKEN and CLIENT_ID to .env file");
            console.log("🔄 Attempting connection anyway...");
        }
        // Use real credentials from .env or fallback
        const token = this.accessToken || "YOUR_ACCESS_TOKEN";
        const clientId = this.clientId || "1100232369";
        const wsUrl = `wss://api-feed.dhan.co?version=2&token=${token}&clientId=${clientId}&authType=2`;
        console.log("🔗 Connecting to Dhan market feed...");
        console.log(`📡 Using Client ID: ${clientId}`);
        this.ws = new ws_1.default(wsUrl);
        this.ws.on("open", () => {
            console.log("✅ Connected to market feed");
            this.subscribeToInstruments();
        });
        this.ws.on("message", (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleMarketData(message);
            }
            catch (error) {
                console.error("❌ Error parsing message:", error);
            }
        });
        this.ws.on("error", (error) => {
            console.error("❌ WebSocket error:", error);
            console.log("🔄 Will retry connection in 5 seconds...");
        });
        this.ws.on("close", (code, reason) => {
            console.log(`🔌 WebSocket connection closed: ${code} - ${reason}`);
            console.log("🔄 Reconnecting in 5 seconds...");
            setTimeout(() => this.connectToMarketFeed(), 5000);
        });
    }
    subscribeToInstruments() {
        if (!this.ws || this.ws.readyState !== ws_1.default.OPEN)
            return;
        const subscriptionMessage = {
            RequestCode: SUBSCRIPTION_TYPES[this.subscriptionType],
            InstrumentCount: this.instruments.length,
            InstrumentList: this.instruments.map((instrument) => ({
                ExchangeSegment: instrument.exchangeCode,
                SecurityId: instrument.securityId,
            })),
        };
        console.log(`📡 Subscribing to ${this.instruments.length} instruments...`);
        this.ws.send(JSON.stringify(subscriptionMessage));
    }
    handleMarketData(message) {
        try {
            // Handle different message formats from Dhan API
            let data = message;
            // If it's wrapped in a type/data structure
            if (message.type && message.data) {
                data = message.data;
            }
            // Look for security ID in various possible fields
            const securityId = data.security_id || data.SecurityId || data.securityId || data.SI;
            if (securityId) {
                const instrument = this.instruments.find((inst) => inst.securityId === securityId.toString());
                if (instrument) {
                    // Parse market data from Dhan API format
                    const ltp = data.LTP || data.ltp || data.LastPrice || 0;
                    const prevClose = data.prev_close || data.PrevClose || data.close || ltp;
                    const change = ltp - prevClose;
                    const changePercent = prevClose > 0 ? (change / prevClose) * 100 : 0;
                    const marketData = {
                        ticker: instrument.ticker,
                        securityId: instrument.securityId,
                        exchange: instrument.exchange,
                        exchangeCode: instrument.exchangeCode,
                        ltp: ltp,
                        change: change,
                        changePercent: changePercent,
                        volume: data.volume || data.Volume || data.TotalTradedQuantity || 0,
                        high: data.high || data.High || data.DayHigh || ltp,
                        low: data.low || data.Low || data.DayLow || ltp,
                        open: data.open || data.Open || data.DayOpen || ltp,
                        close: prevClose,
                        timestamp: Date.now(),
                    };
                    this.marketData.set(instrument.securityId, marketData);
                    this.io.emit("marketData", marketData);
                    console.log(`📈 ${instrument.ticker}: ₹${ltp} (${change >= 0 ? "+" : ""}${change.toFixed(2)})`);
                }
            }
        }
        catch (error) {
            console.error("❌ Error handling market data:", error);
        }
    }
    start() {
        const port = process.env.PORT || 8080;
        this.server.listen(port, () => {
            console.log(`🚀 Lightweight server running on port ${port}`);
        });
    }
}
// Start the server
const server = new LightweightMarketFeedServer();
server.start();
