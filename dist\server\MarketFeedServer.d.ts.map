{"version": 3, "file": "MarketFeedServer.d.ts", "sourceRoot": "", "sources": ["../../src/server/MarketFeedServer.ts"], "names": [], "mappings": "AAIA,OAAO,eAAe,CAAC;AAwCvB,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,gBAAgB,CAAS;IACjC,OAAO,CAAC,IAAI,CAAS;IAErB,OAAO,CAAC,EAAE,CAA0B;IACpC,OAAO,CAAC,WAAW,CAAkB;IACrC,OAAO,CAAC,YAAY,CAAa;IACjC,OAAO,CAAC,WAAW,CAAoB;IAGvC,OAAO,CAAC,QAAQ,CAAsC;IACtD,OAAO,CAAC,iBAAiB,CAAqD;IAG9E,OAAO,CAAC,GAAG,CAAsB;IACjC,OAAO,CAAC,MAAM,CAAM;IACpB,OAAO,CAAC,EAAE,CAAS;IAGnB,OAAO,CAAC,eAAe,CAAa;IACpC,OAAO,CAAC,kBAAkB,CAAa;IACvC,OAAO,CAAC,oBAAoB,CAAa;;IAsBzC;;OAEG;IACH,OAAO,CAAC,cAAc;IAqBtB;;OAEG;YACW,eAAe;IAsI7B;;OAEG;IACH,OAAO,CAAC,cAAc;IA0TtB;;OAEG;IACH,OAAO,CAAC,mBAAmB;IAkB3B;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC,IAAI,CAAC;IA0D1C;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAqB9B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IA0BxB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAuCxB;;OAEG;IACH,OAAO,CAAC,eAAe;IA6HvB;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAQ9B;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IA6B5B;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAyB7B;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IA6B/B;;OAEG;IACH,SAAS,IAAI;QACX,SAAS,EAAE,OAAO,CAAC;QACnB,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;KAChB;CASF"}