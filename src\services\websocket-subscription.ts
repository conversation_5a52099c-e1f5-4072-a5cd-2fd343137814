import DatabaseService, { Company } from './database';

export interface WebSocketInstrument {
  ExchangeSegment: string;
  SecurityId: string;
  symbol?: string;
  companyName?: string;
  sector?: string;
  industry?: string;
}

export interface WebSocketConnection {
  connectionId: number;
  instruments: WebSocketInstrument[];
  count: number;
}

export class WebSocketSubscriptionService {
  private static readonly MAX_CONNECTIONS = 5;
  private static readonly MAX_INSTRUMENTS_PER_CONNECTION = 5000;

  /**
   * Get websocket instruments from database companies
   * Maps NSE_SECURITY_ID to NSE_EQ and BSE_SECURITY_ID to BSE_EQ
   */
  static async getWebSocketInstruments(filters?: {
    sectors?: string[];
    industries?: string[];
    symbols?: string[];
    exchanges?: ('NSE' | 'BSE')[];
    limit?: number;
  }): Promise<WebSocketInstrument[]> {
    try {
      const instruments: WebSocketInstrument[] = [];
      
      // Get companies from database
      const searchResult = await DatabaseService.searchCompanies({
        sector: filters?.sectors?.[0], // For now, use first sector
        industry: filters?.industries?.[0], // For now, use first industry
        limit: filters?.limit || 5000
      });

      const companies = searchResult.companies;

      for (const company of companies) {
        // Add NSE instrument if NSE_SECURITY_ID exists
        if (company.nse_security_id && (!filters?.exchanges || filters.exchanges.includes('NSE'))) {
          instruments.push({
            ExchangeSegment: "NSE_EQ",
            SecurityId: company.nse_security_id,
            symbol: company.nse_symbol || undefined,
            companyName: company.company_name,
            sector: company.sector_name || undefined,
            industry: company.industry_name || undefined
          });
        }

        // Add BSE instrument if BSE_SECURITY_ID exists
        if (company.bse_security_id && (!filters?.exchanges || filters.exchanges.includes('BSE'))) {
          instruments.push({
            ExchangeSegment: "BSE_EQ",
            SecurityId: company.bse_security_id,
            symbol: company.nse_symbol || undefined, // Use NSE symbol as reference
            companyName: company.company_name,
            sector: company.sector_name || undefined,
            industry: company.industry_name || undefined
          });
        }
      }

      console.log(`📊 Generated ${instruments.length} websocket instruments from ${companies.length} companies`);
      return instruments;

    } catch (error) {
      console.error('Error generating websocket instruments:', error);
      throw error;
    }
  }

  /**
   * Get instruments by specific sectors (for index analysis)
   */
  static async getInstrumentsBySectors(sectors: string[], exchange: 'NSE' | 'BSE' = 'NSE'): Promise<WebSocketInstrument[]> {
    try {
      const instruments: WebSocketInstrument[] = [];

      for (const sector of sectors) {
        const companies = await DatabaseService.getCompaniesBySector(sector);
        
        for (const company of companies) {
          if (exchange === 'NSE' && company.nse_security_id) {
            instruments.push({
              ExchangeSegment: "NSE_EQ",
              SecurityId: company.nse_security_id,
              symbol: company.nse_symbol || undefined,
              companyName: company.company_name,
              sector: company.sector_name || undefined,
              industry: company.industry_name || undefined
            });
          } else if (exchange === 'BSE' && company.bse_security_id) {
            instruments.push({
              ExchangeSegment: "BSE_EQ", 
              SecurityId: company.bse_security_id,
              symbol: company.nse_symbol || undefined,
              companyName: company.company_name,
              sector: company.sector_name || undefined,
              industry: company.industry_name || undefined
            });
          }
        }
      }

      return instruments;
    } catch (error) {
      console.error('Error getting instruments by sectors:', error);
      throw error;
    }
  }

  /**
   * Get instruments for specific index constituents
   */
  static async getInstrumentsForIndex(symbols: string[], exchange: 'NSE' | 'BSE' = 'NSE'): Promise<WebSocketInstrument[]> {
    try {
      const instruments: WebSocketInstrument[] = [];

      for (const symbol of symbols) {
        const company = await DatabaseService.getCompanyBySymbol(symbol);
        
        if (company) {
          if (exchange === 'NSE' && company.nse_security_id) {
            instruments.push({
              ExchangeSegment: "NSE_EQ",
              SecurityId: company.nse_security_id,
              symbol: company.nse_symbol || undefined,
              companyName: company.company_name,
              sector: company.sector_name || undefined,
              industry: company.industry_name || undefined
            });
          } else if (exchange === 'BSE' && company.bse_security_id) {
            instruments.push({
              ExchangeSegment: "BSE_EQ",
              SecurityId: company.bse_security_id,
              symbol: company.nse_symbol || undefined,
              companyName: company.company_name,
              sector: company.sector_name || undefined,
              industry: company.industry_name || undefined
            });
          }
        } else {
          console.warn(`⚠️ Company not found for symbol: ${symbol}`);
        }
      }

      return instruments;
    } catch (error) {
      console.error('Error getting instruments for index:', error);
      throw error;
    }
  }

  /**
   * Split instruments into multiple connections (max 5000 per connection)
   */
  static splitIntoConnections(instruments: WebSocketInstrument[]): WebSocketConnection[] {
    const connections: WebSocketConnection[] = [];
    
    for (let i = 0; i < instruments.length; i += this.MAX_INSTRUMENTS_PER_CONNECTION) {
      const connectionInstruments = instruments.slice(i, i + this.MAX_INSTRUMENTS_PER_CONNECTION);
      const connectionId = Math.floor(i / this.MAX_INSTRUMENTS_PER_CONNECTION) + 1;
      
      if (connectionId > this.MAX_CONNECTIONS) {
        console.warn(`⚠️ Exceeded maximum connections (${this.MAX_CONNECTIONS}). Truncating instruments.`);
        break;
      }

      connections.push({
        connectionId,
        instruments: connectionInstruments,
        count: connectionInstruments.length
      });
    }

    console.log(`📡 Split ${instruments.length} instruments into ${connections.length} connections`);
    connections.forEach(conn => {
      console.log(`  Connection ${conn.connectionId}: ${conn.count} instruments`);
    });

    return connections;
  }

  /**
   * Get predefined index instruments (Bank Nifty, Nifty IT, etc.)
   */
  static async getPredefinedIndexInstruments(indexName: string): Promise<WebSocketInstrument[]> {
    const indexSymbols: { [key: string]: string[] } = {
      'BANKNIFTY': [
        'HDFCBANK', 'ICICIBANK', 'AXISBANK', 'SBIN', 'KOTAKBANK',
        'INDUSINDBK', 'FEDERALBNK', 'BANKBARODA', 'IDFCFIRSTB', 'AUBANK', 'PNB'
      ],
      'NIFTYIT': [
        'TCS', 'INFY', 'HCLTECH', 'WIPRO', 'TECHM', 'LTIM', 
        'PERSISTENT', 'MPHASIS', 'COFORGE', 'LTTS'
      ],
      'NIFTYAUTO': [
        'MARUTI', 'M&M', 'TATAMOTORS', 'BAJAJ-AUTO', 'HEROMOTOCO',
        'EICHERMOT', 'TVSMOTOR', 'BOSCHLTD', 'ASHOKLEY', 'BHARATFORG'
      ]
    };

    const symbols = indexSymbols[indexName.toUpperCase()];
    if (!symbols) {
      throw new Error(`Index ${indexName} not found`);
    }

    return await this.getInstrumentsForIndex(symbols);
  }

  /**
   * Generate subscription message for Dhan WebSocket
   */
  static generateSubscriptionMessage(
    instruments: WebSocketInstrument[], 
    requestCode: number = 17 // 15=ticker, 17=quote, 21=full
  ) {
    return {
      RequestCode: requestCode,
      InstrumentCount: instruments.length,
      InstrumentList: instruments.map(instrument => ({
        ExchangeSegment: instrument.ExchangeSegment,
        SecurityId: instrument.SecurityId
      }))
    };
  }

  /**
   * Get sector-wise instrument distribution
   */
  static async getSectorDistribution(): Promise<{
    sector: string;
    nseInstruments: number;
    bseInstruments: number;
    totalInstruments: number;
  }[]> {
    try {
      const sectors = await DatabaseService.getSectors();
      const distribution = [];

      for (const sector of sectors) {
        const companies = await DatabaseService.getCompaniesBySector(sector);
        
        const nseCount = companies.filter(c => c.nse_security_id).length;
        const bseCount = companies.filter(c => c.bse_security_id).length;
        
        distribution.push({
          sector,
          nseInstruments: nseCount,
          bseInstruments: bseCount,
          totalInstruments: nseCount + bseCount
        });
      }

      return distribution.sort((a, b) => b.totalInstruments - a.totalInstruments);
    } catch (error) {
      console.error('Error getting sector distribution:', error);
      throw error;
    }
  }
}

export default WebSocketSubscriptionService;
