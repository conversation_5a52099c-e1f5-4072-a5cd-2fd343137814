"use client";

import React, { useEffect, useState } from "react";
import { io } from "socket.io-client";
import Link from "next/link";
import StocksColumnLayout from "../../components/StocksColumnLayout";
import ConnectionStatus from "../../components/ConnectionStatus";
import Stats from "../../components/Stats";
import Navigation from "../../components/Navigation";
import KeyIndices from "../../components/KeyIndices";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface Stats {
  instrumentCount: number;
  updateRate: number;
  latency: number;
  lastUpdate: string;
}

export default function StocksPage() {
  const [socket, setSocket] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [marketData, setMarketData] = useState<Map<string, MarketData>>(
    new Map()
  );
  const [stats, setStats] = useState<Stats>({
    instrumentCount: 0,
    updateRate: 0,
    latency: 0,
    lastUpdate: "Never",
  });

  useEffect(() => {
    // Connect to the WebSocket server running on port 8080
    const socketInstance = io("http://localhost:8080", {
      transports: ["websocket", "polling"],
      upgrade: true,
      rememberUpgrade: false,
      timeout: 20000,
      forceNew: true,
    });

    socketInstance.on(
      "initialData",
      (data: { instruments: any[]; liveData: MarketData[] }) => {
        setStats((prev) => ({
          ...prev,
          instrumentCount: data.instruments.length,
        }));

        setMarketData((prev) => {
          const newMarketData = new Map(prev);
          data.liveData.forEach((item: MarketData) => {
            newMarketData.set(item.securityId, item);
          });
          return newMarketData;
        });
      }
    );

    socketInstance.on("connect", () => {
      setIsConnected(true);
    });

    socketInstance.on("disconnect", () => {
      setIsConnected(false);
    });

    socketInstance.on("marketData", (data: MarketData) => {
      setMarketData((prev) => {
        const newMarketData = new Map(prev);
        newMarketData.set(data.securityId, data);
        return newMarketData;
      });
    });

    socketInstance.on("marketDataBatch", (batch: MarketData[]) => {
      setMarketData((prev) => {
        const newMarketData = new Map(prev);
        batch.forEach((item) => {
          newMarketData.set(item.securityId, item);
        });
        return newMarketData;
      });
    });

    socketInstance.on("status", (data: any) => {
      setStats((prev) => ({
        ...prev,
        updateRate: data.updateRate || 0,
        latency: data.latency || 0,
        lastUpdate: new Date().toLocaleTimeString(),
      }));
    });

    setSocket(socketInstance);

    // Fetch initial stats from the WebSocket server
    const fetchInitialStats = async () => {
      try {
        const response = await fetch("http://localhost:8080/api/data");
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setStats((prev) => ({
          ...prev,
          instrumentCount: data.totalInstruments || data.instruments || 0,
        }));
      } catch (error) {
        console.warn(
          "Could not fetch initial stats from WebSocket server:",
          error
        );
        // Don't show error to user, just log it - WebSocket will provide the data
      }
    };

    // Try to fetch initial stats, but don't block if it fails
    fetchInitialStats();

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  return (
    <main className="min-h-screen bg-gray-50">
      <Navigation />
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center space-x-4">
            <h1 className="text-3xl font-bold text-gray-900">
              📊 Stocks Market Overview
            </h1>
            <Link
              href="/market-overview"
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              Detailed View
            </Link>
          </div>
          <ConnectionStatus isConnected={isConnected} />
        </div>

        <Stats stats={stats} />

        {/* Key Indices and Stocks Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          {/* Key Indices Sidebar */}
          <div className="lg:col-span-1">
            <KeyIndices />
          </div>

          {/* Main Stocks Column Layout */}
          <div className="lg:col-span-3">
            <StocksColumnLayout data={Array.from(marketData.values())} />
          </div>
        </div>

        {/* Additional Market Insights */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Market Insights
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {
                  Array.from(marketData.values()).filter(
                    (stock) => stock.change > 0
                  ).length
                }
              </div>
              <div className="text-sm text-gray-600">Stocks Up</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {
                  Array.from(marketData.values()).filter(
                    (stock) => stock.change < 0
                  ).length
                }
              </div>
              <div className="text-sm text-gray-600">Stocks Down</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {
                  Array.from(marketData.values()).filter(
                    (stock) => stock.change === 0
                  ).length
                }
              </div>
              <div className="text-sm text-gray-600">Unchanged</div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
