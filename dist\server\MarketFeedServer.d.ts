import "dotenv/config";
export declare class DhanMarketFeedServer {
    private accessToken;
    private clientId;
    private subscriptionType;
    private port;
    private ws;
    private isConnected;
    private messageCount;
    private instruments;
    private liveData;
    private previousCloseData;
    private app;
    private server;
    private io;
    private lastHealthCheck;
    private connectionAttempts;
    private maxReconnectAttempts;
    constructor();
    /**
     * Validate configuration
     */
    private validateConfig;
    /**
     * Load instruments from database
     */
    private loadInstruments;
    /**
     * Setup web server routes
     */
    private setupWebServer;
    /**
     * Setup Socket.IO handlers
     */
    private setupSocketHandlers;
    /**
     * Connect to Dhan WebSocket feed
     */
    connectToMarketFeed(): Promise<void>;
    /**
     * Subscribe to instruments
     */
    private subscribeToInstruments;
    /**
     * Subscribe to a batch of instruments (Fixed to match working implementation)
     */
    private subscribeToBatch;
    /**
     * Handle incoming market data
     */
    private handleMarketData;
    /**
     * Parse binary market data (Real implementation from working codebase)
     */
    private parseMarketData;
    /**
     * Get exchange segment name from code
     */
    private getExchangeSegmentName;
    /**
     * Start the server
     */
    start(): Promise<void>;
    /**
     * Start health monitoring
     */
    private startHealthMonitoring;
    /**
     * Graceful shutdown
     */
    shutdown(): Promise<void>;
    /**
     * Get server status
     */
    getStatus(): {
        connected: boolean;
        instruments: number;
        messages: number;
        uptime: number;
        memory: number;
    };
}
//# sourceMappingURL=MarketFeedServer.d.ts.map