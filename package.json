{"name": "dhan-websocket-server", "version": "0.1.0", "private": true, "description": "Ultra-fast market data server for Dhan trading platform", "scripts": {"dev": "concurrently \"npm run dev:next\" \"npm run dev:server\"", "dev:next": "next dev", "dev:server": "ts-node-dev --respawn --transpile-only --require tsconfig-paths/register src/server/index.ts", "build": "next build && tsc src/server/index.ts --outDir dist --module commonjs --target es2020 --esModuleInterop --allowSyntheticDefaultImports", "start": "concurrently \"next start\" \"node dist/server/index.js\"", "start:server": "node dist/server/index.js", "start:next": "next start", "lint": "next lint", "clean": "rm -rf dist .next"}, "dependencies": {"axios": "1.8.4", "class-variance-authority": "0.7.0", "clsx": "2.1.0", "cors": "^2.8.5", "date-fns": "3.3.1", "dotenv": "^16.0.3", "express": "^4.18.2", "framer-motion": "12.15.0", "lucide-react": "^0.323.0", "multer": "^1.4.5-lts.1", "next": "^15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "2.4.1", "react-icons": "5.0.1", "recharts": "^2.15.3", "sharp": "0.33.2", "socket.io": "^4.6.1", "socket.io-client": "^4.8.1", "tailwind-merge": "2.2.1", "tailwindcss-animate": "1.0.7", "ws": "^8.13.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/recharts": "1.8.29", "@types/socket.io": "^3.0.2", "@types/socket.io-client": "^3.0.0", "@types/ws": "^8.5.5", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "8.29.1", "@typescript-eslint/parser": "8.29.1", "autoprefixer": "^10.4.15", "concurrently": "^8.2.1", "eslint": "8.57.0", "eslint-config-next": "14.2.28", "postcss": "^8.4.28", "prettier": "3.2.5", "prettier-plugin-tailwindcss": "0.5.11", "sonner": "1.4.0", "tailwindcss": "^3.3.3", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}, "pnpm": {"ignoredBuiltDependencies": ["esbuild", "sharp"]}, "overrides": {"glob": "^9.0.0", "rimraf": "^4.0.0", "inflight": "^2.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "8.29.1", "@typescript-eslint/parser": "8.29.1"}, "engines": {"node": "22.x", "pnpm": ">=9.0.0"}}