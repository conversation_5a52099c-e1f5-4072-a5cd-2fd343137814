{"name": "dhan-websocket-server", "version": "0.1.0", "private": true, "description": "Ultra-fast market data server for Dhan trading platform", "scripts": {"dev": "concurrently \"next dev\" \"npm run dev:server\"", "dev:next": "next dev", "dev:server": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register --project tsconfig.server.json src/server/main.ts", "build": "npm run build:next && npm run build:server", "build:next": "next build", "build:server": "tsc --project tsconfig.server.json", "start": "next start", "start:server": "node dist/server/main.js", "start:next": "next start", "lint": "next lint && tsc --noEmit --project tsconfig.server.json", "lint:server": "tsc --noEmit --project tsconfig.server.json", "clean": "rm -rf dist .next", "server": "npm run dev:server", "type-check": "tsc --noEmit && tsc --noEmit --project tsconfig.server.json", "test:server": "echo \"Server tests not implemented yet\"", "prod:server": "npm run build:server && npm run start:server"}, "dependencies": {"@types/pg": "^8.15.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cors": "^2.8.5", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "framer-motion": "^12.15.0", "helmet": "^7.2.0", "lucide-react": "^0.323.0", "multer": "^1.4.5-lts.2", "next": "^15.3.3", "pg": "^8.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "recharts": "^2.15.3", "sharp": "^0.33.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "ws": "^8.18.2", "xlsx": "^0.18.5", "zod": "^3.25.56"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/helmet": "^4.0.0", "@types/multer": "^1.4.13", "@types/node": "^20.19.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/recharts": "^1.8.29", "@types/socket.io": "^3.0.2", "@types/socket.io-client": "^3.0.0", "@types/ws": "^8.18.1", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-config-next": "^14.2.28", "postcss": "^8.5.4", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "sonner": "^1.4.0", "tailwindcss": "^3.4.17", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "pnpm": {"ignoredBuiltDependencies": ["esbuild", "sharp"]}, "overrides": {"glob": "^9.0.0", "rimraf": "^4.0.0", "inflight": "^2.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "@typescript-eslint/eslint-plugin": "8.29.1", "@typescript-eslint/parser": "8.29.1"}, "engines": {"node": "22.x", "pnpm": ">=9.0.0"}}