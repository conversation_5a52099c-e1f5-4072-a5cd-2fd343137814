import { Request, Response, NextFunction } from "express";
import { ZodError } from "zod";
import { appConfig } from "../config";

// Custom error class for API errors
export class APIError extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = "APIError";
  }
}

// Error handler middleware
export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error("Error:", {
    name: err.name,
    message: err.message,
    stack: appConfig.logging.level === "debug" ? err.stack : undefined,
  });

  // Handle Zod validation errors
  if (err instanceof ZodError) {
    return res.status(400).json({
      success: false,
      error: "Validation Error",
      details: err.errors,
    });
  }

  // Handle API errors
  if (err instanceof APIError) {
    return res.status(err.statusCode).json({
      success: false,
      error: err.message,
      code: err.code,
      details: err.details,
    });
  }

  // Handle database errors
  if (err.name === "PostgresError") {
    return res.status(500).json({
      success: false,
      error: "Database Error",
      message: "An error occurred while accessing the database",
    });
  }

  // Handle WebSocket errors
  if (err.name === "WebSocketError") {
    return res.status(503).json({
      success: false,
      error: "WebSocket Error",
      message: "Failed to establish WebSocket connection",
    });
  }

  // Handle file upload errors
  if (err.name === "MulterError") {
    return res.status(400).json({
      success: false,
      error: "File Upload Error",
      message: err.message,
    });
  }

  // Default error handler
  return res.status(500).json({
    success: false,
    error: "Internal Server Error",
    message:
      appConfig.logging.level === "debug"
        ? err.message
        : "An unexpected error occurred",
  });
};

// Not found handler middleware
export const notFoundHandler = (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: "Not Found",
    message: `The requested resource ${req.method} ${req.originalUrl} was not found`,
  });
};

// Async handler wrapper to catch async errors
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Validation error handler
export const validationError = (message: string, details?: any) => {
  return new APIError(400, message, "VALIDATION_ERROR", details);
};

// Authentication error handler
export const authError = (message: string = "Authentication failed") => {
  return new APIError(401, message, "AUTH_ERROR");
};

// Authorization error handler
export const forbiddenError = (message: string = "Access denied") => {
  return new APIError(403, message, "FORBIDDEN");
};

// Resource not found error handler
export const notFoundError = (message: string = "Resource not found") => {
  return new APIError(404, message, "NOT_FOUND");
};

// Rate limit error handler
export const rateLimitError = (message: string = "Too many requests") => {
  return new APIError(429, message, "RATE_LIMIT_EXCEEDED");
};

// Database error handler
export const databaseError = (
  message: string = "Database operation failed"
) => {
  return new APIError(500, message, "DATABASE_ERROR");
};

// WebSocket error handler
export const websocketError = (
  message: string = "WebSocket operation failed"
) => {
  return new APIError(503, message, "WEBSOCKET_ERROR");
};
