import express from "express";
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import WebSocket from "ws";
import cors from "cors";
import * as xlsx from "xlsx";
import { join } from "path";
import { existsSync } from "fs";

// Types
interface Instrument {
  ticker: string;
  exchange: string;
  exchangeCode: number;
  securityId: string;
}

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

// Constants
const EXCHANGE_SEGMENTS = {
  NSE_EQ: 1,
  NSE_FNO: 2,
  BSE_EQ: 11,
  MCX_COMM: 51,
} as const;

const SUBSCRIPTION_TYPES = {
  ticker: 15,
  quote: 17,
  full: 21,
} as const;

class LightweightMarketFeedServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private ws: WebSocket | null = null;
  private instruments: Instrument[] = [];
  private marketData: Map<string, MarketData> = new Map();
  private subscriptionType: keyof typeof SUBSCRIPTION_TYPES = "quote";
  private accessToken: string;
  private clientId: string;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: { origin: "*", methods: ["GET", "POST"] },
    });

    this.accessToken = process.env.ACCESS_TOKEN || "";
    this.clientId = process.env.CLIENT_ID || "";

    this.loadInstruments();
    this.setupWebServer();
    this.validateConfig();
    this.connectToMarketFeed();
  }

  private loadInstruments() {
    try {
      // Try to load from Excel file first
      const excelPath = join(process.cwd(), "webdata.xlsx");
      if (existsSync(excelPath)) {
        console.log("📊 Loading instruments from Excel file...");
        this.loadFromExcel(excelPath);
      } else {
        console.log("📋 Using default instruments...");
        this.setDefaultInstruments();
      }
    } catch (error) {
      console.error("❌ Error loading instruments:", error);
      this.setDefaultInstruments();
    }
  }

  private loadFromExcel(filePath: string) {
    try {
      const workbook = xlsx.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = xlsx.utils.sheet_to_json(worksheet);

      console.log(`📊 Found ${data.length} rows in Excel file`);

      const instruments: Instrument[] = [];

      data.forEach((row: any, index) => {
        const nseSecurityId = row["NSE_SECURITY_ID"];
        const bseSecurityId = row["BSE_SECURITY_ID"];
        const symbol = row["NSE_SYMBOL"] || row["SYMBOL"];

        if (nseSecurityId && symbol) {
          instruments.push({
            ticker: symbol,
            exchange: "NSE_EQ",
            exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
            securityId: nseSecurityId.toString(),
          });
        }

        if (bseSecurityId && symbol) {
          instruments.push({
            ticker: symbol,
            exchange: "BSE_EQ",
            exchangeCode: EXCHANGE_SEGMENTS.BSE_EQ,
            securityId: bseSecurityId.toString(),
          });
        }

        // Limit to 50 instruments for performance
        if (instruments.length >= 50) return;
      });

      this.instruments = instruments;
      console.log(
        `✅ Loaded ${this.instruments.length} instruments from Excel`
      );

      if (this.instruments.length === 0) {
        console.log("⚠️ No valid instruments found in Excel, using defaults");
        this.setDefaultInstruments();
      }
    } catch (error) {
      console.error("❌ Excel loading failed:", error);
      this.setDefaultInstruments();
    }
  }

  private setDefaultInstruments() {
    this.instruments = [
      {
        ticker: "HDFCBANK",
        exchange: "NSE_EQ",
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "1333",
      },
      {
        ticker: "SBIN",
        exchange: "NSE_EQ",
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "3045",
      },
      {
        ticker: "INFY",
        exchange: "NSE_EQ",
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "1594",
      },
      {
        ticker: "TCS",
        exchange: "NSE_EQ",
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "11536",
      },
    ];
    console.log(`📋 Using ${this.instruments.length} default instruments`);
  }

  private validateConfig() {
    if (!this.instruments.length) {
      console.warn("⚠️ No instruments loaded");
    }
    console.log(
      `✅ Configuration validated - ${this.instruments.length} instruments loaded`
    );
  }

  private setupWebServer() {
    this.app.use(cors());
    this.app.use(express.json());

    // Simple API endpoints
    this.app.get("/api/data", (req, res) => {
      res.json({
        connected: this.ws?.readyState === WebSocket.OPEN,
        instruments: this.instruments.length,
        subscriptionType: this.subscriptionType,
        activeInstruments: this.marketData.size,
        latestData: Array.from(this.marketData.values()).slice(0, 20),
      });
    });

    this.app.get("/api/instruments", (req, res) => {
      res.json({
        instruments: this.instruments.slice(0, 50),
        total: this.instruments.length,
      });
    });

    // Socket.IO setup
    this.io.on("connection", (socket) => {
      console.log("📱 Client connected");

      // Send initial data
      socket.emit("initialData", {
        instruments: this.instruments,
        liveData: Array.from(this.marketData.values()),
      });

      socket.on("disconnect", () => {
        console.log("📱 Client disconnected");
      });
    });
  }

  private connectToMarketFeed() {
    if (!this.accessToken || !this.clientId) {
      console.warn("⚠️ Missing credentials - running in demo mode");
      this.startDemoMode();
      return;
    }

    const wsUrl = `wss://api-feed.dhan.co?version=2&token=${this.accessToken}&clientId=${this.clientId}&authType=2`;
    console.log("🔗 Connecting to Dhan market feed...");

    this.ws = new WebSocket(wsUrl);

    this.ws.on("open", () => {
      console.log("✅ Connected to market feed");
      this.subscribeToInstruments();
    });

    this.ws.on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMarketData(message);
      } catch (error) {
        console.error("❌ Error parsing message:", error);
      }
    });

    this.ws.on("error", (error) => {
      console.error("❌ WebSocket error:", error);
    });

    this.ws.on("close", () => {
      console.log("🔌 WebSocket connection closed");
      setTimeout(() => this.connectToMarketFeed(), 5000);
    });
  }

  private subscribeToInstruments() {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;

    const subscriptionMessage = {
      RequestCode: SUBSCRIPTION_TYPES[this.subscriptionType],
      InstrumentCount: this.instruments.length,
      InstrumentList: this.instruments.map((instrument) => ({
        ExchangeSegment: instrument.exchangeCode,
        SecurityId: instrument.securityId,
      })),
    };

    console.log(`📡 Subscribing to ${this.instruments.length} instruments...`);
    this.ws.send(JSON.stringify(subscriptionMessage));
  }

  private handleMarketData(message: any) {
    if (message.type === "quote" && message.data) {
      const data = message.data;
      const instrument = this.instruments.find(
        (inst) => inst.securityId === data.security_id?.toString()
      );

      if (instrument) {
        const marketData: MarketData = {
          ticker: instrument.ticker,
          securityId: instrument.securityId,
          exchange: instrument.exchange,
          exchangeCode: instrument.exchangeCode,
          ltp: data.LTP || 0,
          change: data.change || 0,
          changePercent: data.change_percent || 0,
          volume: data.volume || 0,
          high: data.high || 0,
          low: data.low || 0,
          open: data.open || 0,
          close: data.prev_close || 0,
          timestamp: Date.now(),
        };

        this.marketData.set(instrument.securityId, marketData);
        this.io.emit("marketData", marketData);
      }
    }
  }

  private startDemoMode() {
    console.log("🎭 Starting demo mode with mock data...");

    setInterval(() => {
      this.instruments.forEach((instrument) => {
        const basePrice = 1000 + Math.random() * 2000;
        const change = (Math.random() - 0.5) * 100;
        const changePercent = (change / basePrice) * 100;

        const marketData: MarketData = {
          ticker: instrument.ticker,
          securityId: instrument.securityId,
          exchange: instrument.exchange,
          exchangeCode: instrument.exchangeCode,
          ltp: basePrice + change,
          change: change,
          changePercent: changePercent,
          volume: Math.floor(Math.random() * 1000000),
          high: basePrice + Math.abs(change) + 50,
          low: basePrice - Math.abs(change) - 50,
          open: basePrice,
          close: basePrice,
          timestamp: Date.now(),
        };

        this.marketData.set(instrument.securityId, marketData);
        this.io.emit("marketData", marketData);
      });
    }, 2000);
  }

  public start() {
    const port = process.env.PORT || 8080;
    this.server.listen(port, () => {
      console.log(`🚀 Lightweight server running on port ${port}`);
    });
  }
}

// Start the server
const server = new LightweightMarketFeedServer();
server.start();
