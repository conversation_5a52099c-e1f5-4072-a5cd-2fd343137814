import express from "express";
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import WebSocket from "ws";
import cors from "cors";
import * as xlsx from "xlsx";
import { join } from "path";
import { existsSync } from "fs";
import { config } from "dotenv";

// Load environment variables
config();

// Types
interface Instrument {
  ticker: string;
  exchange: string;
  exchangeCode: number;
  securityId: string;
}

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

// Constants
const EXCHANGE_SEGMENTS = {
  NSE_EQ: 1,
  NSE_FNO: 2,
  BSE_EQ: 11,
  MCX_COMM: 51,
} as const;

const SUBSCRIPTION_TYPES = {
  ticker: 15,
  quote: 17,
  full: 21,
} as const;

class LightweightMarketFeedServer {
  private app: express.Application;
  private server: any;
  private io: SocketIOServer;
  private ws: WebSocket | null = null;
  private instruments: Instrument[] = [];
  private marketData: Map<string, MarketData> = new Map();
  private subscriptionType: keyof typeof SUBSCRIPTION_TYPES = "quote";
  private accessToken: string;
  private clientId: string;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: { origin: "*", methods: ["GET", "POST"] },
    });

    this.accessToken = process.env.ACCESS_TOKEN || "";
    this.clientId = process.env.CLIENT_ID || "";

    this.loadInstruments();
    this.setupWebServer();
    this.validateConfig();
    this.connectToMarketFeed();
  }

  private loadInstruments() {
    try {
      // Try to load from Excel file first
      const excelPath = join(process.cwd(), "webdata.xlsx");
      if (existsSync(excelPath)) {
        console.log("📊 Loading instruments from Excel file...");
        this.loadFromExcel(excelPath);
      } else {
        console.log("📋 Using default instruments...");
        this.setDefaultInstruments();
      }
    } catch (error) {
      console.error("❌ Error loading instruments:", error);
      this.setDefaultInstruments();
    }
  }

  private loadFromExcel(filePath: string) {
    try {
      const workbook = xlsx.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = xlsx.utils.sheet_to_json(worksheet);

      console.log(`📊 Found ${data.length} rows in Excel file`);

      // Debug: Check first row to see column names
      if (data.length > 0) {
        console.log("📋 Excel columns:", Object.keys(data[0]));
      }

      const instruments: Instrument[] = [];

      // Use the actual column names from the Excel file
      data.forEach((row: any, index) => {
        const ticker = row["Ticker"];
        const securityId = row["SecurityId"];
        const exchangeSegment = row["ExchangeSegment"];

        // Only add NSE_EQ instruments for better performance
        if (ticker && securityId && exchangeSegment === "NSE_EQ") {
          instruments.push({
            ticker: ticker.toString().trim(),
            exchange: "NSE_EQ",
            exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
            securityId: securityId.toString().trim(),
          });
        }

        // Limit to 100 instruments for good performance
        if (instruments.length >= 100) return;
      });

      this.instruments = instruments;
      console.log(
        `✅ Loaded ${this.instruments.length} instruments from Excel`
      );

      if (this.instruments.length === 0) {
        console.log("⚠️ No valid instruments found in Excel, using defaults");
        this.setDefaultInstruments();
      }
    } catch (error) {
      console.error("❌ Excel loading failed:", error);
      this.setDefaultInstruments();
    }
  }

  private setDefaultInstruments() {
    this.instruments = [
      {
        ticker: "HDFCBANK",
        exchange: "NSE_EQ",
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "1333",
      },
      {
        ticker: "SBIN",
        exchange: "NSE_EQ",
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "3045",
      },
      {
        ticker: "INFY",
        exchange: "NSE_EQ",
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "1594",
      },
      {
        ticker: "TCS",
        exchange: "NSE_EQ",
        exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
        securityId: "11536",
      },
    ];
    console.log(`📋 Using ${this.instruments.length} default instruments`);
  }

  private validateConfig() {
    if (!this.instruments.length) {
      console.warn("⚠️ No instruments loaded");
    }
    console.log(
      `✅ Configuration validated - ${this.instruments.length} instruments loaded`
    );
  }

  private setupWebServer() {
    this.app.use(cors());
    this.app.use(express.json());

    // Simple API endpoints
    this.app.get("/api/data", (req, res) => {
      res.json({
        connected: this.ws?.readyState === WebSocket.OPEN,
        instruments: this.instruments.length,
        subscriptionType: this.subscriptionType,
        activeInstruments: this.marketData.size,
        latestData: Array.from(this.marketData.values()).slice(0, 20),
      });
    });

    this.app.get("/api/instruments", (req, res) => {
      res.json({
        instruments: this.instruments.slice(0, 50),
        total: this.instruments.length,
      });
    });

    // Socket.IO setup
    this.io.on("connection", (socket) => {
      console.log("📱 Client connected");

      // Send initial data
      socket.emit("initialData", {
        instruments: this.instruments,
        liveData: Array.from(this.marketData.values()),
      });

      socket.on("disconnect", () => {
        console.log("📱 Client disconnected");
      });
    });
  }

  private connectToMarketFeed() {
    // Always try to connect to real Dhan API first
    this.accessToken = process.env.ACCESS_TOKEN || "";
    this.clientId = process.env.CLIENT_ID || "";

    if (!this.accessToken || !this.clientId) {
      console.warn("⚠️ Missing credentials in .env file");
      console.log("📋 Please add ACCESS_TOKEN and CLIENT_ID to .env file");
      console.log("🔄 Attempting connection anyway...");
    }

    // Use real credentials from .env or fallback
    const token = this.accessToken || "YOUR_ACCESS_TOKEN";
    const clientId = this.clientId || "1100232369";

    const wsUrl = `wss://api-feed.dhan.co?version=2&token=${token}&clientId=${clientId}&authType=2`;
    console.log("🔗 Connecting to Dhan market feed...");
    console.log(`📡 Using Client ID: ${clientId}`);

    this.ws = new WebSocket(wsUrl);

    this.ws.on("open", () => {
      console.log("✅ Connected to market feed");
      this.subscribeToInstruments();
    });

    this.ws.on("message", (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMarketData(message);
      } catch (error) {
        console.error("❌ Error parsing message:", error);
      }
    });

    this.ws.on("error", (error) => {
      console.error("❌ WebSocket error:", error);
      console.log("🔄 Will retry connection in 5 seconds...");
    });

    this.ws.on("close", (code, reason) => {
      console.log(`🔌 WebSocket connection closed: ${code} - ${reason}`);
      console.log("🔄 Reconnecting in 5 seconds...");
      setTimeout(() => this.connectToMarketFeed(), 5000);
    });
  }

  private subscribeToInstruments() {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;

    const subscriptionMessage = {
      RequestCode: SUBSCRIPTION_TYPES[this.subscriptionType],
      InstrumentCount: this.instruments.length,
      InstrumentList: this.instruments.map((instrument) => ({
        ExchangeSegment: instrument.exchangeCode,
        SecurityId: instrument.securityId,
      })),
    };

    console.log(`📡 Subscribing to ${this.instruments.length} instruments...`);
    this.ws.send(JSON.stringify(subscriptionMessage));
  }

  private handleMarketData(message: any) {
    try {
      // Handle different message formats from Dhan API
      let data = message;

      // If it's wrapped in a type/data structure
      if (message.type && message.data) {
        data = message.data;
      }

      // Look for security ID in various possible fields
      const securityId =
        data.security_id || data.SecurityId || data.securityId || data.SI;

      if (securityId) {
        const instrument = this.instruments.find(
          (inst) => inst.securityId === securityId.toString()
        );

        if (instrument) {
          // Parse market data from Dhan API format
          const ltp = data.LTP || data.ltp || data.LastPrice || 0;
          const prevClose =
            data.prev_close || data.PrevClose || data.close || ltp;
          const change = ltp - prevClose;
          const changePercent = prevClose > 0 ? (change / prevClose) * 100 : 0;

          const marketData: MarketData = {
            ticker: instrument.ticker,
            securityId: instrument.securityId,
            exchange: instrument.exchange,
            exchangeCode: instrument.exchangeCode,
            ltp: ltp,
            change: change,
            changePercent: changePercent,
            volume: data.volume || data.Volume || data.TotalTradedQuantity || 0,
            high: data.high || data.High || data.DayHigh || ltp,
            low: data.low || data.Low || data.DayLow || ltp,
            open: data.open || data.Open || data.DayOpen || ltp,
            close: prevClose,
            timestamp: Date.now(),
          };

          this.marketData.set(instrument.securityId, marketData);
          this.io.emit("marketData", marketData);

          console.log(
            `📈 ${instrument.ticker}: ₹${ltp} (${change >= 0 ? "+" : ""}${change.toFixed(2)})`
          );
        }
      }
    } catch (error) {
      console.error("❌ Error handling market data:", error);
    }
  }

  public start() {
    const port = process.env.PORT || 8080;
    this.server.listen(port, () => {
      console.log(`🚀 Lightweight server running on port ${port}`);
    });
  }
}

// Start the server
const server = new LightweightMarketFeedServer();
server.start();
