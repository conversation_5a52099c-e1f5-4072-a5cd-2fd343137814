// ============================================================================
// MOCK DATA GENERATOR - Realistic Market Data Simulation
// ============================================================================

import { MarketData, Instrument } from '@/types';

export class MockDataGenerator {
  private baseValues: Map<string, number> = new Map();
  private lastValues: Map<string, number> = new Map();
  private volatility: Map<string, number> = new Map();

  constructor() {
    this.initializeBaseValues();
  }

  /**
   * Initialize realistic base values for common indices
   */
  private initializeBaseValues(): void {
    const indexBaseValues = {
      // BSE Indices (actual available in server)
      'SENSEX': 82000,
      'BSE100': 18500,
      'BSE200': 12800,
      'BSE500': 28500,
      'MIDCAP': 45000,
      'BANKEX': 52000,
      'AUTO': 35000,
      'IT': 38000,
      'FMCG': 18500,
      'METAL': 25000,
      'REALTY': 4500,
      'HC': 28000,
      'POWER': 5800,
      'FIN': 24000,
      'BSEPBI': 48000,
      'SENSEX50': 28000,
      
      // Default for unknown indices
      'DEFAULT': 15000
    };

    const volatilityMap = {
      'SENSEX': 0.8,
      'BSE100': 1.0,
      'BSE200': 1.2,
      'BSE500': 1.1,
      'MIDCAP': 1.8,
      'BANKEX': 1.5,
      'AUTO': 2.0,
      'IT': 1.3,
      'FMCG': 0.9,
      'METAL': 2.5,
      'REALTY': 3.0,
      'HC': 1.4,
      'POWER': 2.2,
      'FIN': 1.6,
      'BSEPBI': 1.7,
      'SENSEX50': 1.4,
      'DEFAULT': 1.5
    };

    Object.entries(indexBaseValues).forEach(([key, value]) => {
      this.baseValues.set(key, value);
      this.lastValues.set(key, value);
      this.volatility.set(key, volatilityMap[key] || 1.5);
    });
  }

  /**
   * Get base value for an instrument
   */
  private getBaseValue(ticker: string): number {
    // Try exact match first
    if (this.baseValues.has(ticker)) {
      return this.baseValues.get(ticker)!;
    }

    // Try partial matches for common patterns
    const upperTicker = ticker.toUpperCase();
    for (const [key, value] of this.baseValues.entries()) {
      if (upperTicker.includes(key) || key.includes(upperTicker)) {
        return value;
      }
    }

    // Generate consistent value based on ticker hash
    let hash = 0;
    for (let i = 0; i < ticker.length; i++) {
      hash = ((hash << 5) - hash + ticker.charCodeAt(i)) & 0xffffffff;
    }
    const baseValue = Math.abs(hash % 50000) + 5000; // 5K to 55K range
    
    this.baseValues.set(ticker, baseValue);
    this.lastValues.set(ticker, baseValue);
    this.volatility.set(ticker, 1.5);
    
    return baseValue;
  }

  /**
   * Get volatility for an instrument
   */
  private getVolatility(ticker: string): number {
    return this.volatility.get(ticker) || 1.5;
  }

  /**
   * Generate realistic market data for a single instrument
   */
  generateMarketData(instrument: Instrument): MarketData {
    const baseValue = this.getBaseValue(instrument.ticker);
    const lastValue = this.lastValues.get(instrument.ticker) || baseValue;
    const volatilityFactor = this.getVolatility(instrument.ticker);

    // Generate realistic price movement
    const maxChangePercent = 0.5 * volatilityFactor; // Max 0.5% * volatility per update
    const changePercent = (Math.random() - 0.5) * maxChangePercent * 2;
    const change = (lastValue * changePercent) / 100;
    const newLtp = Math.max(baseValue * 0.8, lastValue + change); // Don't go below 80% of base

    // Calculate total change from base (day's change)
    const totalChange = newLtp - baseValue;
    const totalChangePercent = (totalChange / baseValue) * 100;

    // Generate OHLC data
    const high = Math.max(newLtp, lastValue) + (Math.random() * newLtp * 0.002);
    const low = Math.min(newLtp, lastValue) - (Math.random() * newLtp * 0.002);
    const open = baseValue + (Math.random() - 0.5) * (baseValue * 0.01);

    // Generate volume (higher for more volatile stocks)
    const baseVolume = 100000;
    const volumeMultiplier = 1 + (Math.abs(totalChangePercent) * 0.1);
    const volume = Math.floor(baseVolume * volumeMultiplier * (0.5 + Math.random()));

    // Update last value for next iteration
    this.lastValues.set(instrument.ticker, newLtp);

    const marketData: MarketData = {
      ticker: instrument.ticker,
      securityId: instrument.securityId.toString(),
      exchange: instrument.exchange,
      exchangeCode: instrument.exchangeCode,
      ltp: Math.round(newLtp * 100) / 100,
      change: Math.round(totalChange * 100) / 100,
      changePercent: Math.round(totalChangePercent * 100) / 100,
      volume: volume,
      high: Math.round(high * 100) / 100,
      low: Math.round(low * 100) / 100,
      open: Math.round(open * 100) / 100,
      close: baseValue, // Previous day's close
      timestamp: Date.now()
    };

    return marketData;
  }

  /**
   * Generate market data for multiple instruments
   */
  generateBatchMarketData(instruments: Instrument[]): MarketData[] {
    return instruments.map(instrument => this.generateMarketData(instrument));
  }

  /**
   * Reset all values to base (simulate market open)
   */
  resetToBase(): void {
    for (const [ticker, baseValue] of this.baseValues.entries()) {
      this.lastValues.set(ticker, baseValue);
    }
  }

  /**
   * Simulate market volatility event (sudden price movements)
   */
  simulateVolatilityEvent(intensity: number = 1.0): void {
    for (const [ticker, lastValue] of this.lastValues.entries()) {
      const volatility = this.getVolatility(ticker);
      const maxChange = lastValue * 0.05 * intensity * volatility; // Up to 5% change
      const change = (Math.random() - 0.5) * maxChange * 2;
      const newValue = Math.max(lastValue * 0.7, lastValue + change);
      this.lastValues.set(ticker, newValue);
    }
  }

  /**
   * Get current statistics
   */
  getStats(): {
    totalInstruments: number;
    avgPrice: number;
    avgVolatility: number;
    priceRange: { min: number; max: number };
  } {
    const values = Array.from(this.lastValues.values());
    const volatilities = Array.from(this.volatility.values());

    return {
      totalInstruments: values.length,
      avgPrice: values.reduce((sum, val) => sum + val, 0) / values.length,
      avgVolatility: volatilities.reduce((sum, val) => sum + val, 0) / volatilities.length,
      priceRange: {
        min: Math.min(...values),
        max: Math.max(...values)
      }
    };
  }

  /**
   * Update base value for an instrument (simulate corporate actions)
   */
  updateBaseValue(ticker: string, newBaseValue: number): void {
    this.baseValues.set(ticker, newBaseValue);
    this.lastValues.set(ticker, newBaseValue);
  }

  /**
   * Set volatility for an instrument
   */
  setVolatility(ticker: string, volatility: number): void {
    this.volatility.set(ticker, Math.max(0.1, Math.min(5.0, volatility)));
  }
}

// Singleton instance
export const mockDataGenerator = new MockDataGenerator();

export default MockDataGenerator;
