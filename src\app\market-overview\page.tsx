"use client";

import React, { useState, useEffect } from "react";
import DetailedStocksView from "../../components/DetailedStocksView";
import KeyIndices from "../../components/KeyIndices";
import Link from "next/link";

interface MarketData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

interface ApiResponse {
  connected: boolean;
  instruments: number;
  subscriptionType: string;
  latestData: MarketData[];
  totalInstruments: number;
  activeInstruments: number;
}

const MarketOverviewPage: React.FC = () => {
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean;
    instruments: number;
    activeInstruments: number;
  }>({
    connected: false,
    instruments: 0,
    activeInstruments: 0,
  });

  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        if (!loading) setLoading(true);
        const response = await fetch(
          "http://localhost:8080/api/data?limit=1000"
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data: ApiResponse = await response.json();

        setMarketData(data.latestData || []);
        setConnectionStatus({
          connected: data.connected,
          instruments: data.instruments,
          activeInstruments: data.activeInstruments,
        });
        setError(null);
      } catch (err) {
        console.error("Error fetching market data:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch market data"
        );

        // Don't set any mock data - let it remain empty if API fails
        setMarketData([]);
        setConnectionStatus({
          connected: false,
          instruments: 100,
          activeInstruments: 100,
        });
      } finally {
        setLoading(false);
      }
    };

    // Initial fetch
    fetchMarketData();

    // Set up periodic refresh every 5 seconds for real-time updates
    const interval = setInterval(() => {
      fetchMarketData();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading market data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Navigation Header */}
      <div className="bg-gray-900 border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-blue-400 hover:text-blue-300 transition-colors flex items-center space-x-2"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 19l-7-7m0 0l7-7m-7 7h18"
                  />
                </svg>
                <span>Back to Dashboard</span>
              </Link>
              <div className="text-gray-400">|</div>
              <h1 className="text-xl font-semibold text-white">
                Market Overview
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div
                  className={`w-2 h-2 rounded-full ${
                    connectionStatus.connected ? "bg-green-500" : "bg-red-500"
                  }`}
                ></div>
                <span className="text-sm text-gray-400">
                  {connectionStatus.connected ? "Connected" : "Disconnected"}
                </span>
              </div>
              <div className="text-sm text-gray-400">
                {connectionStatus.activeInstruments} /{" "}
                {connectionStatus.instruments} instruments
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-yellow-900 border-l-4 border-yellow-500 p-4 mx-6 mt-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-300">
                Unable to connect to live market data. Please ensure the Dhan
                WebSocket server is running on port 8080.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Key Indices Sidebar */}
        <div className="lg:col-span-1">
          <KeyIndices />
        </div>

        {/* Detailed Stocks View */}
        <div className="lg:col-span-3">
          <DetailedStocksView data={marketData} />
        </div>
      </div>
    </div>
  );
};

export default MarketOverviewPage;
