"use client";

import React, { useEffect, useState, useRef } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { io, Socket } from "socket.io-client";

interface StockDetail {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
  // Additional calculated fields
  dayRange: string;
  weekRange52: string;
  marketCap: string;
  sector: string;
  industry: string;
}

interface CompanyInfo {
  id: number;
  company_name: string;
  nse_symbol?: string;
  bse_symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_name?: string;
  isin_no?: string;
}

interface PriceHistory {
  price: number;
  timestamp: number;
}

export default function StockDetailPage() {
  const params = useParams();
  const router = useRouter();
  const ticker = params.ticker as string;

  const [stockData, setStockData] = useState<StockDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [priceHistory, setPriceHistory] = useState<PriceHistory[]>([]);
  const [lastUpdate, setLastUpdate] = useState<string>("Never");
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [selectedExchange, setSelectedExchange] = useState<"NSE" | "BSE">(
    "NSE"
  );

  const maxHistoryPoints = 50;

  // Fetch company information
  useEffect(() => {
    if (!ticker) return;

    const fetchCompanyInfo = async () => {
      try {
        const response = await fetch(`/api/companies/${ticker}`);
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setCompanyInfo(data.data);
          }
        }
      } catch (err) {
        console.error("Error fetching company info:", err);
      }
    };

    fetchCompanyInfo();
  }, [ticker]);

  useEffect(() => {
    if (!ticker) return;

    const fetchStockDetail = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `http://localhost:8080/api/stock/${ticker}`
        );

        if (!response.ok) {
          throw new Error("Stock not found");
        }

        const data = await response.json();
        setStockData(data);

        // Initialize price history
        setPriceHistory([
          {
            price: data.ltp,
            timestamp: Date.now(),
          },
        ]);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch stock data"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchStockDetail();
  }, [ticker]);

  // Real-time WebSocket connection
  useEffect(() => {
    if (!stockData || !companyInfo) return;

    // Skip if company doesn't have both exchange security IDs
    if (
      !companyInfo.nse_security_id ||
      !companyInfo.bse_security_id ||
      companyInfo.nse_security_id === "-" ||
      companyInfo.bse_security_id === "-"
    ) {
      return;
    }

    const socketInstance = io("http://localhost:8080", {
      transports: ["websocket", "polling"],
    });

    socketInstance.on("connect", () => {
      console.log("Connected to real-time feed");
      setIsConnected(true);
    });

    socketInstance.on("disconnect", () => {
      console.log("Disconnected from real-time feed");
      setIsConnected(false);
    });

    // Handle individual market data updates
    socketInstance.on("marketData", (data: any) => {
      if (data.ticker === ticker) {
        updateStockData(data);
      }
    });

    // Handle batch market data updates
    socketInstance.on("marketDataBatch", (batch: any[]) => {
      const relevantData = batch.find((item) => item.ticker === ticker);
      if (relevantData) {
        updateStockData(relevantData);
      }
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, [stockData, ticker, companyInfo, selectedExchange]);

  const updateStockData = (newData: any) => {
    setStockData((prev) => {
      if (!prev) return null;

      const updatedData = {
        ...prev,
        ltp: newData.ltp || prev.ltp,
        change: newData.change || prev.change,
        changePercent: newData.changePercent || prev.changePercent,
        volume: newData.volume || prev.volume,
        high: newData.high || prev.high,
        low: newData.low || prev.low,
        open: newData.open || prev.open,
        close: newData.close || prev.close,
        timestamp: newData.timestamp || Date.now(),
      };

      // Update price history
      setPriceHistory((prev) => {
        const newHistory = [
          ...prev,
          {
            price: updatedData.ltp,
            timestamp: Date.now(),
          },
        ];
        return newHistory.slice(-maxHistoryPoints);
      });

      setLastUpdate(new Date().toLocaleTimeString());
      return updatedData;
    });
  };

  // Helper function to get price trend
  const getPriceTrend = () => {
    if (priceHistory.length < 2) return "neutral";
    const recent = priceHistory.slice(-5);
    const trend = recent[recent.length - 1].price - recent[0].price;
    return trend > 0 ? "up" : trend < 0 ? "down" : "neutral";
  };

  // Helper function to format volume
  const formatVolume = (volume: number): string => {
    if (volume >= 10000000) {
      return `${(volume / 10000000).toFixed(1)}Cr`;
    } else if (volume >= 100000) {
      return `${(volume / 100000).toFixed(1)}L`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading stock details...</p>
        </div>
      </div>
    );
  }

  if (error || !stockData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">
            Stock Not Found
          </h1>
          <p className="text-gray-600 mb-6">
            {error || "The requested stock could not be found."}
          </p>
          <Link
            href="/"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  const isPositive = stockData.change >= 0;
  const changeColor = isPositive ? "text-green-600" : "text-red-600";
  const changeBgColor = isPositive ? "bg-green-50" : "bg-red-50";
  const changeIcon = isPositive ? "+" : "";
  const priceTrend = getPriceTrend();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Enhanced Header */}
      <div className="bg-white shadow-lg border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-6">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
            <Link
              href="/"
              className="hover:text-blue-600 transition-colors flex items-center"
            >
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              Home
            </Link>
            <span>/</span>
            <span>All Stocks</span>
            <span>/</span>
            <span className="text-gray-900 font-medium">
              {stockData.ticker}
            </span>
          </nav>

          {/* Stock Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-4 lg:mb-0">
              <div className="flex items-center space-x-4 mb-3">
                <h1 className="text-4xl font-bold text-gray-900">
                  {stockData.ticker}
                </h1>
                <div className="flex items-center space-x-2">
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      isConnected
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    <div
                      className={`w-2 h-2 rounded-full mr-2 ${
                        isConnected ? "bg-green-400" : "bg-red-400"
                      }`}
                    ></div>
                    {isConnected ? "Live" : "Offline"}
                  </span>
                  {priceTrend !== "neutral" && (
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        priceTrend === "up"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {priceTrend === "up" ? "↗" : "↘"} Trending {priceTrend}
                    </span>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  {stockData.exchange}
                </span>
                {companyInfo &&
                  companyInfo.nse_security_id &&
                  companyInfo.bse_security_id &&
                  companyInfo.nse_security_id !== "-" &&
                  companyInfo.bse_security_id !== "-" && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Exchange:</span>
                      <div className="flex rounded-lg border">
                        <button
                          type="button"
                          onClick={() => setSelectedExchange("NSE")}
                          className={`px-3 py-1 text-sm font-medium rounded-l-lg transition-colors ${
                            selectedExchange === "NSE"
                              ? "bg-blue-600 text-white"
                              : "bg-white text-gray-700 hover:bg-gray-50"
                          }`}
                        >
                          NSE
                        </button>
                        <button
                          type="button"
                          onClick={() => setSelectedExchange("BSE")}
                          className={`px-3 py-1 text-sm font-medium rounded-r-lg transition-colors ${
                            selectedExchange === "BSE"
                              ? "bg-blue-600 text-white"
                              : "bg-white text-gray-700 hover:bg-gray-50"
                          }`}
                        >
                          BSE
                        </button>
                      </div>
                    </div>
                  )}
                <span className="text-gray-600">
                  {companyInfo?.sector_name || stockData.sector || "Technology"}{" "}
                  |{" "}
                  {companyInfo?.industry_name ||
                    stockData.industry ||
                    "IT Services"}
                </span>
                <span className="text-xs text-gray-500">
                  Last updated: {lastUpdate}
                </span>
              </div>
            </div>

            {/* Price Display */}
            <div className="text-right">
              <div className="text-5xl font-bold text-gray-900 mb-2">
                ₹{stockData.ltp.toFixed(2)}
              </div>
              <div
                className={`inline-flex items-center px-4 py-2 rounded-lg text-lg font-semibold ${changeBgColor} ${changeColor}`}
              >
                <span className="mr-2">
                  {isPositive ? (
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </span>
                {changeIcon}
                {stockData.change.toFixed(2)} ({changeIcon}
                {stockData.changePercent.toFixed(2)}%)
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8 space-y-8">
        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Price Card */}
          <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Current Price
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  ₹{stockData.ltp.toFixed(2)}
                </p>
              </div>
              <div className={`p-3 rounded-full ${changeBgColor}`}>
                <svg
                  className={`w-6 h-6 ${changeColor}`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d={
                      isPositive
                        ? "M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z"
                        : "M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z"
                    }
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Volume Card */}
          <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Volume</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatVolume(stockData.volume)}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-50">
                <svg
                  className="w-6 h-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* High Card */}
          <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Day High</p>
                <p className="text-2xl font-bold text-gray-900">
                  ₹{stockData.high.toFixed(2)}
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-50">
                <svg
                  className="w-6 h-6 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Low Card */}
          <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Day Low</p>
                <p className="text-2xl font-bold text-gray-900">
                  ₹{stockData.low.toFixed(2)}
                </p>
              </div>
              <div className="p-3 rounded-full bg-red-50">
                <svg
                  className="w-6 h-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Performance Section */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900">
              {stockData.ticker} Performance
            </h2>
            <div className="flex items-center space-x-4">
              <span
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  isConnected
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                <div
                  className={`w-2 h-2 rounded-full mr-2 ${
                    isConnected ? "bg-green-400 animate-pulse" : "bg-red-400"
                  }`}
                ></div>
                {isConnected ? "Live Data" : "Offline"}
              </span>
            </div>
          </div>

          {/* Enhanced Day's Range Visualization */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-700">
                Day's Range
              </h3>
              <span className="text-sm text-gray-500">
                Current: ₹{stockData.ltp.toFixed(2)}
              </span>
            </div>
            <div className="relative">
              <div className="flex justify-between text-sm font-medium text-gray-600 mb-3">
                <span className="bg-red-50 text-red-700 px-3 py-1 rounded-full">
                  Low: ₹{stockData.low.toFixed(2)}
                </span>
                <span className="bg-green-50 text-green-700 px-3 py-1 rounded-full">
                  High: ₹{stockData.high.toFixed(2)}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-4 relative overflow-hidden">
                <div className="bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 h-4 rounded-full w-full"></div>
                <div
                  className="absolute top-0 w-4 h-4 bg-blue-600 rounded-full shadow-lg border-2 border-white transform -translate-x-2"
                  style={{
                    left: `${Math.min(
                      Math.max(
                        ((stockData.ltp - stockData.low) /
                          (stockData.high - stockData.low)) *
                          100,
                        0
                      ),
                      100
                    )}%`,
                  }}
                >
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                    ₹{stockData.ltp.toFixed(2)}
                  </div>
                </div>
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-2">
                <span>0%</span>
                <span>50%</span>
                <span>100%</span>
              </div>
            </div>
          </div>

          {/* Enhanced Key Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-blue-900 mb-4">
                Trading Info
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-blue-700">Previous Close</span>
                  <span className="font-bold text-blue-900">
                    ₹{stockData.close.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-blue-700">Open</span>
                  <span className="font-bold text-blue-900">
                    ₹{stockData.open.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-blue-700">Volume</span>
                  <span className="font-bold text-blue-900">
                    {formatVolume(stockData.volume)}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-green-900 mb-4">
                Price Range
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-green-700">Day's Range</span>
                  <span className="font-bold text-green-900">
                    ₹{stockData.low.toFixed(2)} - ₹{stockData.high.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-green-700">52W Range</span>
                  <span className="font-bold text-green-900">
                    {stockData.weekRange52 || "₹1,114.85 - ₹1,608.80"}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-green-700">Market Cap</span>
                  <span className="font-bold text-green-900">
                    {stockData.marketCap || "₹19,18,092.14 Cr"}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-purple-900 mb-4">
                Live Stats
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-purple-700">Price Updates</span>
                  <span className="font-bold text-purple-900">
                    {priceHistory.length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-purple-700">Trend</span>
                  <span
                    className={`font-bold ${
                      priceTrend === "up"
                        ? "text-green-600"
                        : priceTrend === "down"
                          ? "text-red-600"
                          : "text-gray-600"
                    }`}
                  >
                    {priceTrend === "up"
                      ? "↗ Bullish"
                      : priceTrend === "down"
                        ? "↘ Bearish"
                        : "→ Neutral"}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-purple-700">Last Update</span>
                  <span className="font-bold text-purple-900">
                    {lastUpdate}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Additional Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Technical Details */}
          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-indigo-500">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-indigo-100 rounded-lg mr-3">
                <svg
                  className="w-6 h-6 text-indigo-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900">
                Technical Details
              </h3>
            </div>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600 font-medium">Security ID</span>
                <span className="font-bold text-gray-900">
                  {stockData.securityId}
                </span>
              </div>
              {companyInfo &&
                companyInfo.nse_security_id &&
                companyInfo.bse_security_id &&
                companyInfo.nse_security_id !== "-" &&
                companyInfo.bse_security_id !== "-" && (
                  <>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600 font-medium">
                        NSE Security ID
                      </span>
                      <span className="font-bold text-gray-900">
                        {companyInfo.nse_security_id}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600 font-medium">
                        BSE Security ID
                      </span>
                      <span className="font-bold text-gray-900">
                        {companyInfo.bse_security_id}
                      </span>
                    </div>
                  </>
                )}
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600 font-medium">Exchange</span>
                <span className="font-bold text-gray-900">
                  {selectedExchange} ({stockData.exchange})
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600 font-medium">Sector</span>
                <span className="font-bold text-gray-900">
                  {stockData.sector || "Technology"}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600 font-medium">Industry</span>
                <span className="font-bold text-gray-900">
                  {stockData.industry || "IT Services"}
                </span>
              </div>
            </div>
          </div>

          {/* Live Data Status */}
          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-green-500">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-green-100 rounded-lg mr-3">
                <svg
                  className="w-6 h-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900">Live Data</h3>
            </div>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600 font-medium">Connection</span>
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    isConnected
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  <div
                    className={`w-2 h-2 rounded-full mr-2 ${
                      isConnected ? "bg-green-400 animate-pulse" : "bg-red-400"
                    }`}
                  ></div>
                  {isConnected ? "Live" : "Offline"}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600 font-medium">
                  Updates Received
                </span>
                <span className="font-bold text-gray-900">
                  {priceHistory.length}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600 font-medium">Last Update</span>
                <span className="font-bold text-gray-900">{lastUpdate}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-gray-600 font-medium">Data Source</span>
                <span className="font-bold text-gray-900">Dhan API</span>
              </div>
            </div>
          </div>

          {/* Enhanced Quick Actions */}
          <div className="bg-white rounded-xl shadow-lg p-6 border-t-4 border-purple-500">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-purple-100 rounded-lg mr-3">
                <svg
                  className="w-6 h-6 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900">Quick Actions</h3>
            </div>
            <div className="space-y-4">
              <button
                type="button"
                className="w-full flex items-center justify-center bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
                Add to Watchlist
              </button>
              <button
                type="button"
                className="w-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
                View Chart
              </button>
              <Link
                href="/"
                className="w-full flex items-center justify-center bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 px-4 rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-200 transform hover:scale-105 shadow-lg"
              >
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 19l-7-7m0 0l7-7m-7 7h18"
                  />
                </svg>
                Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
