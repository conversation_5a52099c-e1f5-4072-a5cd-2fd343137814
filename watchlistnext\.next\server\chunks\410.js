exports.id=410,exports.ids=[410],exports.modules={91:(e,t,r)=>{"use strict";let{tokenChars:n}=r(9179);function s(e,t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)}e.exports={format:function(e){return Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>[t].concat(Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,r,i=Object.create(null),o=Object.create(null),a=!1,l=!1,c=!1,u=-1,h=-1,d=-1,f=0;for(;f<e.length;f++)if(h=e.charCodeAt(f),void 0===t)if(-1===d&&1===n[h])-1===u&&(u=f);else if(0!==f&&(32===h||9===h))-1===d&&-1!==u&&(d=f);else if(59===h||44===h){if(-1===u)throw SyntaxError(`Unexpected character at index ${f}`);-1===d&&(d=f);let r=e.slice(u,d);44===h?(s(i,r,o),o=Object.create(null)):t=r,u=d=-1}else throw SyntaxError(`Unexpected character at index ${f}`);else if(void 0===r)if(-1===d&&1===n[h])-1===u&&(u=f);else if(32===h||9===h)-1===d&&-1!==u&&(d=f);else if(59===h||44===h){if(-1===u)throw SyntaxError(`Unexpected character at index ${f}`);-1===d&&(d=f),s(o,e.slice(u,d),!0),44===h&&(s(i,t,o),o=Object.create(null),t=void 0),u=d=-1}else if(61===h&&-1!==u&&-1===d)r=e.slice(u,f),u=d=-1;else throw SyntaxError(`Unexpected character at index ${f}`);else if(l){if(1!==n[h])throw SyntaxError(`Unexpected character at index ${f}`);-1===u?u=f:a||(a=!0),l=!1}else if(c)if(1===n[h])-1===u&&(u=f);else if(34===h&&-1!==u)c=!1,d=f;else if(92===h)l=!0;else throw SyntaxError(`Unexpected character at index ${f}`);else if(34===h&&61===e.charCodeAt(f-1))c=!0;else if(-1===d&&1===n[h])-1===u&&(u=f);else if(-1!==u&&(32===h||9===h))-1===d&&(d=f);else if(59===h||44===h){if(-1===u)throw SyntaxError(`Unexpected character at index ${f}`);-1===d&&(d=f);let n=e.slice(u,d);a&&(n=n.replace(/\\/g,""),a=!1),s(o,r,n),44===h&&(s(i,t,o),o=Object.create(null),t=void 0),r=void 0,u=d=-1}else throw SyntaxError(`Unexpected character at index ${f}`);if(-1===u||c||32===h||9===h)throw SyntaxError("Unexpected end of input");-1===d&&(d=f);let p=e.slice(u,d);return void 0===t?s(i,p,o):(void 0===r?s(o,p,!0):a?s(o,r,p.replace(/\\/g,"")):s(o,r,p),s(i,t,o)),i}}},248:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(7006),s=r(5221),i=r(5180),o=r(9736),a=r(5638),l=r(4969),c=r(9634),u=r(3666),h=r(5967),d=r(9860),f=r(2878);function p(e,t){let{origin:r}=t,p={},g=e.canonicalUrl,y=e.tree;p.preserveCustomHistoryState=!1;let _=(0,u.createEmptyCacheNode)(),m=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);_.lazyData=(0,n.fetchServerResponse)(new URL(g,r),{flightRouterState:[y[0],y[1],y[2],"refetch"],nextUrl:m?e.nextUrl:null});let b=Date.now();return _.lazyData.then(async r=>{let{flightData:n,canonicalUrl:u}=r;if("string"==typeof n)return(0,a.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(_.lazyData=null,n)){let{tree:n,seedData:l,head:d,isRootRender:v}=r;if(!v)return console.log("REFRESH FAILED"),e;let C=(0,i.applyRouterStatePatchToTree)([""],y,n,e.canonicalUrl);if(null===C)return(0,h.handleSegmentMismatch)(e,t,n);if((0,o.isNavigatingToNewRootLayout)(y,C))return(0,a.handleExternalUrl)(e,p,g,e.pushRef.pendingPush);let E=u?(0,s.createHrefFromUrl)(u):void 0;if(u&&(p.canonicalUrl=E),null!==l){let e=l[1],t=l[3];_.rsc=e,_.prefetchRsc=null,_.loading=t,(0,c.fillLazyItemsTillLeafWithHead)(b,_,void 0,n,l,d,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:C,updatedCache:_,includeNextUrl:m,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=_,p.patchedTree=C,y=C}return(0,l.handleMutable)(e,p)},()=>e)}r(6495),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},388:e=>{function t(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}e.exports=function(e,r){r=r||{};var n,s,i,o,a=typeof e;if("string"===a&&e.length>0){var l=e;if(!((l=String(l)).length>100)){var c=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(l);if(c){var u=parseFloat(c[1]);switch((c[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*u;case"weeks":case"week":case"w":return 6048e5*u;case"days":case"day":case"d":return 864e5*u;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*u;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*u;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*u;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return u;default:break}}}return}if("number"===a&&isFinite(e)){return r.long?(s=Math.abs(n=e))>=864e5?t(n,s,864e5,"day"):s>=36e5?t(n,s,36e5,"hour"):s>=6e4?t(n,s,6e4,"minute"):s>=1e3?t(n,s,1e3,"second"):n+" ms":(o=Math.abs(i=e))>=864e5?Math.round(i/864e5)+"d":o>=36e5?Math.round(i/36e5)+"h":o>=6e4?Math.round(i/6e4)+"m":o>=1e3?Math.round(i/1e3)+"s":i+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},402:(e,t,r)=>{"use strict";let n,s=r(1820),i=r(3997),o=r(7782),{env:a}=process;function l(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function c(e,t){if(0===n)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!t&&void 0===n)return 0;let r=n||0;if("dumb"===a.TERM)return r;if("win32"===process.platform){let e=s.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:r;if("TEAMCITY_VERSION"in a)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION);if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){let e=parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:r}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?n=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(n=1),"FORCE_COLOR"in a&&(n="true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(parseInt(a.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return l(c(e,e&&e.isTTY))},stdout:l(c(!0,i.isatty(1))),stderr:l(c(!0,i.isatty(2)))}},547:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},672:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},739:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return u}});let n=r(5221),s=r(5180),i=r(9736),o=r(5638),a=r(5426),l=r(4969),c=r(3666);function u(e,t){let{serverResponse:{flightData:r,canonicalUrl:u},navigatedAt:h}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof r)return(0,o.handleExternalUrl)(e,d,r,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of r){let{segmentPath:r,tree:l}=t,g=(0,s.applyRouterStatePatchToTree)(["",...r],f,l,e.canonicalUrl);if(null===g)return e;if((0,i.isNavigatingToNewRootLayout)(f,g))return(0,o.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let y=u?(0,n.createHrefFromUrl)(u):void 0;y&&(d.canonicalUrl=y);let _=(0,c.createEmptyCacheNode)();(0,a.applyFlightData)(h,p,_,t),d.patchedTree=g,d.cache=_,p=_,f=g}return(0,l.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},897:(e,t,r)=>{e.exports=function(e){function t(e){let r,s,i,o=null;function a(...e){if(!a.enabled)return;let n=Number(new Date);a.diff=n-(r||n),a.prev=r,a.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";s++;let i=t.formatters[n];if("function"==typeof i){let t=e[s];r=i.call(a,t),e.splice(s,1),s--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(s!==t.namespaces&&(s=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,r){let n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function s(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(s),...t.skips.map(s).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),s=n.length;for(r=0;r<s;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let r,n;if("*"===e[e.length-1])return!0;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(388),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},1104:(e,t,r)=>{"use strict";let n,s=r(4075),i=r(2743),o=r(3994),{kStatusCode:a}=r(5091),l=Buffer[Symbol.species],c=Buffer.from([0,0,255,255]),u=Symbol("permessage-deflate"),h=Symbol("total-length"),d=Symbol("callback"),f=Symbol("buffers"),p=Symbol("error");class g{constructor(e,t,r){this._maxPayload=0|r,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,n||(n=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[d];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,r=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!r)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(r.server_no_context_takeover=!0),t.clientNoContextTakeover&&(r.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(r.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?r.client_max_window_bits=t.clientMaxWindowBits:(!0===r.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete r.client_max_window_bits,r}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let r=e[t];if(r.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(r=r[0],"client_max_window_bits"===t){if(!0!==r){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else if("server_max_window_bits"===t){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==r)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=r})}),e}decompress(e,t,r){n.add(n=>{this._decompress(e,t,(e,t)=>{n(),r(e,t)})})}compress(e,t,r){n.add(n=>{this._compress(e,t,(e,t)=>{n(),r(e,t)})})}_decompress(e,t,r){let n=this._isServer?"client":"server";if(!this._inflate){let e=`${n}_max_window_bits`,t="number"!=typeof this.params[e]?s.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=s.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[u]=this,this._inflate[h]=0,this._inflate[f]=[],this._inflate.on("error",m),this._inflate.on("data",_)}this._inflate[d]=r,this._inflate.write(e),t&&this._inflate.write(c),this._inflate.flush(()=>{let e=this._inflate[p];if(e){this._inflate.close(),this._inflate=null,r(e);return}let s=i.concat(this._inflate[f],this._inflate[h]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[h]=0,this._inflate[f]=[],t&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),r(null,s)})}_compress(e,t,r){let n=this._isServer?"server":"client";if(!this._deflate){let e=`${n}_max_window_bits`,t="number"!=typeof this.params[e]?s.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=s.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[h]=0,this._deflate[f]=[],this._deflate.on("data",y)}this._deflate[d]=r,this._deflate.write(e),this._deflate.flush(s.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=i.concat(this._deflate[f],this._deflate[h]);t&&(e=new l(e.buffer,e.byteOffset,e.length-4)),this._deflate[d]=null,this._deflate[h]=0,this._deflate[f]=[],t&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),r(null,e)})}}function y(e){this[f].push(e),this[h]+=e.length}function _(e){if(this[h]+=e.length,this[u]._maxPayload<1||this[h]<=this[u]._maxPayload)return void this[f].push(e);this[p]=RangeError("Max payload size exceeded"),this[p].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[p][a]=1009,this.removeListener("data",_),this.reset()}function m(e){this[u]._inflate=null,e[a]=1007,this[d](e)}e.exports=g},1151:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return m},MissingStaticPage:function(){return _},NormalizeError:function(){return g},PageNotFoundError:function(){return y},SP:function(){return d},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,s=Array(n),i=0;i<n;i++)s[i]=arguments[i];return r||(r=!0,t=e(...s)),t}}let s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>s.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function a(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,f=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class g extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class _ extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class m extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},1340:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(1151),s=r(5574);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,s.hasBasePath)(r.pathname)}catch(e){return!1}}},1566:(e,t,r)=>{"use strict";let{kForOnEventAttribute:n,kListener:s}=r(5091),i=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),l=Symbol("kMessage"),c=Symbol("kReason"),u=Symbol("kTarget"),h=Symbol("kType"),d=Symbol("kWasClean");class f{constructor(e){this[u]=null,this[h]=e}get target(){return this[u]}get type(){return this[h]}}Object.defineProperty(f.prototype,"target",{enumerable:!0}),Object.defineProperty(f.prototype,"type",{enumerable:!0});class p extends f{constructor(e,t={}){super(e),this[i]=void 0===t.code?0:t.code,this[c]=void 0===t.reason?"":t.reason,this[d]=void 0!==t.wasClean&&t.wasClean}get code(){return this[i]}get reason(){return this[c]}get wasClean(){return this[d]}}Object.defineProperty(p.prototype,"code",{enumerable:!0}),Object.defineProperty(p.prototype,"reason",{enumerable:!0}),Object.defineProperty(p.prototype,"wasClean",{enumerable:!0});class g extends f{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[l]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[l]}}Object.defineProperty(g.prototype,"error",{enumerable:!0}),Object.defineProperty(g.prototype,"message",{enumerable:!0});class y extends f{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function _(e,t,r){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,r):e.call(t,r)}Object.defineProperty(y.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:p,ErrorEvent:g,Event:f,EventTarget:{addEventListener(e,t,r={}){let i;for(let i of this.listeners(e))if(!r[n]&&i[s]===t&&!i[n])return;if("message"===e)i=function(e,r){let n=new y("message",{data:r?e:e.toString()});n[u]=this,_(t,this,n)};else if("close"===e)i=function(e,r){let n=new p("close",{code:e,reason:r.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});n[u]=this,_(t,this,n)};else if("error"===e)i=function(e){let r=new g("error",{error:e,message:e.message});r[u]=this,_(t,this,r)};else{if("open"!==e)return;i=function(){let e=new f("open");e[u]=this,_(t,this,e)}}i[n]=!!r[n],i[s]=t,r.once?this.once(e,i):this.on(e,i)},removeEventListener(e,t){for(let r of this.listeners(e))if(r[s]===t&&!r[n]){this.removeListener(e,r);break}}},MessageEvent:y}},1567:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},useLinkStatus:function(){return m}});let n=r(7834),s=r(8625),i=n._(r(4996)),o=r(3613),a=r(3132),l=r(2564),c=r(6304),u=r(1151),h=r(7481);r(7646);let d=r(5292),f=r(1340),p=r(5808);function g(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}function y(e){let t,r,n,[o,y]=(0,i.useOptimistic)(d.IDLE_LINK_STATUS),m=(0,i.useRef)(null),{href:b,as:v,children:C,prefetch:E=null,passHref:w,replace:S,shallow:O,scroll:R,onClick:k,onMouseEnter:T,onTouchStart:x,legacyBehavior:P=!1,onNavigate:N,ref:A,unstable_dynamicOnHover:j,...F}=e;t=C,P&&("string"==typeof t||"number"==typeof t)&&(t=(0,s.jsx)("a",{children:t}));let L=i.default.useContext(a.AppRouterContext),M=!1!==E,U=null===E?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:I,as:D}=i.default.useMemo(()=>{let e=g(b);return{href:e,as:v?g(v):e}},[b,v]);P&&(r=i.default.Children.only(t));let B=P?r&&"object"==typeof r&&r.ref:A,H=i.default.useCallback(e=>(null!==L&&(m.current=(0,d.mountLinkInstance)(e,I,L,U,M,y)),()=>{m.current&&((0,d.unmountLinkForCurrentNavigation)(m.current),m.current=null),(0,d.unmountPrefetchableInstance)(e)}),[M,I,L,U,y]),q={ref:(0,c.useMergedRef)(H,B),onClick(e){P||"function"!=typeof k||k(e),P&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,s,o,a){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){s&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(r||t,s?"replace":"push",null==o||o,n.current)})}}(e,I,D,m,S,R,N))},onMouseEnter(e){P||"function"!=typeof T||T(e),P&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),L&&M&&(0,d.onNavigationIntent)(e.currentTarget,!0===j)},onTouchStart:function(e){P||"function"!=typeof x||x(e),P&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),L&&M&&(0,d.onNavigationIntent)(e.currentTarget,!0===j)}};return(0,u.isAbsoluteUrl)(D)?q.href=D:P&&!w&&("a"!==r.type||"href"in r.props)||(q.href=(0,h.addBasePath)(D)),n=P?i.default.cloneElement(r,q):(0,s.jsx)("a",{...F,...q,children:t}),(0,s.jsx)(_.Provider,{value:o,children:n})}r(3614);let _=(0,i.createContext)(d.IDLE_LINK_STATUS),m=()=>(0,i.useContext)(_);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1706:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(7006),r(5221),r(5180),r(9736),r(5638),r(4969),r(5426),r(3666),r(5967),r(9860);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2058:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return c},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],s=t.parallelRoutes,o=new Map(s);for(let t in n){let r=n[t],a=r[0],l=(0,i.createRouterCacheKey)(a),c=s.get(t);if(void 0!==c){let n=c.get(l);if(void 0!==n){let s=e(n,r),i=new Map(c);i.set(l,s),o.set(t,i)}}}let a=t.rsc,l=_(a)&&"pending"===a.status;return{lazyData:null,rsc:a,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o,navigatedAt:t.navigatedAt}}}});let n=r(5383),s=r(2463),i=r(2245),o=r(9736),a=r(9820),l={route:null,node:null,dynamicRequestTree:null,children:null};function c(e,t,r,o,a,c,d,f,p){return function e(t,r,o,a,c,d,f,p,g,y,_){let m=o[1],b=a[1],v=null!==d?d[2]:null;c||!0===a[4]&&(c=!0);let C=r.parallelRoutes,E=new Map(C),w={},S=null,O=!1,R={};for(let r in b){let o,a=b[r],h=m[r],d=C.get(r),k=null!==v?v[r]:null,T=a[0],x=y.concat([r,T]),P=(0,i.createRouterCacheKey)(T),N=void 0!==h?h[0]:void 0,A=void 0!==d?d.get(P):void 0;if(null!==(o=T===n.DEFAULT_SEGMENT_KEY?void 0!==h?{route:h,node:null,dynamicRequestTree:null,children:null}:u(t,h,a,A,c,void 0!==k?k:null,f,p,x,_):g&&0===Object.keys(a[1]).length?u(t,h,a,A,c,void 0!==k?k:null,f,p,x,_):void 0!==h&&void 0!==N&&(0,s.matchSegment)(T,N)&&void 0!==A&&void 0!==h?e(t,A,h,a,c,k,f,p,g,x,_):u(t,h,a,A,c,void 0!==k?k:null,f,p,x,_))){if(null===o.route)return l;null===S&&(S=new Map),S.set(r,o);let e=o.node;if(null!==e){let t=new Map(d);t.set(P,e),E.set(r,t)}let t=o.route;w[r]=t;let n=o.dynamicRequestTree;null!==n?(O=!0,R[r]=n):R[r]=t}else w[r]=a,R[r]=a}if(null===S)return null;let k={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:E,navigatedAt:t};return{route:h(a,w),node:k,dynamicRequestTree:O?h(a,R):null,children:S}}(e,t,r,o,!1,a,c,d,f,[],p)}function u(e,t,r,n,s,c,u,f,p,g){return!s&&(void 0===t||(0,o.isNavigatingToNewRootLayout)(t,r))?l:function e(t,r,n,s,o,l,c,u){let f,p,g,y,_=r[1],m=0===Object.keys(_).length;if(void 0!==n&&n.navigatedAt+a.DYNAMIC_STALETIME_MS>t)f=n.rsc,p=n.loading,g=n.head,y=n.navigatedAt;else if(null===s)return d(t,r,null,o,l,c,u);else if(f=s[1],p=s[3],g=m?o:null,y=t,s[4]||l&&m)return d(t,r,s,o,l,c,u);let b=null!==s?s[2]:null,v=new Map,C=void 0!==n?n.parallelRoutes:null,E=new Map(C),w={},S=!1;if(m)u.push(c);else for(let r in _){let n=_[r],s=null!==b?b[r]:null,a=null!==C?C.get(r):void 0,h=n[0],d=c.concat([r,h]),f=(0,i.createRouterCacheKey)(h),p=e(t,n,void 0!==a?a.get(f):void 0,s,o,l,d,u);v.set(r,p);let g=p.dynamicRequestTree;null!==g?(S=!0,w[r]=g):w[r]=n;let y=p.node;if(null!==y){let e=new Map;e.set(f,y),E.set(r,e)}}return{route:r,node:{lazyData:null,rsc:f,prefetchRsc:null,head:g,prefetchHead:null,loading:p,parallelRoutes:E,navigatedAt:y},dynamicRequestTree:S?h(r,w):null,children:v}}(e,r,n,c,u,f,p,g)}function h(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function d(e,t,r,n,s,o,a){let l=h(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,r,n,s,o,a,l){let c=r[1],u=null!==n?n[2]:null,h=new Map;for(let r in c){let n=c[r],d=null!==u?u[r]:null,f=n[0],p=a.concat([r,f]),g=(0,i.createRouterCacheKey)(f),y=e(t,n,void 0===d?null:d,s,o,p,l),_=new Map;_.set(g,y),h.set(r,_)}let d=0===h.size;d&&l.push(a);let f=null!==n?n[1]:null,p=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:h,prefetchRsc:void 0!==f?f:null,prefetchHead:d?s:[null,null],loading:void 0!==p?p:null,rsc:m(),head:d?m():null,navigatedAt:t}}(e,t,r,n,s,o,a),dynamicRequestTree:l,children:null}}function f(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:o,head:a}=t;o&&function(e,t,r,n,o){let a=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],i=a.children;if(null!==i){let e=i.get(r);if(void 0!==e){let t=e.route[0];if((0,s.matchSegment)(n,t)){a=e;continue}}}return}!function e(t,r,n,o){if(null===t.dynamicRequestTree)return;let a=t.children,l=t.node;if(null===a){null!==l&&(function e(t,r,n,o,a){let l=r[1],c=n[1],u=o[2],h=t.parallelRoutes;for(let t in l){let r=l[t],n=c[t],o=u[t],d=h.get(t),f=r[0],p=(0,i.createRouterCacheKey)(f),y=void 0!==d?d.get(p):void 0;void 0!==y&&(void 0!==n&&(0,s.matchSegment)(f,n[0])&&null!=o?e(y,r,n,o,a):g(r,y,null))}let d=t.rsc,f=o[1];null===d?t.rsc=f:_(d)&&d.resolve(f);let p=t.head;_(p)&&p.resolve(a)}(l,t.route,r,n,o),t.dynamicRequestTree=null);return}let c=r[1],u=n[2];for(let t in r){let r=c[t],n=u[t],i=a.get(t);if(void 0!==i){let t=i.route[0];if((0,s.matchSegment)(r[0],t)&&null!=n)return e(i,r,n,o)}}}(a,r,n,o)}(e,r,n,o,a)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)g(e.route,r,t);else for(let e of n.values())p(e,t);e.dynamicRequestTree=null}function g(e,t,r){let n=e[1],s=t.parallelRoutes;for(let e in n){let t=n[e],o=s.get(e);if(void 0===o)continue;let a=t[0],l=(0,i.createRouterCacheKey)(a),c=o.get(l);void 0!==c&&g(t,c,r)}let o=t.rsc;_(o)&&(null===r?o.resolve(null):o.reject(r));let a=t.head;_(a)&&a.resolve(null)}let y=Symbol();function _(e){return e&&e.tag===y}function m(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=y,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2743:(e,t,r)=>{"use strict";let{EMPTY_BUFFER:n}=r(5091),s=Buffer[Symbol.species];function i(e,t,r,n,s){for(let i=0;i<s;i++)r[n+i]=e[i]^t[3&i]}function o(e,t){for(let r=0;r<e.length;r++)e[r]^=t[3&r]}function a(e){let t;return(a.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?t=new s(e):ArrayBuffer.isView(e)?t=new s(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),a.readOnly=!1),t)}if(e.exports={concat:function(e,t){if(0===e.length)return n;if(1===e.length)return e[0];let r=Buffer.allocUnsafe(t),i=0;for(let t=0;t<e.length;t++){let n=e[t];r.set(n,i),i+=n.length}return i<t?new s(r.buffer,r.byteOffset,i):r},mask:i,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:a,unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=r(1962);e.exports.mask=function(e,r,n,s,o){o<48?i(e,r,n,s,o):t.mask(e,r,n,s,o)},e.exports.unmask=function(e,r){e.length<32?o(e,r):t.unmask(e,r)}}catch(e){}},2878:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,s,,o]=t;for(let a in n.includes(i.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=r,t[3]="refresh"),s)e(s[a],r)}},refreshInactiveParallelSegments:function(){return o}});let n=r(5426),s=r(7006),i=r(5383);async function o(e){let t=new Set;await a({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function a(e){let{navigatedAt:t,state:r,updatedTree:i,updatedCache:o,includeNextUrl:l,fetchedSegments:c,rootTree:u=i,canonicalUrl:h}=e,[,d,f,p]=i,g=[];if(f&&f!==h&&"refresh"===p&&!c.has(f)){c.add(f);let e=(0,s.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[u[0],u[1],u[2],"refetch"],nextUrl:l?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,n.applyFlightData)(t,o,o,e)});g.push(e)}for(let e in d){let n=a({navigatedAt:t,state:r,updatedTree:d[e],updatedCache:o,includeNextUrl:l,fetchedSegments:c,rootTree:u,canonicalUrl:h});g.push(n)}await Promise.all(g)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2963:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},3171:(e,t,r)=>{var n=r(9021),s=r(9551),i=r(9646).spawn;function o(e){"use strict";e=e||{};var t,o,a=this,l=r(1630),c=r(5591),u={},h=!1,d={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},f=Object.assign({},d),p=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],g=["TRACE","TRACK","CONNECT"],y=!1,_=!1,m=!1,b={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),this.status=null,this.statusText=null,this.open=function(e,t,r,n,s){if(this.abort(),_=!1,m=!1,!(e&&-1===g.indexOf(e)))throw Error("SecurityError: Request method not allowed");u={method:e,url:t.toString(),async:"boolean"!=typeof r||r,user:n||null,password:s||null},v(this.OPENED)},this.setDisableHeaderCheck=function(e){h=e},this.setRequestHeader=function(e,t){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!h&&(!e||-1!==p.indexOf(e.toLowerCase())))return console.warn('Refused to set unsafe header "'+e+'"'),!1;if(y)throw Error("INVALID_STATE_ERR: send flag is true");return f[e]=t,!0},this.getResponseHeader=function(e){return"string"==typeof e&&this.readyState>this.OPENED&&o.headers[e.toLowerCase()]&&!_?o.headers[e.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||_)return"";var e="";for(var t in o.headers)"set-cookie"!==t&&"set-cookie2"!==t&&(e+=t+": "+o.headers[t]+"\r\n");return e.substr(0,e.length-2)},this.getRequestHeader=function(e){return"string"==typeof e&&f[e]?f[e]:""},this.send=function(r){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(y)throw Error("INVALID_STATE_ERR: send has already been called");var h,d=!1,p=!1,g=s.parse(u.url);switch(g.protocol){case"https:":d=!0;case"http:":h=g.hostname;break;case"file:":p=!0;break;case void 0:case"":h="localhost";break;default:throw Error("Protocol not supported.")}if(p){if("GET"!==u.method)throw Error("XMLHttpRequest: Only GET method is supported");if(u.async)n.readFile(unescape(g.pathname),function(e,t){e?a.handleError(e,e.errno||-1):(a.status=200,a.responseText=t.toString("utf8"),a.response=t,v(a.DONE))});else try{this.response=n.readFileSync(unescape(g.pathname)),this.responseText=this.response.toString("utf8"),this.status=200,v(a.DONE)}catch(e){this.handleError(e,e.errno||-1)}return}var m=g.port||(d?443:80),b=g.pathname+(g.search?g.search:"");if(f.Host=h,d&&443===m||80===m||(f.Host+=":"+g.port),u.user){void 0===u.password&&(u.password="");var C=new Buffer(u.user+":"+u.password);f.Authorization="Basic "+C.toString("base64")}"GET"===u.method||"HEAD"===u.method?r=null:r?(f["Content-Length"]=Buffer.isBuffer(r)?r.length:Buffer.byteLength(r),Object.keys(f).some(function(e){return"content-type"===e.toLowerCase()})||(f["Content-Type"]="text/plain;charset=UTF-8")):"POST"===u.method&&(f["Content-Length"]=0);var E=e.agent||!1,w={host:h,port:m,path:b,method:u.method,headers:f,agent:E};if(d&&(w.pfx=e.pfx,w.key=e.key,w.passphrase=e.passphrase,w.cert=e.cert,w.ca=e.ca,w.ciphers=e.ciphers,w.rejectUnauthorized=!1!==e.rejectUnauthorized),_=!1,u.async){var S=d?c.request:l.request;y=!0,a.dispatchEvent("readystatechange");var O=function(r){if(302===(o=r).statusCode||303===o.statusCode||307===o.statusCode){u.url=o.headers.location;var n=s.parse(u.url);h=n.hostname;var i={hostname:n.hostname,port:n.port,path:n.path,method:303===o.statusCode?"GET":u.method,headers:f};d&&(i.pfx=e.pfx,i.key=e.key,i.passphrase=e.passphrase,i.cert=e.cert,i.ca=e.ca,i.ciphers=e.ciphers,i.rejectUnauthorized=!1!==e.rejectUnauthorized),(t=S(i,O).on("error",R)).end();return}v(a.HEADERS_RECEIVED),a.status=o.statusCode,o.on("data",function(e){if(e){var t=Buffer.from(e);a.response=Buffer.concat([a.response,t])}y&&v(a.LOADING)}),o.on("end",function(){y&&(y=!1,v(a.DONE),a.responseText=a.response.toString("utf8"))}),o.on("error",function(e){a.handleError(e)})},R=function(e){if(t.reusedSocket&&"ECONNRESET"===e.code)return S(w,O).on("error",R);a.handleError(e)};t=S(w,O).on("error",R),e.autoUnref&&t.on("socket",e=>{e.unref()}),r&&t.write(r),t.end(),a.dispatchEvent("loadstart")}else{var k=".node-xmlhttprequest-content-"+process.pid,T=".node-xmlhttprequest-sync-"+process.pid;n.writeFileSync(T,"","utf8");for(var x="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(d?"s":"")+".request;var options = "+JSON.stringify(w)+";var responseText = '';var responseData = Buffer.alloc(0);var req = doRequest(options, function(response) {response.on('data', function(chunk) {  var data = Buffer.from(chunk);  responseText += data.toString('utf8');  responseData = Buffer.concat([responseData, data]);});response.on('end', function() {fs.writeFileSync('"+k+"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');fs.unlinkSync('"+T+"');});response.on('error', function(error) {fs.writeFileSync('"+k+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+T+"');});}).on('error', function(error) {fs.writeFileSync('"+k+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+T+"');});"+(r?"req.write('"+JSON.stringify(r).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",P=i(process.argv[0],["-e",x]);n.existsSync(T););if(a.responseText=n.readFileSync(k,"utf8"),P.stdin.end(),n.unlinkSync(k),a.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var N=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,""));a.handleError(N,503)}else{a.status=a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1");var A=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"));o={statusCode:a.status,headers:A.data.headers},a.responseText=A.data.text,a.response=Buffer.from(A.data.data,"base64"),v(a.DONE,!0)}}},this.handleError=function(e,t){this.status=t||0,this.statusText=e,this.responseText=e.stack,_=!0,v(this.DONE)},this.abort=function(){t&&(t.abort(),t=null),f=Object.assign({},d),this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),_=m=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||y)&&this.readyState!==this.DONE&&(y=!1,v(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(e,t){e in b||(b[e]=[]),b[e].push(t)},this.removeEventListener=function(e,t){e in b&&(b[e]=b[e].filter(function(e){return e!==t}))},this.dispatchEvent=function(e){if("function"==typeof a["on"+e]&&(this.readyState===this.DONE&&u.async?setTimeout(function(){a["on"+e]()},0):a["on"+e]()),e in b)for(let t=0,r=b[e].length;t<r;t++)this.readyState===this.DONE?setTimeout(function(){b[e][t].call(a)},0):b[e][t].call(a)};var v=function(e){if(a.readyState!==e&&(a.readyState!==a.UNSENT||!m)&&(a.readyState=e,(u.async||a.readyState<a.OPENED||a.readyState===a.DONE)&&a.dispatchEvent("readystatechange"),a.readyState===a.DONE)){let e;e=m?"abort":_?"error":"load",a.dispatchEvent(e),a.dispatchEvent("loadend")}}}e.exports=o,o.XMLHttpRequest=o},3428:(e,t,r)=>{"use strict";let{tokenChars:n}=r(9179);e.exports={parse:function(e){let t=new Set,r=-1,s=-1,i=0;for(;i<e.length;i++){let o=e.charCodeAt(i);if(-1===s&&1===n[o])-1===r&&(r=i);else if(0!==i&&(32===o||9===o))-1===s&&-1!==r&&(s=i);else if(44===o){if(-1===r)throw SyntaxError(`Unexpected character at index ${i}`);-1===s&&(s=i);let n=e.slice(r,s);if(t.has(n))throw SyntaxError(`The "${n}" subprotocol is duplicated`);t.add(n),r=s=-1}else throw SyntaxError(`Unexpected character at index ${i}`)}if(-1===r||-1!==s)throw SyntaxError("Unexpected end of input");let o=e.slice(r,i);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},3613:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let n=r(7834)._(r(8973)),s=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",o=e.pathname||"",a=e.hash||"",l=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:r&&(c=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(c+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let u=e.search||l&&"?"+l||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||s.test(i))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),a&&"#"!==a[0]&&(a="#"+a),u&&"?"!==u[0]&&(u="?"+u),""+i+c+(o=o.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return i(e)}},3614:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},3666:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return x},createPrefetchURL:function(){return k},default:function(){return j},isExternalURL:function(){return R}});let n=r(7834),s=r(8625),i=n._(r(4996)),o=r(3132),a=r(2564),l=r(5221),c=r(2715),u=r(1991),h=n._(r(4078)),d=r(5534),f=r(7481),p=r(6768),g=r(5060),y=r(6823),_=r(1272),m=r(4640),b=r(5574),v=r(6900),C=r(66),E=r(5808),w=r(677),S=r(4854);r(5292);let O={};function R(e){return e.origin!==window.location.origin}function k(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return R(t)?null:t}function T(e){let{appRouterState:t}=e;return(0,i.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,s={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(s,"",n)):window.history.replaceState(s,"",n)},[t]),(0,i.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function x(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function P(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function N(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,s=null!==n?n:r;return(0,i.useDeferredValue)(r,s)}function A(e){let t,{actionQueue:r,assetPrefix:n,globalError:l}=e,d=(0,u.useActionQueue)(r),{canonicalUrl:f}=d,{searchParams:C,pathname:R}=(0,i.useMemo)(()=>{let e=new URL(f,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,m.removeBasePath)(e.pathname):e.pathname}},[f]);(0,i.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(O.pendingMpaPath=void 0,(0,u.dispatchAppRouterAction)({type:a.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,i.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,S.isRedirectError)(t)){e.preventDefault();let r=(0,w.getURLFromRedirectError)(t);(0,w.getRedirectTypeFromError)(t)===S.RedirectType.push?E.publicAppRouterInstance.push(r,{}):E.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:k}=d;if(k.mpaNavigation){if(O.pendingMpaPath!==f){let e=window.location;k.pendingPush?e.assign(f):e.replace(f),O.pendingMpaPath=f}(0,i.use)(_.unresolvedThenable)}(0,i.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,i.startTransition)(()=>{(0,u.dispatchAppRouterAction)({type:a.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,s){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=P(t),s&&r(s)),e(t,n,s)},window.history.replaceState=function(e,n,s){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=P(e),s&&r(s)),t(e,n,s)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,i.startTransition)(()=>{(0,E.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:x,tree:A,nextUrl:j,focusAndScrollRef:F}=d,L=(0,i.useMemo)(()=>(0,y.findHeadInCache)(x,A[1]),[x,A]),U=(0,i.useMemo)(()=>(0,v.getSelectedParams)(A),[A]),I=(0,i.useMemo)(()=>({parentTree:A,parentCacheNode:x,parentSegmentPath:null,url:f}),[A,x,f]),D=(0,i.useMemo)(()=>({tree:A,focusAndScrollRef:F,nextUrl:j}),[A,F,j]);if(null!==L){let[e,r]=L;t=(0,s.jsx)(N,{headCacheNode:e},r)}else t=null;let B=(0,s.jsxs)(g.RedirectBoundary,{children:[t,x.rsc,(0,s.jsx)(p.AppRouterAnnouncer,{tree:A})]});return B=(0,s.jsx)(h.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:B}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(T,{appRouterState:d}),(0,s.jsx)(M,{}),(0,s.jsx)(c.PathParamsContext.Provider,{value:U,children:(0,s.jsx)(c.PathnameContext.Provider,{value:R,children:(0,s.jsx)(c.SearchParamsContext.Provider,{value:C,children:(0,s.jsx)(o.GlobalLayoutRouterContext.Provider,{value:D,children:(0,s.jsx)(o.AppRouterContext.Provider,{value:E.publicAppRouterInstance,children:(0,s.jsx)(o.LayoutRouterContext.Provider,{value:I,children:B})})})})})})]})}function j(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:i}=e;return(0,C.useNavFailureHandler)(),(0,s.jsx)(h.ErrorBoundary,{errorComponent:h.default,children:(0,s.jsx)(A,{actionQueue:t,assetPrefix:i,globalError:[r,n]})})}let F=new Set,L=new Set;function M(){let[,e]=i.default.useState(0),t=F.size;return(0,i.useEffect)(()=>{let r=()=>e(e=>e+1);return L.add(r),t!==F.size&&r(),()=>{L.delete(r)}},[t,e]),[...F].map((e,t)=>(0,s.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=F.size;return F.add(e),F.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3785:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return h},handleAliasedPrefetchEntry:function(){return u}});let n=r(5383),s=r(3666),i=r(5180),o=r(5221),a=r(2245),l=r(4300),c=r(4969);function u(e,t,r,u,d){let f,p=t.tree,g=t.cache,y=(0,o.createHrefFromUrl)(u);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=h(r,Object.fromEntries(u.searchParams));let{seedData:o,isRootRender:c,pathToSegment:d}=t,_=["",...d];r=h(r,Object.fromEntries(u.searchParams));let m=(0,i.applyRouterStatePatchToTree)(_,p,r,y),b=(0,s.createEmptyCacheNode)();if(c&&o){let t=o[1];b.loading=o[3],b.rsc=t,function e(t,r,s,i,o){if(0!==Object.keys(i[1]).length)for(let l in i[1]){let c,u=i[1][l],h=u[0],d=(0,a.createRouterCacheKey)(h),f=null!==o&&void 0!==o[2][l]?o[2][l]:null;if(null!==f){let e=f[1],r=f[3];c={lazyData:null,rsc:h.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=r.parallelRoutes.get(l);p?p.set(d,c):r.parallelRoutes.set(l,new Map([[d,c]])),e(t,c,s,u,f)}}(e,b,g,r,o)}else b.rsc=g.rsc,b.prefetchRsc=g.prefetchRsc,b.loading=g.loading,b.parallelRoutes=new Map(g.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,g,t);m&&(p=m,g=b,f=!0)}return!!f&&(d.patchedTree=p,d.cache=g,d.canonicalUrl=y,d.hashFragment=u.hash,(0,c.handleMutable)(t,d))}function h(e,t){let[r,s,...i]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),s,...i];let o={};for(let[e,r]of Object.entries(s))o[e]=h(r,t);return[r,o,...i]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3929:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return s}});let n=r(2963);function s(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},3959:(e,t,r)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=r(6243):e.exports=r(7255)},3967:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return x}});let n=r(6414),s=r(2646),i=r(6261),o=r(2564),a=r(6539),l=r(5221),c=r(5638),u=r(5180),h=r(9736),d=r(4969),f=r(9634),p=r(3666),g=r(9860),y=r(5967),_=r(2878),m=r(793),b=r(677),v=r(4854),C=r(9820),E=r(4640),w=r(5574),S=r(9252);r(6495);let{createFromFetch:O,createTemporaryReferenceSet:R,encodeReply:k}=r(4595);async function T(e,t,r){let o,l,{actionId:c,actionArgs:u}=r,h=R(),d=(0,S.extractInfoFromServerReferenceId)(c),f="use-cache"===d.type?(0,S.omitUnusedArgs)(u,d):u,p=await k(f,{temporaryReferences:h}),g=await fetch("",{method:"POST",headers:{Accept:i.RSC_CONTENT_TYPE_HEADER,[i.ACTION_HEADER]:c,[i.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[i.NEXT_URL]:t}:{}},body:p}),y=g.headers.get("x-action-redirect"),[_,b]=(null==y?void 0:y.split(";"))||[];switch(b){case"push":o=v.RedirectType.push;break;case"replace":o=v.RedirectType.replace;break;default:o=void 0}let C=!!g.headers.get(i.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(g.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let E=_?(0,a.assignLocation)(_,new URL(e.canonicalUrl,window.location.href)):void 0,w=g.headers.get("content-type");if(null==w?void 0:w.startsWith(i.RSC_CONTENT_TYPE_HEADER)){let e=await O(Promise.resolve(g),{callServer:n.callServer,findSourceMapURL:s.findSourceMapURL,temporaryReferences:h});return _?{actionFlightData:(0,m.normalizeFlightData)(e.f),redirectLocation:E,redirectType:o,revalidatedParts:l,isPrerender:C}:{actionResult:e.a,actionFlightData:(0,m.normalizeFlightData)(e.f),redirectLocation:E,redirectType:o,revalidatedParts:l,isPrerender:C}}if(g.status>=400)throw Object.defineProperty(Error("text/plain"===w?await g.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:E,redirectType:o,revalidatedParts:l,isPrerender:C}}function x(e,t){let{resolve:r,reject:n}=t,s={},i=e.tree;s.preserveCustomHistoryState=!1;let a=e.nextUrl&&(0,g.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,m=Date.now();return T(e,a,t).then(async g=>{let S,{actionResult:O,actionFlightData:R,redirectLocation:k,redirectType:T,isPrerender:x,revalidatedParts:P}=g;if(k&&(T===v.RedirectType.replace?(e.pushRef.pendingPush=!1,s.pendingPush=!1):(e.pushRef.pendingPush=!0,s.pendingPush=!0),s.canonicalUrl=S=(0,l.createHrefFromUrl)(k,!1)),!R)return(r(O),k)?(0,c.handleExternalUrl)(e,s,k.href,e.pushRef.pendingPush):e;if("string"==typeof R)return r(O),(0,c.handleExternalUrl)(e,s,R,e.pushRef.pendingPush);let N=P.paths.length>0||P.tag||P.cookie;for(let n of R){let{tree:o,seedData:l,head:d,isRootRender:g}=n;if(!g)return console.log("SERVER ACTION APPLY FAILED"),r(O),e;let b=(0,u.applyRouterStatePatchToTree)([""],i,o,S||e.canonicalUrl);if(null===b)return r(O),(0,y.handleSegmentMismatch)(e,t,o);if((0,h.isNavigatingToNewRootLayout)(i,b))return r(O),(0,c.handleExternalUrl)(e,s,S||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],r=(0,p.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(m,r,void 0,o,l,d,void 0),s.cache=r,s.prefetchCache=new Map,N&&await (0,_.refreshInactiveParallelSegments)({navigatedAt:m,state:e,updatedTree:b,updatedCache:r,includeNextUrl:!!a,canonicalUrl:s.canonicalUrl||e.canonicalUrl})}s.patchedTree=b,i=b}return k&&S?(N||((0,C.createSeededPrefetchCacheEntry)({url:k,data:{flightData:R,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:x?o.PrefetchKind.FULL:o.PrefetchKind.AUTO}),s.prefetchCache=e.prefetchCache),n((0,b.getRedirectError)((0,w.hasBasePath)(S)?(0,E.removeBasePath)(S):S,T||v.RedirectType.push))):r(O),(0,d.handleMutable)(e,s)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3994:e=>{"use strict";let t=Symbol("kDone"),r=Symbol("kRun");class n{constructor(e){this[t]=()=>{this.pending--,this[r]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[r]()}[r](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=n},4300:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return c}});let n=r(9554),s=r(9634),i=r(2245),o=r(5383);function a(e,t,r,a,l,c){let{segmentPath:u,seedData:h,tree:d,head:f}=a,p=t,g=r;for(let t=0;t<u.length;t+=2){let r=u[t],a=u[t+1],y=t===u.length-2,_=(0,i.createRouterCacheKey)(a),m=g.parallelRoutes.get(r);if(!m)continue;let b=p.parallelRoutes.get(r);b&&b!==m||(b=new Map(m),p.parallelRoutes.set(r,b));let v=m.get(_),C=b.get(_);if(y){if(h&&(!C||!C.lazyData||C===v)){let t=h[0],r=h[1],i=h[3];C={lazyData:null,rsc:c||t!==o.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:i,parallelRoutes:c&&v?new Map(v.parallelRoutes):new Map,navigatedAt:e},v&&c&&(0,n.invalidateCacheByRouterState)(C,v,d),c&&(0,s.fillLazyItemsTillLeafWithHead)(e,C,v,d,h,f,l),b.set(_,C)}continue}C&&v&&(C===v&&(C={lazyData:C.lazyData,rsc:C.rsc,prefetchRsc:C.prefetchRsc,head:C.head,prefetchHead:C.prefetchHead,parallelRoutes:new Map(C.parallelRoutes),loading:C.loading},b.set(_,C)),p=C,g=v)}}function l(e,t,r,n,s){a(e,t,r,n,s,!0)}function c(e,t,r,n,s){a(e,t,r,n,s,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4332:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(2564),r(5638),r(739),r(5921),r(248),r(7650),r(1706),r(3967);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4640:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(5574),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4910:(e,t,r)=>{e.exports=function(e){function t(e){let r,s,i,o=null;function a(...e){if(!a.enabled)return;let n=Number(new Date);a.diff=n-(r||n),a.prev=r,a.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";s++;let i=t.formatters[n];if("function"==typeof i){let t=e[s];r=i.call(a,t),e.splice(s,1),s--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(s!==t.namespaces&&(s=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,r){let n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function s(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(s),...t.skips.map(s).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),s=n.length;for(r=0;r<s;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let r,n;if("*"===e[e.length-1])return!0;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(388),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},4969:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return i}});let n=r(6900);function s(e){return void 0!==e}function i(e,t){var r,i;let o=null==(r=t.shouldScroll)||r,a=e.nextUrl;if(s(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?a=r:a||(a=e.canonicalUrl)}return{canonicalUrl:s(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:s(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:s(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:s(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!s(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(i=null==t?void 0:t.scrollableSegments)?i:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:s(t.patchedTree)?t.patchedTree:e.tree,nextUrl:a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4988:(e,t,r)=>{"use strict";let{Duplex:n}=r(7910);function s(e){e.emit("close")}function i(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let r=!0,a=new n({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,r){let n=!r&&a._readableState.objectMode?t.toString():t;a.push(n)||e.pause()}),e.once("error",function(e){a.destroyed||(r=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,n){if(e.readyState===e.CLOSED){n(t),process.nextTick(s,a);return}let i=!1;e.once("error",function(e){i=!0,n(e)}),e.once("close",function(){i||n(t),process.nextTick(s,a)}),r&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING)return void e.once("open",function(){a._final(t)});null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,r,n){if(e.readyState===e.CONNECTING)return void e.once("open",function(){a._write(t,r,n)});e.send(t,n)},a.on("end",i),a.on("error",o),a}},5091:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},5180:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,l){let c,[u,h,d,f,p]=r;if(1===t.length){let e=a(r,n);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[g,y]=t;if(!(0,i.matchSegment)(g,u))return null;if(2===t.length)c=a(h[y],n);else if(null===(c=e((0,s.getNextFlightSegmentPath)(t),h[y],n,l)))return null;let _=[t[0],{...h,[y]:c},d,f];return p&&(_[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(_,l),_}}});let n=r(5383),s=r(793),i=r(2463),o=r(2878);function a(e,t){let[r,s]=e,[o,l]=t;if(o===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,i.matchSegment)(r,o)){let t={};for(let e in s)void 0!==l[e]?t[e]=a(s[e],l[e]):t[e]=s[e];for(let e in l)t[e]||(t[e]=l[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5182:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let o=i.length<=2,[a,l]=i,c=(0,s.createRouterCacheKey)(l),u=r.parallelRoutes.get(a),h=t.parallelRoutes.get(a);h&&h!==u||(h=new Map(u),t.parallelRoutes.set(a,h));let d=null==u?void 0:u.get(c),f=h.get(c);if(o){f&&f.lazyData&&f!==d||h.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!f||!d){f||h.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},h.set(c,f)),e(f,d,(0,n.getNextFlightSegmentPath)(i))}}});let n=r(793),s=r(2245);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5292:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return c},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return m},mountLinkInstance:function(){return _},onLinkVisibilityChanged:function(){return v},onNavigationIntent:function(){return C},pingVisibleLinks:function(){return w},setLinkForCurrentNavigation:function(){return u},unmountLinkForCurrentNavigation:function(){return h},unmountPrefetchableInstance:function(){return b}}),r(5808);let n=r(3666),s=r(2564),i=r(6495),o=r(4996),a=null,l={pending:!0},c={pending:!1};function u(e){(0,o.startTransition)(()=>{null==a||a.setOptimisticLinkStatus(c),null==e||e.setOptimisticLinkStatus(l),a=e})}function h(e){a===e&&(a=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,f=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;v(t.target,e)}},{rootMargin:"200px"}):null;function g(e,t){void 0!==d.get(e)&&b(e),d.set(e,t),null!==p&&p.observe(e)}function y(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function _(e,t,r,n,s,i){if(s){let s=y(t);if(null!==s){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:s.href,setOptimisticLinkStatus:i};return g(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:i}}function m(e,t,r,n){let s=y(t);null!==s&&g(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:s.href,setOptimisticLinkStatus:null})}function b(e){let t=d.get(e);if(void 0!==t){d.delete(e),f.delete(t);let r=t.prefetchTask;null!==r&&(0,i.cancelPrefetchTask)(r)}null!==p&&p.unobserve(e)}function v(e,t){let r=d.get(e);void 0!==r&&(r.isVisible=t,t?f.add(r):f.delete(r),E(r))}function C(e,t){let r=d.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,E(r))}function E(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,i.cancelPrefetchTask)(t);return}}function w(e,t){let r=(0,i.getCurrentCacheVersion)();for(let n of f){let o=n.prefetchTask;if(null!==o&&n.cacheVersion===r&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,i.cancelPrefetchTask)(o);let a=(0,i.createCacheKey)(n.prefetchHref,e),l=n.wasHoveredOrTouched?i.PrefetchPriority.Intent:i.PrefetchPriority.Default;n.prefetchTask=(0,i.schedulePrefetchTask)(a,t,n.kind===s.PrefetchKind.FULL,l),n.cacheVersion=(0,i.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5426:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return i}});let n=r(9634),s=r(4300);function i(e,t,r,i,o){let{tree:a,seedData:l,head:c,isRootRender:u}=i;if(null===l)return!1;if(u){let s=l[1];r.loading=l[3],r.rsc=s,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,a,l,c,o)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,s.fillCacheWithNewSubTreeData)(e,r,t,i,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5461:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[i,o]=r,[a,l]=t;return(0,s.matchSegment)(a,i)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),o[l]):!!Array.isArray(a)}}});let n=r(793),s=r(2463);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5470:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return c}});let n=r(672),s=r(9962);var i=s._("_maxConcurrency"),o=s._("_runningCount"),a=s._("_queue"),l=s._("_processNext");class c{enqueue(e){let t,r,s=new Promise((e,n)=>{t=e,r=n}),i=async()=>{try{n._(this,o)[o]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,o)[o]--,n._(this,l)[l]()}};return n._(this,a)[a].push({promiseFn:s,task:i}),n._(this,l)[l](),s}bump(e){let t=n._(this,a)[a].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,a)[a].splice(t,1)[0];n._(this,a)[a].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:u}),Object.defineProperty(this,i,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),n._(this,i)[i]=e,n._(this,o)[o]=0,n._(this,a)[a]=[]}}function u(e){if(void 0===e&&(e=!1),(n._(this,o)[o]<n._(this,i)[i]||e)&&n._(this,a)[a].length>0){var t;null==(t=n._(this,a)[a].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5534:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return i},getBotType:function(){return l},isBot:function(){return a}});let n=r(9478),s=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,i=n.HTML_LIMITED_BOT_UA_RE.source;function o(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function a(e){return s.test(e)||o(e)}function l(e){return s.test(e)?"dom":o(e)?"html":void 0}},5574:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return s}});let n=r(3929);function s(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5638:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,r){let{url:C,isExternalUrl:E,navigateType:w,shouldScroll:S,allowAliasing:O}=r,R={},{hash:k}=C,T=(0,s.createHrefFromUrl)(C),x="push"===w;if((0,y.prunePrefetchCache)(t.prefetchCache),R.preserveCustomHistoryState=!1,R.pendingPush=x,E)return b(t,R,C.toString(),x);if(document.getElementById("__next-page-redirect"))return b(t,R,T,x);let P=(0,y.getOrCreatePrefetchCacheEntry)({url:C,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:O}),{treeAtTimeOfPrefetch:N,data:A}=P;return d.prefetchQueue.bump(A),A.then(d=>{let{flightData:y,canonicalUrl:E,postponed:w}=d,O=Date.now(),A=!1;if(P.lastUsedTime||(P.lastUsedTime=O,A=!0),P.aliased){let n=(0,m.handleAliasedPrefetchEntry)(O,t,y,C,R);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof y)return b(t,R,y,x);let j=E?(0,s.createHrefFromUrl)(E):T;if(k&&t.canonicalUrl.split("#",1)[0]===j.split("#",1)[0])return R.onlyHashChange=!0,R.canonicalUrl=j,R.shouldScroll=S,R.hashFragment=k,R.scrollableSegments=[],(0,u.handleMutable)(t,R);let F=t.tree,L=t.cache,M=[];for(let e of y){let{pathToSegment:r,seedData:s,head:u,isHeadPartial:d,isRootRender:y}=e,m=e.tree,E=["",...r],S=(0,o.applyRouterStatePatchToTree)(E,F,m,T);if(null===S&&(S=(0,o.applyRouterStatePatchToTree)(E,N,m,T)),null!==S){if(s&&y&&w){let e=(0,g.startPPRNavigation)(O,L,F,m,s,u,d,!1,M);if(null!==e){if(null===e.route)return b(t,R,T,x);S=e.route;let r=e.node;null!==r&&(R.cache=r);let s=e.dynamicRequestTree;if(null!==s){let r=(0,n.fetchServerResponse)(C,{flightRouterState:s,nextUrl:t.nextUrl});(0,g.listenForDynamicRequest)(e,r)}}else S=m}else{if((0,l.isNavigatingToNewRootLayout)(F,S))return b(t,R,T,x);let n=(0,f.createEmptyCacheNode)(),s=!1;for(let t of(P.status!==c.PrefetchCacheEntryStatus.stale||A?s=(0,h.applyFlightData)(O,L,n,e,P):(s=function(e,t,r,n){let s=!1;for(let i of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),v(n).map(e=>[...r,...e])))(0,_.clearCacheNodeDataForSegmentPath)(e,t,i),s=!0;return s}(n,L,r,m),P.lastUsedTime=O),(0,a.shouldHardNavigate)(E,F)?(n.rsc=L.rsc,n.prefetchRsc=L.prefetchRsc,(0,i.invalidateCacheBelowFlightSegmentPath)(n,L,r),R.cache=n):s&&(R.cache=n,L=n),v(m))){let e=[...r,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&M.push(e)}}F=S}}return R.patchedTree=F,R.canonicalUrl=j,R.scrollableSegments=M,R.hashFragment=k,R.shouldScroll=S,(0,u.handleMutable)(t,R)},()=>t)}}});let n=r(7006),s=r(5221),i=r(9590),o=r(5180),a=r(5461),l=r(9736),c=r(2564),u=r(4969),h=r(5426),d=r(7650),f=r(3666),p=r(5383),g=r(2058),y=r(9820),_=r(5182),m=r(3785);function b(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,u.handleMutable)(e,t)}function v(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,s]of Object.entries(n))for(let n of v(s))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(6495),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5808:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return _},dispatchTraverseAction:function(){return m},getCurrentAppRouterState:function(){return g},publicAppRouterInstance:function(){return b}});let n=r(2564),s=r(4332),i=r(4996),o=r(358);r(6495);let a=r(1991),l=r(7481),c=r(3666),u=r(7650),h=r(5292);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?f({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function f(e){let{actionQueue:t,action:r,setState:n}=e,s=t.state;t.pending=r;let i=r.payload,a=t.action(s,i);function l(e){r.discarded||(t.state=e,d(t,n),r.resolve(e))}(0,o.isThenable)(a)?a.then(l,e=>{d(t,n),r.reject(e)}):l(a)}function p(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let s={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{s={resolve:e,reject:t}});(0,i.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:s.resolve,reject:s.reject};null===e.pending?(e.last=o,f({actionQueue:e,action:o,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),f({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(r,e,t),action:async(e,t)=>(0,s.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function g(){return null}function y(){return null}function _(e,t,r,s){let i=new URL((0,l.addBasePath)(e),location.href);(0,h.setLinkForCurrentNavigation)(s);(0,a.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:i,isExternalUrl:(0,c.isExternalURL)(i),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function m(e,t){(0,a.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),s=(0,c.createPrefetchURL)(e);if(null!==s){var i;(0,u.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:s,kind:null!=(i=null==t?void 0:t.kind)?i:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,i.startTransition)(()=>{var r;_(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,i.startTransition)(()=>{var r;_(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,i.startTransition)(()=>{(0,a.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5921:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return i}});let n=r(5221),s=r(6900);function i(e,t){var r;let{url:i,tree:o}=t,a=(0,n.createHrefFromUrl)(i),l=o||e.tree,c=e.cache;return{canonicalUrl:a,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:c,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,s.extractPathFromFlightRouterState)(l))?r:i.pathname}}r(2058),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5967:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return s}});let n=r(5638);function s(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6143:(e,t,r)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=r(8075):e.exports=r(8956)},6243:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,s=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(s=n))}),t.splice(s,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(6342)(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},6304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return s}});let n=r(4996);function s(e,t){let r=(0,n.useRef)(null),s=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=s.current;t&&(s.current=null,t())}else e&&(r.current=i(e,n)),t&&(s.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6342:(e,t,r)=>{e.exports=function(e){function t(e){let r,s,i,o=null;function a(...e){if(!a.enabled)return;let n=Number(new Date);a.diff=n-(r||n),a.prev=r,a.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";s++;let i=t.formatters[n];if("function"==typeof i){let t=e[s];r=i.call(a,t),e.splice(s,1),s--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(s!==t.namespaces&&(s=t.namespaces,i=t.enabled(e)),i),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,r){let n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function s(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(s),...t.skips.map(s).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),s=n.length;for(r=0;r<s;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let r,n;if("*"===e[e.length-1])return!0;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(388),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},6495:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return h},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return l},createCacheKey:function(){return u},getCurrentCacheVersion:function(){return o},navigate:function(){return s},prefetch:function(){return n},reschedulePrefetchTask:function(){return c},revalidateEntireCache:function(){return i},schedulePrefetchTask:function(){return a}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,s=r,i=r,o=r,a=r,l=r,c=r,u=r;var h=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6539:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return s}});let n=r(7481);function s(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6768:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let n=r(4996),s=r(8709),i="next-route-announcer";function o(e){let{tree:t}=e,[r,o]=(0,n.useState)(null);(0,n.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(i)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(i);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(i)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[a,l]=(0,n.useState)(""),c=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==c.current&&c.current!==e&&l(e),c.current=e},[t]),r?(0,s.createPortal)(a,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6823:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return s}});let n=r(2245);function s(e,t){return function e(t,r,s){if(0===Object.keys(r).length)return[t,s];if(r.children){let[i,o]=r.children,a=t.parallelRoutes.get("children");if(a){let t=(0,n.createRouterCacheKey)(i),r=a.get(t);if(r){let n=e(r,o,s+"/"+t);if(n)return n}}}for(let i in r){if("children"===i)continue;let[o,a]=r[i],l=t.parallelRoutes.get(i);if(!l)continue;let c=(0,n.createRouterCacheKey)(o),u=l.get(c);if(!u)continue;let h=e(u,a,s+"/"+c);if(h)return h}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6900:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return u},extractPathFromFlightRouterState:function(){return c},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],i=Array.isArray(t),o=i?t[1]:t;!o||o.startsWith(s.PAGE_SEGMENT_KEY)||(i&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):i&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(8385),s=r(5383),i=r(2463),o=e=>"/"===e[0]?e.slice(1):e,a=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=o(t))||(0,s.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===s.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(s.PAGE_SEGMENT_KEY))return"";let i=[a(r)],o=null!=(t=e[1])?t:{},u=o.children?c(o.children):void 0;if(void 0!==u)i.push(u);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let r=c(t);void 0!==r&&i.push(r)}return l(i)}function u(e,t){let r=function e(t,r){let[s,o]=t,[l,u]=r,h=a(s),d=a(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>h.startsWith(e)||d.startsWith(e)))return"";if(!(0,i.matchSegment)(s,l)){var f;return null!=(f=c(r))?f:""}for(let t in o)if(u[t]){let r=e(o[t],u[t]);if(null!==r)return a(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7184:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return s}});let n=r(2963);function s(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:s,hash:i}=(0,n.parsePath)(e);return""+t+r+s+i}},7209:(e,t,r)=>{"use strict";let n=r(4735),s=r(5591),i=r(1630),o=r(1645),a=r(4631),{randomBytes:l,createHash:c}=r(5511),{Duplex:u,Readable:h}=r(7910),{URL:d}=r(9551),f=r(1104),p=r(9173),g=r(9333),{BINARY_TYPES:y,EMPTY_BUFFER:_,GUID:m,kForOnEventAttribute:b,kListener:v,kStatusCode:C,kWebSocket:E,NOOP:w}=r(5091),{EventTarget:{addEventListener:S,removeEventListener:O}}=r(1566),{format:R,parse:k}=r(91),{toBuffer:T}=r(2743),x=Symbol("kAborted"),P=[8,13],N=["CONNECTING","OPEN","CLOSING","CLOSED"],A=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class j extends n{constructor(e,t,r){super(),this._binaryType=y[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=_,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=j.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(r=t,t=[]):t=[t]),function e(t,r,n,o){let a,u,h,p,g={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:P[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=g.autoPong,!P.includes(g.protocolVersion))throw RangeError(`Unsupported protocol version: ${g.protocolVersion} (supported versions: ${P.join(", ")})`);if(r instanceof d)a=r;else try{a=new d(r)}catch(e){throw SyntaxError(`Invalid URL: ${r}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let y="wss:"===a.protocol,_="ws+unix:"===a.protocol;if("ws:"===a.protocol||y||_?_&&!a.pathname?u="The URL's pathname is empty":a.hash&&(u="The URL contains a fragment identifier"):u='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',u){let e=SyntaxError(u);if(0!==t._redirects)return void F(t,e);throw e}let b=y?443:80,v=l(16).toString("base64"),C=y?s.request:i.request,E=new Set;if(g.createConnection=g.createConnection||(y?M:L),g.defaultPort=g.defaultPort||b,g.port=a.port||b,g.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,g.headers={...g.headers,"Sec-WebSocket-Version":g.protocolVersion,"Sec-WebSocket-Key":v,Connection:"Upgrade",Upgrade:"websocket"},g.path=a.pathname+a.search,g.timeout=g.handshakeTimeout,g.perMessageDeflate&&(h=new f(!0!==g.perMessageDeflate?g.perMessageDeflate:{},!1,g.maxPayload),g.headers["Sec-WebSocket-Extensions"]=R({[f.extensionName]:h.offer()})),n.length){for(let e of n){if("string"!=typeof e||!A.test(e)||E.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");E.add(e)}g.headers["Sec-WebSocket-Protocol"]=n.join(",")}if(g.origin&&(g.protocolVersion<13?g.headers["Sec-WebSocket-Origin"]=g.origin:g.headers.Origin=g.origin),(a.username||a.password)&&(g.auth=`${a.username}:${a.password}`),_){let e=g.path.split(":");g.socketPath=e[0],g.path=e[1]}if(g.followRedirects){if(0===t._redirects){t._originalIpc=_,t._originalSecure=y,t._originalHostOrSocketPath=_?g.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,r]of Object.entries(e))o.headers[t.toLowerCase()]=r}else if(0===t.listenerCount("redirect")){let e=_?!!t._originalIpc&&g.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||y)||(delete g.headers.authorization,delete g.headers.cookie,e||delete g.headers.host,g.auth=void 0)}g.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(g.auth).toString("base64")),p=t._req=C(g),t._redirects&&t.emit("redirect",t.url,p)}else p=t._req=C(g);g.timeout&&p.on("timeout",()=>{U(t,p,"Opening handshake has timed out")}),p.on("error",e=>{null===p||p[x]||(p=t._req=null,F(t,e))}),p.on("response",s=>{let i=s.headers.location,a=s.statusCode;if(i&&g.followRedirects&&a>=300&&a<400){let s;if(++t._redirects>g.maxRedirects)return void U(t,p,"Maximum redirects exceeded");p.abort();try{s=new d(i,r)}catch(e){F(t,SyntaxError(`Invalid URL: ${i}`));return}e(t,s,n,o)}else t.emit("unexpected-response",p,s)||U(t,p,`Unexpected server response: ${s.statusCode}`)}),p.on("upgrade",(e,r,n)=>{let s;if(t.emit("upgrade",e),t.readyState!==j.CONNECTING)return;p=t._req=null;let i=e.headers.upgrade;if(void 0===i||"websocket"!==i.toLowerCase())return void U(t,r,"Invalid Upgrade header");let o=c("sha1").update(v+m).digest("base64");if(e.headers["sec-websocket-accept"]!==o)return void U(t,r,"Invalid Sec-WebSocket-Accept header");let a=e.headers["sec-websocket-protocol"];if(void 0!==a?E.size?E.has(a)||(s="Server sent an invalid subprotocol"):s="Server sent a subprotocol but none was requested":E.size&&(s="Server sent no subprotocol"),s)return void U(t,r,s);a&&(t._protocol=a);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!h)return void U(t,r,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");try{e=k(l)}catch(e){U(t,r,"Invalid Sec-WebSocket-Extensions header");return}let n=Object.keys(e);if(1!==n.length||n[0]!==f.extensionName)return void U(t,r,"Server indicated an extension that was not requested");try{h.accept(e[f.extensionName])}catch(e){U(t,r,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[f.extensionName]=h}t.setSocket(r,n,{allowSynchronousEvents:g.allowSynchronousEvents,generateMask:g.generateMask,maxPayload:g.maxPayload,skipUTF8Validation:g.skipUTF8Validation})}),g.finishRequest?g.finishRequest(p,t):p.end()}(this,e,t,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){y.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,r){let n=new p({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation});this._sender=new g(e,this._extensions,r.generateMask),this._receiver=n,this._socket=e,n[E]=this,e[E]=this,n.on("conclude",D),n.on("drain",B),n.on("error",H),n.on("message",W),n.on("ping",V),n.on("pong",z),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",G),e.on("data",K),e.on("end",Y),e.on("error",X),this._readyState=j.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=j.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[f.extensionName]&&this._extensions[f.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=j.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==j.CLOSED){if(this.readyState===j.CONNECTING)return void U(this,this._req,"WebSocket was closed before the connection was established");if(this.readyState===j.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=j.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==j.CONNECTING&&this.readyState!==j.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,r){if(this.readyState===j.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==j.OPEN)return void I(this,e,r);void 0===t&&(t=!this._isServer),this._sender.ping(e||_,t,r)}pong(e,t,r){if(this.readyState===j.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==j.OPEN)return void I(this,e,r);void 0===t&&(t=!this._isServer),this._sender.pong(e||_,t,r)}resume(){this.readyState!==j.CONNECTING&&this.readyState!==j.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,r){if(this.readyState===j.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(r=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==j.OPEN)return void I(this,e,r);let n={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[f.extensionName]||(n.compress=!1),this._sender.send(e||_,n,r)}terminate(){if(this.readyState!==j.CLOSED){if(this.readyState===j.CONNECTING)return void U(this,this._req,"WebSocket was closed before the connection was established");this._socket&&(this._readyState=j.CLOSING,this._socket.destroy())}}}function F(e,t){e._readyState=j.CLOSING,e.emit("error",t),e.emitClose()}function L(e){return e.path=e.socketPath,o.connect(e)}function M(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function U(e,t,r){e._readyState=j.CLOSING;let n=Error(r);Error.captureStackTrace(n,U),t.setHeader?(t[x]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(F,e,n)):(t.destroy(n),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function I(e,t,r){if(t){let r=T(t).length;e._socket?e._sender._bufferedBytes+=r:e._bufferedAmount+=r}if(r){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${N[e.readyState]})`);process.nextTick(r,t)}}function D(e,t){let r=this[E];r._closeFrameReceived=!0,r._closeMessage=t,r._closeCode=e,void 0!==r._socket[E]&&(r._socket.removeListener("data",K),process.nextTick($,r._socket),1005===e?r.close():r.close(e,t))}function B(){let e=this[E];e.isPaused||e._socket.resume()}function H(e){let t=this[E];void 0!==t._socket[E]&&(t._socket.removeListener("data",K),process.nextTick($,t._socket),t.close(e[C])),t.emit("error",e)}function q(){this[E].emitClose()}function W(e,t){this[E].emit("message",e,t)}function V(e){let t=this[E];t._autoPong&&t.pong(e,!this._isServer,w),t.emit("ping",e)}function z(e){this[E].emit("pong",e)}function $(e){e.resume()}function G(){let e,t=this[E];this.removeListener("close",G),this.removeListener("data",K),this.removeListener("end",Y),t._readyState=j.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[E]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",q),t._receiver.on("finish",q))}function K(e){this[E]._receiver.write(e)||this.pause()}function Y(){let e=this[E];e._readyState=j.CLOSING,e._receiver.end(),this.end()}function X(){let e=this[E];this.removeListener("error",X),this.on("error",w),e&&(e._readyState=j.CLOSING,this.destroy())}Object.defineProperty(j,"CONNECTING",{enumerable:!0,value:N.indexOf("CONNECTING")}),Object.defineProperty(j.prototype,"CONNECTING",{enumerable:!0,value:N.indexOf("CONNECTING")}),Object.defineProperty(j,"OPEN",{enumerable:!0,value:N.indexOf("OPEN")}),Object.defineProperty(j.prototype,"OPEN",{enumerable:!0,value:N.indexOf("OPEN")}),Object.defineProperty(j,"CLOSING",{enumerable:!0,value:N.indexOf("CLOSING")}),Object.defineProperty(j.prototype,"CLOSING",{enumerable:!0,value:N.indexOf("CLOSING")}),Object.defineProperty(j,"CLOSED",{enumerable:!0,value:N.indexOf("CLOSED")}),Object.defineProperty(j.prototype,"CLOSED",{enumerable:!0,value:N.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(j.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(j.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[v];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),j.prototype.addEventListener=S,j.prototype.removeEventListener=O,e.exports=j},7255:(e,t,r)=>{let n=r(3997),s=r(8354);t.init=function(e){e.inspectOpts={};let r=Object.keys(t.inspectOpts);for(let n=0;n<r.length;n++)e.inspectOpts[r[n]]=t.inspectOpts[r[n]]},t.log=function(...e){return process.stderr.write(s.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(r){let{namespace:n,useColors:s}=this;if(s){let t=this.color,s="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${s};1m${n} \u001B[0m`;r[0]=i+r[0].split("\n").join("\n"+i),r.push(s+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else r[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+r[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(process.stderr.fd)},t.destroy=s.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=r(402);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[r]=n,e},{}),e.exports=r(6342)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts)}},7322:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,s=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(s=n))}),t.splice(s,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(897)(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},7481:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return i}});let n=r(7184),s=r(8212);function i(e,t){return(0,s.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7650:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return i},prefetchReducer:function(){return o}});let n=r(5470),s=r(9820),i=new n.PromiseQueue(5),o=function(e,t){(0,s.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,s.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7782:e=>{"use strict";e.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":1===e.length?"-":"--",n=t.indexOf(r+e),s=t.indexOf("--");return -1!==n&&(-1===s||n<s)}},8075:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,s=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(s=n))}),t.splice(s,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(4910)(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},8212:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return i}});let n=r(547),s=r(2963),i=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:i}=(0,s.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+i};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8842:(e,t,r)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=r(7322):e.exports=r(8964)},8956:(e,t,r)=>{let n=r(3997),s=r(8354);t.init=function(e){e.inspectOpts={};let r=Object.keys(t.inspectOpts);for(let n=0;n<r.length;n++)e.inspectOpts[r[n]]=t.inspectOpts[r[n]]},t.log=function(...e){return process.stderr.write(s.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(r){let{namespace:n,useColors:s}=this;if(s){let t=this.color,s="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${s};1m${n} \u001B[0m`;r[0]=i+r[0].split("\n").join("\n"+i),r.push(s+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else r[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+r[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(process.stderr.fd)},t.destroy=s.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=r(402);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[r]=n,e},{}),e.exports=r(4910)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts)}},8964:(e,t,r)=>{let n=r(3997),s=r(8354);t.init=function(e){e.inspectOpts={};let r=Object.keys(t.inspectOpts);for(let n=0;n<r.length;n++)e.inspectOpts[r[n]]=t.inspectOpts[r[n]]},t.log=function(...e){return process.stderr.write(s.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(r){let{namespace:n,useColors:s}=this;if(s){let t=this.color,s="\x1b[3"+(t<8?t:"8;5;"+t),i=`  ${s};1m${n} \u001B[0m`;r[0]=i+r[0].split("\n").join("\n"+i),r.push(s+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else r[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+r[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(process.stderr.fd)},t.destroy=s.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=r(402);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[r]=n,e},{}),e.exports=r(897)(t);let{formatters:i}=e.exports;i.o=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},i.O=function(e){return this.inspectOpts.colors=this.useColors,s.inspect(e,this.inspectOpts)}},8973:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function s(e){let t=new URLSearchParams;for(let[r,s]of Object.entries(e))if(Array.isArray(s))for(let e of s)t.append(r,n(e));else t.set(r,n(s));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return s}})},9173:(e,t,r)=>{"use strict";let{Writable:n}=r(7910),s=r(1104),{BINARY_TYPES:i,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:l}=r(5091),{concat:c,toArrayBuffer:u,unmask:h}=r(2743),{isValidStatusCode:d,isValidUTF8:f}=r(9179),p=Buffer[Symbol.species];class g extends n{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||i[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[l]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,r){if(8===this._opcode&&0==this._state)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new p(t.buffer,t.byteOffset+e,t.length-e),new p(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let r=this._buffers[0],n=t.length-e;e>=r.length?t.set(this._buffers.shift(),n):(t.set(new Uint8Array(r.buffer,r.byteOffset,e),n),this._buffers[0]=new p(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0)return void e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));let r=(64&t[0])==64;if(r&&!this._extensions[s.extensionName])return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(r)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(!this._fragmented)return void e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented)return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));this._compressed=r}else{if(!(this._opcode>7)||!(this._opcode<11))return void e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));if(!this._fin)return void e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));if(r)return void e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength)return void e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"))}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked)return void e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"))}else if(this._masked)return void e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),r=t.readUInt32BE(0);if(r>2097151)return void e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));this._payloadLength=0x100000000*r+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0))return void e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&h(t,this._mask)}if(this._opcode>7)return void this.controlMessage(t,e);if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[s.extensionName].decompress(e,this._fin,(e,r)=>{if(e)return t(e);if(r.length){if(this._messageLength+=r.length,this._messageLength>this._maxPayload&&this._maxPayload>0)return void t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));this._fragments.push(r)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let n;n="nodebuffer"===this._binaryType?c(r,t):"arraybuffer"===this._binaryType?u(c(r,t)):r,this._allowSynchronousEvents?(this.emit("message",n,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",n,!0),this._state=0,this.startLoop(e)}))}else{let n=c(r,t);if(!this._skipUTF8Validation&&!f(n))return void e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));5===this._state||this._allowSynchronousEvents?(this.emit("message",n,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",n,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let r=e.readUInt16BE(0);if(!d(r))return void t(this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));let n=new p(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!f(n))return void t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));this._loop=!1,this.emit("conclude",r,n),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,r,n,s){this._loop=!1,this._errored=!0;let i=new e(r?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(i,this.createError),i.code=s,i[a]=n,i}}e.exports=g},9179:(e,t,r)=>{"use strict";let{isUtf8:n}=r(9428);function s(e){let t=e.length,r=0;for(;r<t;)if((128&e[r])==0)r++;else if((224&e[r])==192){if(r+1===t||(192&e[r+1])!=128||(254&e[r])==192)return!1;r+=2}else if((240&e[r])==224){if(r+2>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||224===e[r]&&(224&e[r+1])==128||237===e[r]&&(224&e[r+1])==160)return!1;r+=3}else{if((248&e[r])!=240||r+3>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||(192&e[r+3])!=128||240===e[r]&&(240&e[r+1])==128||244===e[r]&&e[r+1]>143||e[r]>244)return!1;r+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:s,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},n)e.exports.isValidUTF8=function(e){return e.length<24?s(e):n(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=r(6809);e.exports.isValidUTF8=function(e){return e.length<32?s(e):t(e)}}catch(e){}},9252:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},9333:(e,t,r)=>{"use strict";let n,{Duplex:s}=r(7910),{randomFillSync:i}=r(5511),o=r(1104),{EMPTY_BUFFER:a}=r(5091),{isValidStatusCode:l}=r(9179),{mask:c,toBuffer:u}=r(2743),h=Symbol("kByteLength"),d=Buffer.alloc(4),f=8192;class p{constructor(e,t,r){this._extensions=t||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let r,s,o=!1,a=2,l=!1;t.mask&&(r=t.maskBuffer||d,t.generateMask?t.generateMask(r):(8192===f&&(void 0===n&&(n=Buffer.alloc(8192)),i(n,0,8192),f=0),r[0]=n[f++],r[1]=n[f++],r[2]=n[f++],r[3]=n[f++]),l=(r[0]|r[1]|r[2]|r[3])==0,a=6),"string"==typeof e?s=(!t.mask||l)&&void 0!==t[h]?t[h]:(e=Buffer.from(e)).length:(s=e.length,o=t.mask&&t.readOnly&&!l);let u=s;s>=65536?(a+=8,u=127):s>125&&(a+=2,u=126);let p=Buffer.allocUnsafe(o?s+a:a);return(p[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(p[0]|=64),p[1]=u,126===u?p.writeUInt16BE(s,2):127===u&&(p[2]=p[3]=0,p.writeUIntBE(s,4,6)),t.mask)?(p[1]|=128,p[a-4]=r[0],p[a-3]=r[1],p[a-2]=r[2],p[a-1]=r[3],l)?[p,e]:o?(c(e,r,p,a,s),[p]):(c(e,r,e,0,s),[p,e]):[p,e]}close(e,t,r,n){let s;if(void 0===e)s=a;else if("number"==typeof e&&l(e))if(void 0!==t&&t.length){let r=Buffer.byteLength(t);if(r>123)throw RangeError("The message must not be greater than 123 bytes");(s=Buffer.allocUnsafe(2+r)).writeUInt16BE(e,0),"string"==typeof t?s.write(t,2):s.set(t,2)}else(s=Buffer.allocUnsafe(2)).writeUInt16BE(e,0);else throw TypeError("First argument must be a valid error code number");let i={[h]:s.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,s,!1,i,n]):this.sendFrame(p.frame(s,i),n)}ping(e,t,r){let n,s;if("string"==typeof e?(n=Buffer.byteLength(e),s=!1):(n=(e=u(e)).length,s=u.readOnly),n>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[h]:n,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,i,r]):this.sendFrame(p.frame(e,i),r)}pong(e,t,r){let n,s;if("string"==typeof e?(n=Buffer.byteLength(e),s=!1):(n=(e=u(e)).length,s=u.readOnly),n>125)throw RangeError("The data size must not be greater than 125 bytes");let i={[h]:n,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:s,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,i,r]):this.sendFrame(p.frame(e,i),r)}send(e,t,r){let n,s,i=this._extensions[o.extensionName],a=t.binary?2:1,l=t.compress;if("string"==typeof e?(n=Buffer.byteLength(e),s=!1):(n=(e=u(e)).length,s=u.readOnly),this._firstFragment?(this._firstFragment=!1,l&&i&&i.params[i._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=n>=i._threshold),this._compress=l):(l=!1,a=0),t.fin&&(this._firstFragment=!0),i){let i={[h]:n,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:s,rsv1:l};this._deflating?this.enqueue([this.dispatch,e,this._compress,i,r]):this.dispatch(e,this._compress,i,r)}else this.sendFrame(p.frame(e,{[h]:n,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:s,rsv1:!1}),r)}dispatch(e,t,r,n){if(!t)return void this.sendFrame(p.frame(e,r),n);let s=this._extensions[o.extensionName];this._bufferedBytes+=r[h],this._deflating=!0,s.compress(e,r.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof n&&n(e);for(let t=0;t<this._queue.length;t++){let r=this._queue[t],n=r[r.length-1];"function"==typeof n&&n(e)}return}this._bufferedBytes-=r[h],this._deflating=!1,r.readOnly=!1,this.sendFrame(p.frame(t,r),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][h],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][h],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=p},9478:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},9485:(e,t,r)=>{"use strict";let n,s;r.d(t,{io:()=>eN});var i,o={};r.r(o),r.d(o,{Decoder:()=>ev,Encoder:()=>em,PacketType:()=>i,protocol:()=>e_});var a=r(3171),l=r.t(a,2);let c=Object.create(null);c.open="0",c.close="1",c.ping="2",c.pong="3",c.message="4",c.upgrade="5",c.noop="6";let u=Object.create(null);Object.keys(c).forEach(e=>{u[c[e]]=e});let h={type:"error",data:"parser error"},d=({type:e,data:t},r,n)=>n(t instanceof ArrayBuffer||ArrayBuffer.isView(t)?r?t:"b"+f(t,!0).toString("base64"):c[e]+(t||"")),f=(e,t)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!t?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength),p=(e,t)=>{if("string"!=typeof e)return{type:"message",data:g(e,t)};let r=e.charAt(0);return"b"===r?{type:"message",data:g(Buffer.from(e.substring(1),"base64"),t)}:u[r]?e.length>1?{type:u[r],data:e.substring(1)}:{type:u[r]}:h},g=(e,t)=>"arraybuffer"===t?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e),y=(e,t)=>{let r=e.length,n=Array(r),s=0;e.forEach((e,i)=>{d(e,!1,e=>{n[i]=e,++s===r&&t(n.join("\x1e"))})})},_=(e,t)=>{let r=e.split("\x1e"),n=[];for(let e=0;e<r.length;e++){let s=p(r[e],t);if(n.push(s),"error"===s.type)break}return n};function m(e){return e.reduce((e,t)=>e+t.length,0)}function b(e,t){if(e[0].length===t)return e.shift();let r=new Uint8Array(t),n=0;for(let s=0;s<t;s++)r[s]=e[0][n++],n===e[0].length&&(e.shift(),n=0);return e.length&&n<e[0].length&&(e[0]=e[0].slice(n)),r}function v(e){if(e){var t=e;for(var r in v.prototype)t[r]=v.prototype[r];return t}}v.prototype.on=v.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},v.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},v.prototype.off=v.prototype.removeListener=v.prototype.removeAllListeners=v.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var s=0;s<n.length;s++)if((r=n[s])===t||r.fn===t){n.splice(s,1);break}return 0===n.length&&delete this._callbacks["$"+e],this},v.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){r=r.slice(0);for(var n=0,s=r.length;n<s;++n)r[n].apply(this,t)}return this},v.prototype.emitReserved=v.prototype.emit,v.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},v.prototype.hasListeners=function(e){return!!this.listeners(e).length};let C=process.nextTick,E=global;class w{constructor(){this._cookies=new Map}parseCookies(e){e&&e.forEach(e=>{let t=function(e){let t=e.split("; "),r=t[0].indexOf("=");if(-1===r)return;let n=t[0].substring(0,r).trim();if(!n.length)return;let s=t[0].substring(r+1).trim();34===s.charCodeAt(0)&&(s=s.slice(1,-1));let i={name:n,value:s};for(let e=1;e<t.length;e++){let r=t[e].split("=");if(2!==r.length)continue;let n=r[0].trim(),s=r[1].trim();switch(n){case"Expires":i.expires=new Date(s);break;case"Max-Age":let o=new Date;o.setUTCSeconds(o.getUTCSeconds()+parseInt(s,10)),i.expires=o}}return i}(e);t&&this._cookies.set(t.name,t)})}get cookies(){let e=Date.now();return this._cookies.forEach((t,r)=>{var n;(null==(n=t.expires)?void 0:n.getTime())<e&&this._cookies.delete(r)}),this._cookies.entries()}addCookies(e){let t=[];for(let[e,r]of this.cookies)t.push(`${e}=${r.value}`);t.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",t.join("; ")))}appendCookies(e){for(let[t,r]of this.cookies)e.append("cookie",`${t}=${r.value}`)}}function S(e,...t){return t.reduce((t,r)=>(e.hasOwnProperty(r)&&(t[r]=e[r]),t),{})}let O=E.setTimeout,R=E.clearTimeout;function k(e,t){t.useNativeTimers?(e.setTimeoutFn=O.bind(E),e.clearTimeoutFn=R.bind(E)):(e.setTimeoutFn=E.setTimeout.bind(E),e.clearTimeoutFn=E.clearTimeout.bind(E))}function T(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var x=r(8842);let P=x("engine.io-client:transport");class N extends Error{constructor(e,t,r){super(e),this.description=t,this.context=r,this.type="TransportError"}}class A extends v{constructor(e){super(),this.writable=!1,k(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,r){return super.emitReserved("error",new N(e,t,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState?this.write(e):P("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=p(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t}(e);return t.length?"?"+t:""}}let j=x("engine.io-client:polling");class F extends A{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{j("paused"),this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(j("we are currently polling - waiting to pause"),e++,this.once("pollComplete",function(){j("pre-pause polling complete"),--e||t()})),this.writable||(j("we are currently writing - waiting to pause"),e++,this.once("drain",function(){j("pre-pause writing complete"),--e||t()}))}else t()}_poll(){j("polling"),this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){j("polling got data %s",e),_(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState?this._poll():j('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{j("writing close packet"),this.write([{type:"close"}])};"open"===this.readyState?(j("transport open - closing"),e()):(j("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,y(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=T()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let L=!1;try{L="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let M=L,U=x("engine.io-client:polling");function I(){}class D extends F{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,r=location.port;r||(r=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||r!==e.port}}doWrite(e,t){let r=this.request({method:"POST",data:e});r.on("success",t),r.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){U("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class B extends v{constructor(e,t,r){super(),this.createRequest=e,k(this,r),this._opts=r,this._method=r.method||"GET",this._uri=t,this._data=void 0!==r.data?r.data:null,this._create()}_create(){var e;let t=S(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let r=this._xhr=this.createRequest(t);try{U("xhr open %s: %s",this._method,this._uri),r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&r.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{r.setRequestHeader("Accept","*/*")}catch(e){}null==(e=this._opts.cookieJar)||e.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var e;3===r.readyState&&(null==(e=this._opts.cookieJar)||e.parseCookies(r.getResponseHeader("set-cookie"))),4===r.readyState&&(200===r.status||1223===r.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof r.status?r.status:0)},0))},U("xhr data %s",this._data),r.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=B.requestsCount++,B.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=I,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete B.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function H(){for(let e in B.requests)B.requests.hasOwnProperty(e)&&B.requests[e].abort()}B.requestsCount=0,B.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",H):"function"==typeof addEventListener&&addEventListener("onpagehide"in E?"pagehide":"unload",H,!1)),function(){let e=function(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||M))return new XMLHttpRequest}catch(e){}if(!t)try{return new E[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}({xdomain:!1});e&&e.responseType}();let q=a||l;class W extends D{request(e={}){var t;return Object.assign(e,{xd:this.xd,cookieJar:null==(t=this.socket)?void 0:t._cookieJar},this.opts),new B(e=>new q(e),this.uri(),e)}}r(4988),r(9173),r(9333);var V=r(7209);r(9553);let z=x("engine.io-client:websocket"),$="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class G extends A{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,r=$?{}:S(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,r)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;d(r,this.supportsBinary,e=>{try{this.doWrite(r,e)}catch(e){z("websocket closed before onclose event")}n&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=T()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}E.WebSocket||E.MozWebSocket;class K extends G{createSocket(e,t,r){var n;if(null==(n=this.socket)?void 0:n._cookieJar)for(let[e,t]of(r.headers=r.headers||{},r.headers.cookie="string"==typeof r.headers.cookie?[r.headers.cookie]:r.headers.cookie||[],this.socket._cookieJar.cookies))r.headers.cookie.push(`${e}=${t.value}`);return new V(e,t,r)}doWrite(e,t){let r={};e.options&&(r.compress=e.options.compress),this.opts.perMessageDeflate&&("string"==typeof t?Buffer.byteLength(t):t.length)<this.opts.perMessageDeflate.threshold&&(r.compress=!1),this.ws.send(t,r)}}let Y=x("engine.io-client:webtransport");class X extends A{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{Y("transport closed gracefully"),this.onClose()}).catch(e=>{Y("transport closed due to %s",e),this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){s||(s=new TextDecoder);let r=[],n=0,i=-1,o=!1;return new TransformStream({transform(a,l){for(r.push(a);;){if(0===n){if(1>m(r))break;let e=b(r,1);o=(128&e[0])==128,n=(i=127&e[0])<126?3:126===i?1:2}else if(1===n){if(2>m(r))break;let e=b(r,2);i=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),n=3}else if(2===n){if(8>m(r))break;let e=b(r,8),t=new DataView(e.buffer,e.byteOffset,e.length),s=t.getUint32(0);if(s>2097151){l.enqueue(h);break}i=0x100000000*s+t.getUint32(4),n=3}else{if(m(r)<i)break;let e=b(r,i);l.enqueue(p(o?e:s.decode(e),t)),n=0}if(0===i||i>e){l.enqueue(h);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=e.readable.pipeThrough(t).getReader(),i=new TransformStream({transform(e,t){!function(e,t){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return t(f(e.data,!1));d(e,!0,e=>{n||(n=new TextEncoder),t(n.encode(e))})}(e,r=>{let n,s=r.length;if(s<126)new DataView((n=new Uint8Array(1)).buffer).setUint8(0,s);else if(s<65536){let e=new DataView((n=new Uint8Array(3)).buffer);e.setUint8(0,126),e.setUint16(1,s)}else{let e=new DataView((n=new Uint8Array(9)).buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(s))}e.data&&"string"!=typeof e.data&&(n[0]|=128),t.enqueue(n),t.enqueue(r)})}});i.readable.pipeTo(e.writable),this._writer=i.writable.getWriter();let o=()=>{r.read().then(({done:e,value:t})=>{if(e)return void Y("session is closed");Y("received chunk: %o",t),this.onPacket(t),o()}).catch(e=>{Y("an error occurred while reading: %s",e)})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;this._writer.write(r).then(()=>{n&&C(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null==(e=this._transport)||e.close()}}let J={websocket:K,webtransport:X,polling:W},Q=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Z=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ee(e){if(e.length>8e3)throw"URI too long";let t=e,r=e.indexOf("["),n=e.indexOf("]");-1!=r&&-1!=n&&(e=e.substring(0,r)+e.substring(r,n).replace(/:/g,";")+e.substring(n,e.length));let s=Q.exec(e||""),i={},o=14;for(;o--;)i[Z[o]]=s[o]||"";return -1!=r&&-1!=n&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=function(e,t){let r=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&r.splice(0,1),"/"==t.slice(-1)&&r.splice(r.length-1,1),r}(0,i.path),i.queryKey=function(e,t){let r={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,n){t&&(r[t]=n)}),r}(0,i.query),i}let et=x("engine.io-client:socket"),er="function"==typeof addEventListener&&"function"==typeof removeEventListener,en=[];er&&addEventListener("offline",()=>{et("closing %d connection(s) because the network was lost",en.length),en.forEach(e=>e())},!1);class es extends v{constructor(e,t){if(super(),this.binaryType="nodebuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let r=ee(e);t.hostname=r.host,t.secure="https"===r.protocol||"wss"===r.protocol,t.port=r.port,r.query&&(t.query=r.query)}else t.host&&(t.hostname=ee(t.host).host);k(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},r=e.split("&");for(let e=0,n=r.length;e<n;e++){let n=r[e].split("=");t[decodeURIComponent(n[0])]=decodeURIComponent(n[1])}return t}(this.opts.query)),er&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(et("adding listener for the 'offline' event"),this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},en.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=new w),this._open()}createTransport(e){et('creating transport "%s"',e);let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let r=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return et("options: %j",r),new this._transportsByName[e](r)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);let e=this.opts.rememberUpgrade&&es.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){et("setting transport %s",e.name),this.transport&&(et("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){et("socket open"),this.readyState="open",es.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(et('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}else et('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();et("flushing %d packets in socket",e.length),this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let r=this.writeBuffer[t].data;if(r&&(e+="string"==typeof r?function(e){let t=0,r=0;for(let n=0,s=e.length;n<s;n++)(t=e.charCodeAt(n))<128?r+=1:t<2048?r+=2:t<55296||t>=57344?r+=3:(n++,r+=4);return r}(r):Math.ceil(1.33*(r.byteLength||r.size))),t>0&&e>this._maxPayload)return et("only send %d out of %d packets",t,this.writeBuffer.length),this.writeBuffer.slice(0,t);e+=2}return et("payload size is %d (max: %d)",e,this._maxPayload),this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(et("throttled timer detected, scheduling connection close"),this._pingTimeoutTime=0,C(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,r){return this._sendPacket("message",e,t,r),this}send(e,t,r){return this._sendPacket("message",e,t,r),this}_sendPacket(e,t,r,n){if("function"==typeof t&&(n=t,t=void 0),"function"==typeof r&&(n=r,r=null),"closing"===this.readyState||"closed"===this.readyState)return;(r=r||{}).compress=!1!==r.compress;let s={type:e,data:t,options:r};this.emitReserved("packetCreate",s),this.writeBuffer.push(s),n&&this.once("flush",n),this.flush()}close(){let e=()=>{this._onClose("forced close"),et("socket closing - telling transport to close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},r=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():e()}):this.upgrading?r():e()),this}_onError(e){if(et("socket error %j",e),es.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return et("trying next transport"),this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(et('socket close with reason: "%s"',e),this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),er&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=en.indexOf(this._offlineEventListener);-1!==e&&(et("removing listener for the 'offline' event"),en.splice(e,1))}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}es.protocol=4;class ei extends es{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade){et("starting upgrade probes");for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}}_probe(e){et('probing transport "%s"',e);let t=this.createTransport(e),r=!1;es.priorWebsocketSuccess=!1;let n=()=>{r||(et('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",n=>{if(!r)if("pong"===n.type&&"probe"===n.data){if(et('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;es.priorWebsocketSuccess="websocket"===t.name,et('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{r||"closed"!==this.readyState&&(et("changing transport and sending upgrade packet"),c(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{et('probe transport "%s" failed',e);let r=Error("probe error");r.transport=t.name,this.emitReserved("upgradeError",r)}}))};function s(){r||(r=!0,c(),t.close(),t=null)}let i=r=>{let n=Error("probe error: "+r);n.transport=t.name,s(),et('probe transport "%s" failed because of error: %s',e,r),this.emitReserved("upgradeError",n)};function o(){i("transport closed")}function a(){i("socket closed")}function l(e){t&&e.name!==t.name&&(et('"%s" works - aborting "%s"',e.name,t.name),s())}let c=()=>{t.removeListener("open",n),t.removeListener("error",i),t.removeListener("close",o),this.off("close",a),this.off("upgrading",l)};t.once("open",n),t.once("error",i),t.once("close",o),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{r||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let r=0;r<e.length;r++)~this.transports.indexOf(e[r])&&t.push(e[r]);return t}}class eo extends ei{constructor(e,t={}){let r="object"==typeof e?e:t;(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(e=>J[e]).filter(e=>!!e)),super(e,r)}}eo.protocol;var ea=r(6143);let el=ea("socket.io-client:url"),ec="function"==typeof ArrayBuffer,eu=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,eh=Object.prototype.toString,ed="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===eh.call(Blob),ef="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===eh.call(File);function ep(e){return ec&&(e instanceof ArrayBuffer||eu(e))||ed&&e instanceof Blob||ef&&e instanceof File}let eg=r(3959)("socket.io-parser"),ey=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],e_=5;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(i||(i={}));class em{constructor(e){this.replacer=e}encode(e){return(eg("encoding packet %j",e),(e.type===i.EVENT||e.type===i.ACK)&&function e(t,r){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let r=0,n=t.length;r<n;r++)if(e(t[r]))return!0;return!1}if(ep(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&e(t[r]))return!0;return!1}(e))?this.encodeAsBinary({type:e.type===i.EVENT?i.BINARY_EVENT:i.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===i.BINARY_EVENT||e.type===i.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),eg("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=function(e){let t=[],r=e.data;return e.data=function e(t,r){if(!t)return t;if(ep(t)){let e={_placeholder:!0,num:r.length};return r.push(t),e}if(Array.isArray(t)){let n=Array(t.length);for(let s=0;s<t.length;s++)n[s]=e(t[s],r);return n}if("object"==typeof t&&!(t instanceof Date)){let n={};for(let s in t)Object.prototype.hasOwnProperty.call(t,s)&&(n[s]=e(t[s],r));return n}return t}(r,t),e.attachments=t.length,{packet:e,buffers:t}}(e),r=this.encodeAsString(t.packet),n=t.buffers;return n.unshift(r),n}}function eb(e){return"[object Object]"===Object.prototype.toString.call(e)}class ev extends v{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let r=(t=this.decodeString(e)).type===i.BINARY_EVENT;r||t.type===i.BINARY_ACK?(t.type=r?i.EVENT:i.ACK,this.reconstructor=new eC(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(ep(e)||e.base64)if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet");else throw Error("Unknown type: "+e)}decodeString(e){let t=0,r={type:Number(e.charAt(0))};if(void 0===i[r.type])throw Error("unknown packet type "+r.type);if(r.type===i.BINARY_EVENT||r.type===i.BINARY_ACK){let n=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let s=e.substring(n,t);if(s!=Number(s)||"-"!==e.charAt(t))throw Error("Illegal attachments");r.attachments=Number(s)}if("/"===e.charAt(t+1)){let n=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);r.nsp=e.substring(n,t)}else r.nsp="/";let n=e.charAt(t+1);if(""!==n&&Number(n)==n){let n=t+1;for(;++t;){let r=e.charAt(t);if(null==r||Number(r)!=r){--t;break}if(t===e.length)break}r.id=Number(e.substring(n,t+1))}if(e.charAt(++t)){let n=this.tryParse(e.substr(t));if(ev.isPayloadValid(r.type,n))r.data=n;else throw Error("invalid payload")}return eg("decoded %s as %j",e,r),r}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case i.CONNECT:return eb(t);case i.DISCONNECT:return void 0===t;case i.CONNECT_ERROR:return"string"==typeof t||eb(t);case i.EVENT:case i.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===ey.indexOf(t[0]));case i.ACK:case i.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class eC{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,r;let e=(t=this.reconPack,r=this.buffers,t.data=function e(t,r){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<r.length)return r[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let n=0;n<t.length;n++)t[n]=e(t[n],r);else if("object"==typeof t)for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(t[n]=e(t[n],r));return t}(t.data,r),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function eE(e,t,r){return e.on(t,r),function(){e.off(t,r)}}let ew=ea("socket.io-client:socket"),eS=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class eO extends v{constructor(e,t,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[eE(e,"open",this.onopen.bind(this)),eE(e,"packet",this.onpacket.bind(this)),eE(e,"error",this.onerror.bind(this)),eE(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var r,n,s;if(eS.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let o={type:i.EVENT,data:t};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++;ew("emitting packet with ack id %d",e);let r=t.pop();this._registerAckCallback(e,r),o.id=e}let a=null==(n=null==(r=this.io.engine)?void 0:r.transport)?void 0:n.writable,l=this.connected&&!(null==(s=this.io.engine)?void 0:s._hasPingExpired());return this.flags.volatile&&!a?ew("discard packet as the transport is not currently writable"):l?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o),this.flags={},this}_registerAckCallback(e,t){var r;let n=null!=(r=this.flags.timeout)?r:this._opts.ackTimeout;if(void 0===n){this.acks[e]=t;return}let s=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&(ew("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(t,1));ew("event with ack id %d has timed out after %d ms",e,n),t.call(this,Error("operation has timed out"))},n),i=(...e)=>{this.io.clearTimeoutFn(s),t.apply(this,e)};i.withError=!0,this.acks[e]=i}emitWithAck(e,...t){return new Promise((r,n)=>{let s=(e,t)=>e?n(e):r(t);s.withError=!0,t.push(s),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...n)=>{if(r===this._queue[0])return null!==e?r.tryCount>this._opts.retries&&(ew("packet [%d] is discarded after %d tries",r.id,r.tryCount),this._queue.shift(),t&&t(e)):(ew("packet [%d] was successfully sent",r.id),this._queue.shift(),t&&t(null,...n)),r.pending=!1,this._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(e=!1){if(ew("draining queue"),!this.connected||0===this._queue.length)return;let t=this._queue[0];if(t.pending&&!e)return void ew("packet [%d] has already been sent and is waiting for an ack",t.id);t.pending=!0,t.tryCount++,ew("sending packet [%d] (try n\xb0%d)",t.id,t.tryCount),this.flags=t.flags,this.emit.apply(this,t.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){ew("transport is open - connecting"),"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:i.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){ew("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case i.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case i.EVENT:case i.BINARY_EVENT:this.onevent(e);break;case i.ACK:case i.BINARY_ACK:this.onack(e);break;case i.DISCONNECT:this.ondisconnect();break;case i.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];ew("emitting event %j",t),null!=e.id&&(ew("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,r=!1;return function(...n){r||(r=!0,ew("sending ack %j",n),t.packet({type:i.ACK,id:e,data:n}))}}onack(e){let t=this.acks[e.id];if("function"!=typeof t)return void ew("bad ack %s",e.id);delete this.acks[e.id],ew("calling ack %s with %j",e.id,e.data),t.withError&&e.data.unshift(null),t.apply(this,e.data)}onconnect(e,t){ew("socket connected with id %s",e),this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){ew("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&(ew("performing disconnect (%s)",this.nsp),this.packet({type:i.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let r=0;r<t.length;r++)if(e===t[r]){t.splice(r,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let r=0;r<t.length;r++)if(e===t[r]){t.splice(r,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function eR(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}eR.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-r:e+r}return 0|Math.min(e,this.max)},eR.prototype.reset=function(){this.attempts=0},eR.prototype.setMin=function(e){this.ms=e},eR.prototype.setMax=function(e){this.max=e},eR.prototype.setJitter=function(e){this.jitter=e};let ek=ea("socket.io-client:manager");class eT extends v{constructor(e,t){var r;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,k(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!=(r=t.randomizationFactor)?r:.5),this.backoff=new eR({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let n=t.parser||o;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null==(t=this.backoff)||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null==(t=this.backoff)||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null==(t=this.backoff)||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(ek("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;ek("opening %s",this.uri),this.engine=new eo(this.uri,this.opts);let t=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;let n=eE(t,"open",function(){r.onopen(),e&&e()}),s=t=>{ek("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},i=eE(t,"error",s);if(!1!==this._timeout){let e=this._timeout;ek("connect attempt will timeout after %d",e);let r=this.setTimeoutFn(()=>{ek("connect attempt timed out after %d",e),n(),s(Error("timeout")),t.close()},e);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}return this.subs.push(n),this.subs.push(i),this}connect(e){return this.open(e)}onopen(){ek("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(eE(e,"ping",this.onping.bind(this)),eE(e,"data",this.ondata.bind(this)),eE(e,"error",this.onerror.bind(this)),eE(e,"close",this.onclose.bind(this)),eE(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){C(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){ek("error",e),this.emitReserved("error",e)}socket(e,t){let r=this.nsps[e];return r?this._autoConnect&&!r.active&&r.connect():(r=new eO(this,e,t),this.nsps[e]=r),r}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active)return void ek("socket %s is still active, skipping close",e);this._close()}_packet(e){ek("writing packet %j",e);let t=this.encoder.encode(e);for(let r=0;r<t.length;r++)this.engine.write(t[r],e.options)}cleanup(){ek("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){ek("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var r;ek("closed due to %s",e),this.cleanup(),null==(r=this.engine)||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)ek("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();ek("will wait %dms before reconnect attempt",t),this._reconnecting=!0;let r=this.setTimeoutFn(()=>{!e.skipReconnect&&(ek("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(ek("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):(ek("reconnect success"),e.onreconnect())}))},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let ex=ea("socket.io-client"),eP={};function eN(e,t){let r;"object"==typeof e&&(t=e,e=void 0);let n=function(e,t="",r){let n=e;r=r||"undefined"!=typeof location&&location,null==e&&(e=r.protocol+"//"+r.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?r.protocol+e:r.host+e),/^(https?|wss?):\/\//.test(e)||(el("protocol-less url %s",e),e=void 0!==r?r.protocol+"//"+e:"https://"+e),el("parse %s",e),n=ee(e)),!n.port&&(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";let s=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+s+":"+n.port+t,n.href=n.protocol+"://"+s+(r&&r.port===n.port?"":":"+n.port),n}(e,(t=t||{}).path||"/socket.io"),s=n.source,i=n.id,o=n.path,a=eP[i]&&o in eP[i].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?(ex("ignoring socket cache for %s",s),r=new eT(s,t)):(eP[i]||(ex("new io instance for %s",s),eP[i]=new eT(s,t)),r=eP[i]),n.query&&!t.query&&(t.query=n.queryKey),r.socket(n.path,t)}Object.assign(eN,{Manager:eT,Socket:eO,io:eN,connect:eN})},9553:(e,t,r)=>{"use strict";let n=r(4735),s=r(1630),{Duplex:i}=r(7910),{createHash:o}=r(5511),a=r(91),l=r(1104),c=r(3428),u=r(7209),{GUID:h,kWebSocket:d}=r(5091),f=/^[+/0-9A-Za-z]{22}==$/;class p extends n{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:u,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=s.createServer((e,t)=>{let r=s.STATUS_CODES[426];t.writeHead(426,{"Content-Length":r.length,"Content-Type":"text/plain"}),t.end(r)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let r of Object.keys(t))e.on(r,t[r]);return function(){for(let r of Object.keys(t))e.removeListener(r,t[r])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,r,n)=>{this.handleUpgrade(t,r,n,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(g,this);return}if(e&&this.once("close",e),1!==this._state)if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(g,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{g(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,r,n){t.on("error",y);let s=e.headers["sec-websocket-key"],i=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method)return void m(this,e,t,405,"Invalid HTTP method");if(void 0===i||"websocket"!==i.toLowerCase())return void m(this,e,t,400,"Invalid Upgrade header");if(void 0===s||!f.test(s))return void m(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");if(8!==o&&13!==o)return void m(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");if(!this.shouldHandle(e))return void _(t,400);let u=e.headers["sec-websocket-protocol"],h=new Set;if(void 0!==u)try{h=c.parse(u)}catch(r){m(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let d=e.headers["sec-websocket-extensions"],p={};if(this.options.perMessageDeflate&&void 0!==d){let r=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(d);e[l.extensionName]&&(r.accept(e[l.extensionName]),p[l.extensionName]=r)}catch(r){m(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let i={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length)return void this.options.verifyClient(i,(i,o,a,l)=>{if(!i)return _(t,o||401,a,l);this.completeUpgrade(p,s,h,e,t,r,n)});if(!this.options.verifyClient(i))return _(t,401)}this.completeUpgrade(p,s,h,e,t,r,n)}completeUpgrade(e,t,r,n,s,i,c){if(!s.readable||!s.writable)return s.destroy();if(s[d])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return _(s,503);let u=o("sha1").update(t+h).digest("base64"),f=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${u}`],p=new this.options.WebSocket(null,void 0,this.options);if(r.size){let e=this.options.handleProtocols?this.options.handleProtocols(r,n):r.values().next().value;e&&(f.push(`Sec-WebSocket-Protocol: ${e}`),p._protocol=e)}if(e[l.extensionName]){let t=e[l.extensionName].params,r=a.format({[l.extensionName]:[t]});f.push(`Sec-WebSocket-Extensions: ${r}`),p._extensions=e}this.emit("headers",f,n),s.write(f.concat("\r\n").join("\r\n")),s.removeListener("error",y),p.setSocket(s,i,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(p),p.on("close",()=>{this.clients.delete(p),this._shouldEmitClose&&!this.clients.size&&process.nextTick(g,this)})),c(p,n)}}function g(e){e._state=2,e.emit("close")}function y(){this.destroy()}function _(e,t,r,n){r=r||s.STATUS_CODES[t],n={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(r),...n},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${s.STATUS_CODES[t]}\r
`+Object.keys(n).map(e=>`${e}: ${n[e]}`).join("\r\n")+"\r\n\r\n"+r)}function m(e,t,r,n,s){if(e.listenerCount("wsClientError")){let n=Error(s);Error.captureStackTrace(n,m),e.emit("wsClientError",n,r,t)}else _(r,n,s)}e.exports=p},9554:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return s}});let n=r(2245);function s(e,t,r){for(let s in r[1]){let i=r[1][s][0],o=(0,n.createRouterCacheKey)(i),a=t.parallelRoutes.get(s);if(a){let t=new Map(a);t.delete(o),e.parallelRoutes.set(s,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9590:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let o=i.length<=2,[a,l]=i,c=(0,n.createRouterCacheKey)(l),u=r.parallelRoutes.get(a);if(!u)return;let h=t.parallelRoutes.get(a);if(h&&h!==u||(h=new Map(u),t.parallelRoutes.set(a,h)),o)return void h.delete(c);let d=u.get(c),f=h.get(c);f&&d&&(f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},h.set(c,f)),e(f,d,(0,s.getNextFlightSegmentPath)(i)))}}});let n=r(2245),s=r(793);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9634:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,i,o,a,l,c){if(0===Object.keys(o[1]).length){r.head=l;return}for(let u in o[1]){let h,d=o[1][u],f=d[0],p=(0,n.createRouterCacheKey)(f),g=null!==a&&void 0!==a[2][u]?a[2][u]:null;if(i){let n=i.parallelRoutes.get(u);if(n){let i,o=(null==c?void 0:c.kind)==="auto"&&c.status===s.PrefetchCacheEntryStatus.reusable,a=new Map(n),h=a.get(p);i=null!==g?{lazyData:null,rsc:g[1],prefetchRsc:null,head:null,prefetchHead:null,loading:g[3],parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),navigatedAt:t}:o&&h?{lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes),loading:h.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),loading:null,navigatedAt:t},a.set(p,i),e(t,i,h,d,g||null,l,c),r.parallelRoutes.set(u,a);continue}}if(null!==g){let e=g[1],r=g[3];h={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else h={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let y=r.parallelRoutes.get(u);y?y.set(p,h):r.parallelRoutes.set(u,new Map([[p,h]])),e(t,h,void 0,d,g,l,c)}}}});let n=r(2245),s=r(2564);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9736:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],s=r[0];if(Array.isArray(n)&&Array.isArray(s)){if(n[0]!==s[0]||n[2]!==s[2])return!0}else if(n!==s)return!0;if(t[4])return!r[4];if(r[4])return!0;let i=Object.values(t[1])[0],o=Object.values(r[1])[0];return!i||!o||e(i,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9820:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return c},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return h}});let n=r(7006),s=r(2564),i=r(7650);function o(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function a(e,t,r){return o(e,t===s.PrefetchKind.FULL,r)}function l(e){let{url:t,nextUrl:r,tree:n,prefetchCache:i,kind:a,allowAliasing:l=!0}=e,c=function(e,t,r,n,i){for(let a of(void 0===t&&(t=s.PrefetchKind.TEMPORARY),[r,null])){let r=o(e,!0,a),l=o(e,!1,a),c=e.search?r:l,u=n.get(c);if(u&&i){if(u.url.pathname===e.pathname&&u.url.search!==e.search)return{...u,aliased:!0};return u}let h=n.get(l);if(i&&e.search&&t!==s.PrefetchKind.FULL&&h&&!h.key.includes("%"))return{...h,aliased:!0}}if(t!==s.PrefetchKind.FULL&&i){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,a,r,i,l);return c?(c.status=p(c),c.kind!==s.PrefetchKind.FULL&&a===s.PrefetchKind.FULL&&c.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return u({tree:n,url:t,nextUrl:r,prefetchCache:i,kind:null!=a?a:s.PrefetchKind.TEMPORARY})}),a&&c.kind===s.PrefetchKind.TEMPORARY&&(c.kind=a),c):u({tree:n,url:t,nextUrl:r,prefetchCache:i,kind:a||s.PrefetchKind.TEMPORARY})}function c(e){let{nextUrl:t,tree:r,prefetchCache:n,url:i,data:o,kind:l}=e,c=o.couldBeIntercepted?a(i,l,t):a(i,l),u={treeAtTimeOfPrefetch:r,data:Promise.resolve(o),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:c,status:s.PrefetchCacheEntryStatus.fresh,url:i};return n.set(c,u),u}function u(e){let{url:t,kind:r,tree:o,nextUrl:l,prefetchCache:c}=e,u=a(t,r),h=i.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:o,nextUrl:l,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:s}=e,i=n.get(s);if(!i)return;let o=a(t,i.kind,r);return n.set(o,{...i,key:o}),n.delete(s),o}({url:t,existingCacheKey:u,nextUrl:l,prefetchCache:c})),e.prerendered){let t=c.get(null!=r?r:u);t&&(t.kind=s.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:o,data:h,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:u,status:s.PrefetchCacheEntryStatus.fresh,url:t};return c.set(u,d),d}function h(e){for(let[t,r]of e)p(r)===s.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:i}=e;return -1!==i?Date.now()<r+i?s.PrefetchCacheEntryStatus.fresh:s.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+d?n?s.PrefetchCacheEntryStatus.reusable:s.PrefetchCacheEntryStatus.fresh:t===s.PrefetchKind.AUTO&&Date.now()<r+f?s.PrefetchCacheEntryStatus.stale:t===s.PrefetchKind.FULL&&Date.now()<r+f?s.PrefetchCacheEntryStatus.reusable:s.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9962:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>s});var n=0;function s(e){return"__private_"+n+++"_"+e}}};