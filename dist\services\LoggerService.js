"use strict";
// ============================================================================
// LOGGER SERVICE - Structured Logging with Performance Optimization
// ============================================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.LoggerService = void 0;
class LoggerService {
    constructor(logLevel = "INFO", enableConsole = true, enableFile = false, maxBufferSize = 1000) {
        this.logLevel = logLevel;
        this.enableConsole = enableConsole;
        this.enableFile = enableFile;
        this.logBuffer = [];
        this.maxBufferSize = maxBufferSize;
        this.flushInterval = null;
        if (this.enableFile) {
            this.startFlushInterval();
        }
    }
    /**
     * Log debug message
     */
    debug(message, context) {
        if (this.shouldLog("DEBUG")) {
            this.logInternal("DEBUG", message, context);
        }
    }
    /**
     * Log info message
     */
    info(message, context) {
        if (this.shouldLog("INFO")) {
            this.logInternal("INFO", message, context);
        }
    }
    /**
     * Log warning message
     */
    warn(message, context) {
        if (this.shouldLog("WARN")) {
            this.logInternal("WARN", message, context);
        }
    }
    /**
     * Log error message
     */
    error(message, context) {
        if (this.shouldLog("ERROR")) {
            this.logInternal("ERROR", message, context);
        }
    }
    /**
     * Core internal logging method
     */
    logInternal(level, message, context) {
        const logEntry = {
            level,
            message,
            timestamp: Date.now(),
            context: context,
        };
        // Console logging
        if (this.enableConsole) {
            this.logToConsole(logEntry);
        }
        // File logging (buffered)
        if (this.enableFile) {
            this.addToBuffer(logEntry);
        }
    }
    /**
     * Log to console with colors
     */
    logToConsole(entry) {
        const timestamp = new Date(entry.timestamp).toISOString();
        const contextStr = entry.context ? ` ${JSON.stringify(entry.context)}` : "";
        const colors = {
            DEBUG: "\x1b[36m", // Cyan
            INFO: "\x1b[32m", // Green
            WARN: "\x1b[33m", // Yellow
            ERROR: "\x1b[31m", // Red
            RESET: "\x1b[0m", // Reset
        };
        const coloredLevel = `${colors[entry.level]}${entry.level}${colors.RESET}`;
        const logMessage = `[${timestamp}] ${coloredLevel}: ${entry.message}${contextStr}`;
        switch (entry.level) {
            case "DEBUG":
                console.debug(logMessage);
                break;
            case "INFO":
                console.info(logMessage);
                break;
            case "WARN":
                console.warn(logMessage);
                break;
            case "ERROR":
                console.error(logMessage);
                break;
        }
    }
    /**
     * Add log entry to buffer
     */
    addToBuffer(entry) {
        this.logBuffer.push(entry);
        // Flush if buffer is full
        if (this.logBuffer.length >= this.maxBufferSize) {
            this.flushBuffer();
        }
    }
    /**
     * Check if message should be logged based on level
     */
    shouldLog(level) {
        const levels = ["DEBUG", "INFO", "WARN", "ERROR"];
        const currentLevelIndex = levels.indexOf(this.logLevel);
        const messageLevelIndex = levels.indexOf(level);
        return messageLevelIndex >= currentLevelIndex;
    }
    /**
     * Start periodic buffer flushing
     */
    startFlushInterval() {
        this.flushInterval = setInterval(() => {
            this.flushBuffer();
        }, 5000); // Flush every 5 seconds
    }
    /**
     * Flush log buffer to file
     */
    flushBuffer() {
        if (this.logBuffer.length === 0) {
            return;
        }
        // In a real implementation, you would write to a file here
        // For now, we'll just clear the buffer
        this.logBuffer = [];
    }
    /**
     * Set log level
     */
    setLogLevel(level) {
        this.logLevel = level;
    }
    /**
     * Get current log level
     */
    getLogLevel() {
        return this.logLevel;
    }
    /**
     * Enable/disable console logging
     */
    setConsoleLogging(enabled) {
        this.enableConsole = enabled;
    }
    /**
     * Enable/disable file logging
     */
    setFileLogging(enabled) {
        this.enableFile = enabled;
        if (enabled && !this.flushInterval) {
            this.startFlushInterval();
        }
        else if (!enabled && this.flushInterval) {
            clearInterval(this.flushInterval);
            this.flushInterval = null;
        }
    }
    /**
     * Get buffer statistics
     */
    getBufferStats() {
        return {
            size: this.logBuffer.length,
            maxSize: this.maxBufferSize,
            utilization: (this.logBuffer.length / this.maxBufferSize) * 100,
        };
    }
    /**
     * Force flush buffer
     */
    flush() {
        this.flushBuffer();
    }
    /**
     * Cleanup resources
     */
    destroy() {
        if (this.flushInterval) {
            clearInterval(this.flushInterval);
            this.flushInterval = null;
        }
        this.flushBuffer();
    }
    /**
     * Create child logger with context
     */
    child(context) {
        return {
            debug: (message, additionalContext) => this.debug(message, { ...context, ...additionalContext }),
            info: (message, additionalContext) => this.info(message, { ...context, ...additionalContext }),
            warn: (message, additionalContext) => this.warn(message, { ...context, ...additionalContext }),
            error: (message, additionalContext) => this.error(message, { ...context, ...additionalContext }),
        };
    }
    /**
     * Log performance metrics
     */
    performance(operation, duration, context) {
        this.info(`Performance: ${operation} completed in ${duration}ms`, {
            operation,
            duration,
            ...context,
        });
    }
    /**
     * Log with custom level (public interface)
     */
    log(level, message, context) {
        if (this.shouldLog(level)) {
            this.logInternal(level, message, context);
        }
    }
}
exports.LoggerService = LoggerService;
// Singleton instance
exports.logger = new LoggerService(process.env.NODE_ENV === "development" ? "DEBUG" : "INFO", true, // Console logging enabled
false, // File logging disabled for now
1000 // Buffer size
);
exports.default = LoggerService;
//# sourceMappingURL=LoggerService.js.map