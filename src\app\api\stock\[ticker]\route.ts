export async function GET(
  request: Request,
  { params }: { params: Promise<{ ticker: string }> }
) {
  try {
    const resolvedParams = await params;
    const ticker = resolvedParams.ticker;

    if (!ticker) {
      return new Response(
        JSON.stringify({ error: "Ticker parameter is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Fetch data from the WebSocket server
    const response = await fetch("http://localhost:8080/api/data");

    if (!response.ok) {
      throw new Error("Failed to fetch market data");
    }

    const data = await response.json();

    // Find the specific stock by ticker
    const stock = data.latestData?.find(
      (item: any) => item.ticker?.toLowerCase() === ticker.toLowerCase()
    );

    if (!stock) {
      return new Response(JSON.stringify({ error: "Stock not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Enhance the stock data with additional calculated fields
    const enhancedStock = {
      ...stock,
      dayRange: `₹${stock.low?.toFixed(2) || "0.00"} - ₹${
        stock.high?.toFixed(2) || "0.00"
      }`,
      weekRange52: `₹${(stock.low * 0.8)?.toFixed(2) || "0.00"} - ₹${
        (stock.high * 1.2)?.toFixed(2) || "0.00"
      }`, // Mock 52-week range
      marketCap: calculateMarketCap(stock.ltp, stock.volume),
      sector: getSector(stock.ticker),
      industry: getIndustry(stock.ticker),
    };

    return new Response(JSON.stringify(enhancedStock), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Error fetching stock detail:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// Helper function to calculate market cap (mock calculation)
function calculateMarketCap(ltp: number, volume: number): string {
  // This is a simplified calculation - in reality, you'd need shares outstanding
  const mockShares = volume * 1000; // Mock shares outstanding
  const marketCap = ltp * mockShares;

  if (marketCap >= ********000) {
    // 1000 Cr
    return `₹${(marketCap / ********).toFixed(2)} Cr`;
  } else if (marketCap >= ********0) {
    // 10 Cr
    return `₹${(marketCap / ********).toFixed(2)} Cr`;
  } else {
    return `₹${(marketCap / 100000).toFixed(2)} L`;
  }
}

// Helper function to get sector (mock data)
function getSector(ticker: string): string {
  const sectorMap: { [key: string]: string } = {
    RELIANCE: "Energy",
    TCS: "Information Technology",
    INFY: "Information Technology",
    HDFCBANK: "Financial Services",
    ICICIBANK: "Financial Services",
    BHARTIARTL: "Telecommunication",
    ITC: "FMCG",
    SBIN: "Financial Services",
    LT: "Construction",
    HCLTECH: "Information Technology",
    MARUTI: "Automobile",
    BAJFINANCE: "Financial Services",
    ASIANPAINT: "Paints",
    NESTLEIND: "FMCG",
    KOTAKBANK: "Financial Services",
  };

  return sectorMap[ticker.toUpperCase()] || "Others";
}

// Helper function to get industry (mock data)
function getIndustry(ticker: string): string {
  const industryMap: { [key: string]: string } = {
    RELIANCE: "Oil & Gas",
    TCS: "IT Services",
    INFY: "IT Services",
    HDFCBANK: "Private Bank",
    ICICIBANK: "Private Bank",
    BHARTIARTL: "Telecom Services",
    ITC: "Tobacco & FMCG",
    SBIN: "Public Bank",
    LT: "Engineering & Construction",
    HCLTECH: "IT Services",
    MARUTI: "Auto Manufacturer",
    BAJFINANCE: "NBFC",
    ASIANPAINT: "Paints & Coatings",
    NESTLEIND: "Food Products",
    KOTAKBANK: "Private Bank",
  };

  return industryMap[ticker.toUpperCase()] || "Others";
}
