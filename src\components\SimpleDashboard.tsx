"use client";

import React, { useEffect, useState } from "react";
import { io, Socket } from "socket.io-client";

interface MarketData {
  ticker: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: number;
}

const SimpleDashboard: React.FC = () => {
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [, setSocket] = useState<Socket | null>(null);

  useEffect(() => {
    // Simple WebSocket connection - no complex setup
    const socketInstance = io("http://localhost:8080", {
      transports: ["websocket"],
      timeout: 5000,
    });

    socketInstance.on("connect", () => {
      setIsConnected(true);
    });

    socketInstance.on("disconnect", () => {
      setIsConnected(false);
      setMarketData([]); // No demo data, just empty
    });

    // Handle market data updates
    socketInstance.on("marketData", (data: MarketData) => {
      setMarketData((prev) => {
        const newData = [...prev];
        const existingIndex = newData.findIndex(
          (item) => item.ticker === data.ticker
        );
        if (existingIndex >= 0) {
          newData[existingIndex] = data;
        } else {
          newData.push(data);
        }
        return newData.slice(0, 20); // Keep only 20 items for performance
      });
    });

    // Get initial data
    socketInstance.on("initialData", (data: { liveData: MarketData[] }) => {
      setMarketData(data.liveData.slice(0, 20)); // Limit to 20 items
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;
  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    const color = change >= 0 ? "text-green-600" : "text-red-600";
    return (
      <span className={color}>
        {sign}
        {change.toFixed(2)} ({sign}
        {changePercent.toFixed(2)}%)
      </span>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20 dark:from-gray-900 dark:via-blue-900/30 dark:to-indigo-900/20 p-4 transition-colors duration-500">
      {/* Simple Header */}
      <div className="glass dark:glass-dark rounded-2xl shadow-lg p-6 mb-8 transition-all duration-500">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-extrabold gradient-text dark:text-white">
            Market Dashboard
          </h1>
          <div className="flex items-center space-x-2">
            <div
              className={`w-3 h-3 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"} animate-pulse`}
            />
            <span className="text-base text-gray-600 dark:text-gray-300 font-medium">
              {isConnected ? "Live" : "Demo"}
            </span>
          </div>
        </div>
      </div>

      {/* Simple Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="glass dark:glass-dark rounded-2xl shadow-md p-6 text-center">
          <div className="text-sm text-gray-600 dark:text-gray-300">
            Total Stocks
          </div>
          <div className="text-3xl font-extrabold text-gray-900 dark:text-white">
            {marketData.length}
          </div>
        </div>
        <div className="glass dark:glass-dark rounded-2xl shadow-md p-6 text-center">
          <div className="text-sm text-gray-600 dark:text-gray-300">
            Gainers
          </div>
          <div className="text-3xl font-extrabold text-green-600 dark:gradient-text-success">
            {marketData.filter((stock) => stock.change > 0).length}
          </div>
        </div>
        <div className="glass dark:glass-dark rounded-2xl shadow-md p-6 text-center">
          <div className="text-sm text-gray-600 dark:text-gray-300">Losers</div>
          <div className="text-3xl font-extrabold text-red-600 dark:gradient-text-danger">
            {marketData.filter((stock) => stock.change < 0).length}
          </div>
        </div>
      </div>

      {/* Simple Market Data Table */}
      <div className="glass dark:glass-dark rounded-2xl shadow-lg overflow-hidden transition-all duration-500">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Live Market Data
          </h2>
        </div>
        {marketData.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase">
                    Symbol
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase">
                    Price
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase">
                    Change
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-semibold text-gray-500 dark:text-gray-300 uppercase">
                    Volume
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-800">
                {marketData.map((stock) => (
                  <tr
                    key={stock.ticker}
                    className="hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <td className="px-6 py-4 font-medium text-gray-900 dark:text-white">
                      {stock.ticker}
                    </td>
                    <td className="px-6 py-4 text-right text-gray-900 dark:text-white">
                      {formatPrice(stock.ltp)}
                    </td>
                    <td className="px-6 py-4 text-right">
                      {formatChange(stock.change, stock.changePercent)}
                    </td>
                    <td className="px-6 py-4 text-right text-gray-600 dark:text-gray-300">
                      {stock.volume.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-12 text-center">
            <div className="text-gray-500 dark:text-gray-400 text-lg">
              {isConnected
                ? "Waiting for market data..."
                : "No market data available. Market may be closed or feed is disconnected."}
            </div>
          </div>
        )}
      </div>

      {/* Simple Footer */}
      <div className="mt-8 text-center text-sm text-gray-500 dark:text-gray-400">
        Last updated: {new Date().toLocaleTimeString()}
      </div>
    </div>
  );
};

export default SimpleDashboard;
