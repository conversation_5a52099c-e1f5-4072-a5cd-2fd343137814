"use client";

import React, { useEffect, useState } from "react";
import { io, Socket } from "socket.io-client";

interface MarketData {
  ticker: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: number;
}

const SimpleDashboard: React.FC = () => {
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [socket, setSocket] = useState<Socket | null>(null);

  useEffect(() => {
    // Simple WebSocket connection - no complex setup
    const socketInstance = io("http://localhost:8080", {
      transports: ["websocket"],
      timeout: 5000,
    });

    socketInstance.on("connect", () => {
      setIsConnected(true);
    });

    socketInstance.on("disconnect", () => {
      setIsConnected(false);
    });

    // Handle market data updates
    socketInstance.on("marketData", (data: MarketData) => {
      setMarketData(prev => {
        const newData = [...prev];
        const existingIndex = newData.findIndex(item => item.ticker === data.ticker);
        
        if (existingIndex >= 0) {
          newData[existingIndex] = data;
        } else {
          newData.push(data);
        }
        
        return newData.slice(0, 20); // Keep only 20 items for performance
      });
    });

    // Get initial data
    socketInstance.on("initialData", (data: { liveData: MarketData[] }) => {
      setMarketData(data.liveData.slice(0, 20)); // Limit to 20 items
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;
  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    const color = change >= 0 ? "text-green-600" : "text-red-600";
    return (
      <span className={color}>
        {sign}{change.toFixed(2)} ({sign}{changePercent.toFixed(2)}%)
      </span>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Simple Header */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Market Dashboard</h1>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`} />
            <span className="text-sm text-gray-600">
              {isConnected ? "Live" : "Disconnected"}
            </span>
          </div>
        </div>
      </div>

      {/* Simple Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="text-sm text-gray-600">Total Stocks</div>
          <div className="text-2xl font-bold text-gray-900">{marketData.length}</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="text-sm text-gray-600">Gainers</div>
          <div className="text-2xl font-bold text-green-600">
            {marketData.filter(stock => stock.change > 0).length}
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="text-sm text-gray-600">Losers</div>
          <div className="text-2xl font-bold text-red-600">
            {marketData.filter(stock => stock.change < 0).length}
          </div>
        </div>
      </div>

      {/* Simple Market Data Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold text-gray-900">Live Market Data</h2>
        </div>
        
        {marketData.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Symbol</th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Price</th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Change</th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">Volume</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {marketData.map((stock) => (
                  <tr key={stock.ticker} className="hover:bg-gray-50">
                    <td className="px-4 py-3 font-medium text-gray-900">{stock.ticker}</td>
                    <td className="px-4 py-3 text-right text-gray-900">{formatPrice(stock.ltp)}</td>
                    <td className="px-4 py-3 text-right">{formatChange(stock.change, stock.changePercent)}</td>
                    <td className="px-4 py-3 text-right text-gray-600">{stock.volume.toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-8 text-center">
            <div className="text-gray-500">
              {isConnected ? "Waiting for market data..." : "Connecting to market feed..."}
            </div>
          </div>
        )}
      </div>

      {/* Simple Footer */}
      <div className="mt-6 text-center text-sm text-gray-500">
        Last updated: {new Date().toLocaleTimeString()}
      </div>
    </div>
  );
};

export default SimpleDashboard;
