import { DataService as IDataService, MarketData, Watchlist, WatchlistItem, Company } from '@/types';
export declare class DataService implements IDataService {
    private pool;
    private isConnected;
    constructor(connectionConfig: {
        host: string;
        port: number;
        database: string;
        user: string;
        password: string;
        ssl?: any;
    });
    /**
     * Get market data for a specific security
     */
    getMarketData(securityId: string): Promise<MarketData | null>;
    /**
     * Get indices data with caching
     */
    getIndices(limit?: number): Promise<MarketData[]>;
    /**
     * Get all watchlists
     */
    getWatchlists(): Promise<Watchlist[]>;
    /**
     * Get watchlist items
     */
    getWatchlistItems(watchlistId: number): Promise<WatchlistItem[]>;
    /**
     * Get companies with pagination and filtering
     */
    getCompanies(limit?: number, offset?: number, sector?: string, exchange?: string): Promise<{
        companies: Company[];
        total: number;
    }>;
    /**
     * Check database connection
     */
    checkConnection(): Promise<boolean>;
    /**
     * Get connection status
     */
    isConnectionHealthy(): boolean;
    /**
     * Get pool statistics
     */
    getPoolStats(): {
        totalCount: number;
        idleCount: number;
        waitingCount: number;
    };
    /**
     * Close all connections
     */
    close(): Promise<void>;
}
export declare const dataService: DataService;
export default DataService;
//# sourceMappingURL=DataService.d.ts.map