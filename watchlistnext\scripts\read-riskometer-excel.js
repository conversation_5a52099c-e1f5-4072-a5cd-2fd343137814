const XLSX = require("xlsx");
const fs = require("fs");
const path = require("path");

// Function to read and analyze the RiskOMeter Excel file
function readRiskOMeterExcel() {
  try {
    const filePath = path.join(__dirname, "..", "RiskOMeter_Riskometer_Project_Apr25.xlsx");
    
    console.log("📊 Reading RiskOMeter Excel file...");
    console.log(`📁 File path: ${filePath}`);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    
    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    
    console.log("\n📋 Workbook Information:");
    console.log(`📄 Total sheets: ${workbook.SheetNames.length}`);
    console.log(`📝 Sheet names: ${workbook.SheetNames.join(", ")}`);
    
    // Analyze each sheet
    workbook.SheetNames.forEach((sheetName, index) => {
      console.log(`\n${"=".repeat(50)}`);
      console.log(`📊 SHEET ${index + 1}: ${sheetName}`);
      console.log(`${"=".repeat(50)}`);
      
      const worksheet = workbook.Sheets[sheetName];
      
      // Get sheet range
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
      console.log(`📐 Range: ${worksheet['!ref'] || 'Empty'}`);
      console.log(`📏 Rows: ${range.e.r + 1}, Columns: ${range.e.c + 1}`);
      
      // Convert to JSON to analyze data
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (jsonData.length > 0) {
        console.log(`📊 Total rows with data: ${jsonData.length}`);
        
        // Show headers (first row)
        if (jsonData[0]) {
          console.log("\n📋 Headers/First Row:");
          jsonData[0].forEach((header, colIndex) => {
            if (header !== undefined && header !== null && header !== "") {
              console.log(`  Column ${colIndex + 1}: ${header}`);
            }
          });
        }
        
        // Show sample data (first few rows)
        console.log("\n📝 Sample Data (first 5 rows):");
        jsonData.slice(0, 5).forEach((row, rowIndex) => {
          console.log(`  Row ${rowIndex + 1}:`, row.filter(cell => cell !== undefined && cell !== null && cell !== ""));
        });
        
        // Convert to JSON with headers for better analysis
        const jsonWithHeaders = XLSX.utils.sheet_to_json(worksheet);
        
        if (jsonWithHeaders.length > 0) {
          console.log(`\n📊 Records with headers: ${jsonWithHeaders.length}`);
          
          // Show first record structure
          console.log("\n📋 First Record Structure:");
          const firstRecord = jsonWithHeaders[0];
          Object.keys(firstRecord).forEach(key => {
            console.log(`  ${key}: ${firstRecord[key]}`);
          });
          
          // Analyze data types and unique values for key columns
          console.log("\n📈 Data Analysis:");
          const columnAnalysis = {};
          
          Object.keys(firstRecord).forEach(column => {
            const values = jsonWithHeaders.map(row => row[column]).filter(val => val !== undefined && val !== null && val !== "");
            const uniqueValues = [...new Set(values)];
            
            columnAnalysis[column] = {
              totalValues: values.length,
              uniqueValues: uniqueValues.length,
              sampleValues: uniqueValues.slice(0, 5),
              dataType: typeof values[0]
            };
          });
          
          Object.entries(columnAnalysis).forEach(([column, analysis]) => {
            console.log(`  📊 ${column}:`);
            console.log(`    - Total values: ${analysis.totalValues}`);
            console.log(`    - Unique values: ${analysis.uniqueValues}`);
            console.log(`    - Data type: ${analysis.dataType}`);
            console.log(`    - Sample values: ${analysis.sampleValues.join(", ")}`);
          });
        }
      } else {
        console.log("📭 Sheet is empty");
      }
    });
    
    // Save processed data to JSON files for further analysis
    console.log("\n💾 Saving processed data to JSON files...");
    
    workbook.SheetNames.forEach(sheetName => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      
      if (jsonData.length > 0) {
        const outputPath = path.join(__dirname, "..", `riskometer_${sheetName.replace(/[^a-zA-Z0-9]/g, '_')}.json`);
        fs.writeFileSync(outputPath, JSON.stringify(jsonData, null, 2));
        console.log(`✅ Saved ${sheetName} data to: ${outputPath}`);
      }
    });
    
    console.log("\n🎉 RiskOMeter Excel analysis completed!");
    
  } catch (error) {
    console.error("❌ Error reading RiskOMeter Excel file:", error.message);
    throw error;
  }
}

// Run the analysis
if (require.main === module) {
  readRiskOMeterExcel();
}

module.exports = { readRiskOMeterExcel };
