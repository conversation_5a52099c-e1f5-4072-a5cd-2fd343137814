"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Layers,
  Activity,
} from "lucide-react";
import { useRouter } from "next/navigation";

interface StockData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
  companyName?: string;
  sector?: string;
  industry?: string;
  subSector?: string;
}

interface CompanyInfo {
  company_name: string;
  nse_symbol?: string;
  bse_symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_name?: string;
  sub_sector_name?: string;
}

interface SubSectorData {
  subSector: string;
  stocks: StockData[];
  totalStocks: number;
  gainers: number;
  losers: number;
  avgChange: number;
  totalVolume: number;
}

export default function SubSectorDashboard() {
  const router = useRouter();
  const [selectedExchange, setSelectedExchange] = useState<"NSE" | "BSE">(
    "NSE"
  );
  const [subSectorData, setSubSectorData] = useState<SubSectorData[]>([]);
  const [loading, setLoading] = useState(true);
  const [companies, setCompanies] = useState<{ [key: string]: CompanyInfo }>(
    {}
  );

  // Fetch company information
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await fetch("/api/companies?limit=5000");
        const data = await response.json();

        if (data.success) {
          const companyMap: { [key: string]: CompanyInfo } = {};
          data.data.forEach((company: CompanyInfo) => {
            if (company.nse_symbol) {
              companyMap[company.nse_symbol] = company;
            }
            if (
              company.bse_symbol &&
              company.bse_symbol !== company.nse_symbol
            ) {
              companyMap[company.bse_symbol] = company;
            }
          });
          setCompanies(companyMap);
        }
      } catch (error) {
        console.error("Error fetching companies:", error);
      }
    };

    fetchCompanies();
  }, []);

  // Fetch market data and organize by sub-sector
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/data?exchange=${selectedExchange}_EQ&limit=25000`
        );
        const data = await response.json();

        if (data.latestData) {
          // Group stocks by sub-sector
          const subSectorMap: { [key: string]: StockData[] } = {};

          data.latestData.forEach((stock: StockData) => {
            const companyInfo = companies[stock.ticker];
            const subSector = companyInfo?.sub_sector_name || "Others";

            const enrichedStock = {
              ...stock,
              companyName: companyInfo?.company_name || stock.ticker,
              sector: companyInfo?.sector_name || "Unknown",
              industry: companyInfo?.industry_name || "Unknown",
              subSector: subSector,
            };

            if (!subSectorMap[subSector]) {
              subSectorMap[subSector] = [];
            }
            subSectorMap[subSector].push(enrichedStock);
          });

          // Convert to sub-sector data with statistics
          const subSectors: SubSectorData[] = Object.entries(subSectorMap).map(
            ([subSector, stocks]) => {
              const gainers = stocks.filter((s) => s.change > 0).length;
              const losers = stocks.filter((s) => s.change < 0).length;
              const avgChange =
                stocks.reduce((sum, s) => sum + s.changePercent, 0) /
                stocks.length;
              const totalVolume = stocks.reduce((sum, s) => sum + s.volume, 0);

              return {
                subSector,
                stocks: stocks.sort(
                  (a, b) =>
                    Math.abs(b.changePercent) - Math.abs(a.changePercent)
                ), // Sort by highest change
                totalStocks: stocks.length,
                gainers,
                losers,
                avgChange,
                totalVolume,
              };
            }
          );

          // Sort sub-sectors by average change
          subSectors.sort(
            (a, b) => Math.abs(b.avgChange) - Math.abs(a.avgChange)
          );
          setSubSectorData(subSectors);
        }
      } catch (error) {
        console.error("Error fetching market data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (Object.keys(companies).length > 0) {
      fetchMarketData();
    }
  }, [selectedExchange, companies]);

  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;
  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const handleViewAllStocks = (subSectorName: string) => {
    // Navigate to a sub-sector-specific page with all stocks
    const encodedSubSector = encodeURIComponent(subSectorName);
    router.push(
      `/sub-sectors/${encodedSubSector}?exchange=${selectedExchange}`
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading sub-sector data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Professional Header */}
      <div className="page-header rounded-2xl p-8 shadow-large">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center justify-center w-20 h-20 bg-gradient-trading rounded-2xl shadow-glow">
              <Layers className="h-10 w-10 text-white" />
            </div>
            <div>
              <h1 className="header-title text-4xl font-bold mb-2">
                Sub-Sector Analysis
              </h1>
              <p className="text-gray-600 text-lg">
                Real-time sub-sector performance and market insights
              </p>
              <div className="flex items-center gap-4 mt-3">
                <div className="status-indicator live">
                  <span>Live Data</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Activity className="w-4 h-4" />
                  <span>{selectedExchange} Exchange</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex bg-white rounded-xl p-1 shadow-md border border-gray-200">
              <Button
                variant={selectedExchange === "NSE" ? "gradient" : "ghost"}
                size="sm"
                onClick={() => setSelectedExchange("NSE")}
                className="font-semibold"
              >
                NSE
              </Button>
              <Button
                variant={selectedExchange === "BSE" ? "gradient" : "ghost"}
                size="sm"
                onClick={() => setSelectedExchange("BSE")}
                className="font-semibold"
              >
                BSE
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Sub-Sector Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card variant="elevated" className="trading-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  Total Sub-Sectors
                </p>
                <p className="text-3xl font-bold text-gray-900 price-ticker">
                  {subSectorData.length}
                </p>
                <p className="text-xs text-gray-500 mt-1">Active markets</p>
              </div>
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="elevated" className="trading-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  Total Stocks
                </p>
                <p className="text-3xl font-bold text-gray-900 price-ticker">
                  {subSectorData.reduce(
                    (sum, subSector) => sum + subSector.totalStocks,
                    0
                  )}
                </p>
                <p className="text-xs text-gray-500 mt-1">Listed companies</p>
              </div>
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl">
                <Layers className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="elevated" className="trading-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  Gainers
                </p>
                <p className="text-3xl font-bold text-green-600 price-ticker">
                  {subSectorData.reduce(
                    (sum, subSector) => sum + subSector.gainers,
                    0
                  )}
                </p>
                <p className="text-xs text-green-600 mt-1">Positive momentum</p>
              </div>
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="elevated" className="trading-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Losers</p>
                <p className="text-3xl font-bold text-red-600 price-ticker">
                  {subSectorData.reduce(
                    (sum, subSector) => sum + subSector.losers,
                    0
                  )}
                </p>
                <p className="text-xs text-red-600 mt-1">Negative momentum</p>
              </div>
              <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-xl">
                <TrendingDown className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Sub-Sector Cards */}
      <div className="sector-grid">
        {subSectorData.map((subSector) => (
          <Card
            key={subSector.subSector}
            variant="elevated"
            className="trading-card group cursor-pointer"
            onClick={() => handleViewAllStocks(subSector.subSector)}
          >
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between mb-3">
                <CardTitle className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                  {subSector.subSector}
                </CardTitle>
                <Badge
                  variant={subSector.avgChange >= 0 ? "success" : "destructive"}
                  size="lg"
                  className="font-semibold"
                >
                  {subSector.avgChange >= 0 ? "+" : ""}
                  {subSector.avgChange.toFixed(2)}%
                </Badge>
              </div>

              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="bg-gray-50 rounded-lg p-3">
                  <p className="text-2xl font-bold text-gray-900">
                    {subSector.totalStocks}
                  </p>
                  <p className="text-xs text-gray-600 mt-1">Stocks</p>
                </div>
                <div className="bg-green-50 rounded-lg p-3">
                  <p className="text-2xl font-bold text-green-600">
                    {subSector.gainers}
                  </p>
                  <p className="text-xs text-green-600 mt-1">Gainers</p>
                </div>
                <div className="bg-red-50 rounded-lg p-3">
                  <p className="text-2xl font-bold text-red-600">
                    {subSector.losers}
                  </p>
                  <p className="text-xs text-red-600 mt-1">Losers</p>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              <div className="space-y-2">
                {subSector.stocks.slice(0, 3).map((stock) => (
                  <div
                    key={stock.securityId}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg group-hover:bg-blue-50 transition-colors"
                  >
                    <div className="flex-1 min-w-0">
                      <p className="font-semibold text-sm text-gray-900 truncate">
                        {stock.ticker}
                      </p>
                      <p className="text-xs text-gray-600 truncate">
                        {stock.companyName}
                      </p>
                    </div>
                    <div className="text-right ml-3">
                      <p className="font-bold text-sm text-gray-900">
                        {formatPrice(stock.ltp)}
                      </p>
                      <p
                        className={`text-xs font-medium ${
                          stock.change >= 0 ? "text-green-600" : "text-red-600"
                        }`}
                      >
                        {formatChange(stock.change, stock.changePercent)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {subSector.stocks.length > 3 && (
                <div className="pt-4 border-t border-gray-100">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full font-semibold group-hover:bg-blue-50 group-hover:border-blue-300 group-hover:text-blue-600 transition-all"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewAllStocks(subSector.subSector);
                    }}
                  >
                    View all {subSector.stocks.length} stocks
                    <svg
                      className="w-4 h-4 ml-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
