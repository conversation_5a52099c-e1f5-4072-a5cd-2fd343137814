const { Client } = require("pg");
const XLSX = require("xlsx");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

// Database connection
const client = new Client({
  connectionString: process.env.POSTGRES_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false, // Accept self-signed certificates
  },
});

// Create companies table
const createTableSQL = `
  CREATE TABLE IF NOT EXISTS companies (
    id SERIAL PRIMARY KEY,
    face_value DECIMAL(10,2),
    company_name VARCHAR(500) NOT NULL,
    group_bse VARCHAR(10),
    isin_no VARCHAR(20) UNIQUE,
    instrument VARCHAR(50),
    sector_name VARCHAR(200),
    industry_name VARCHAR(200),
    sub_sector VARCHAR(200),
    micro_category VARCHAR(200),
    bse_security_id VARCHAR(50),
    nse_security_id VARCHAR(50),
    nse_symbol VARCHAR(50),
    nse_series VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
`;

// Create indexes for better performance
const createIndexesSQL = [
  `CREATE INDEX IF NOT EXISTS idx_companies_isin ON companies(isin_no);`,
  `CREATE INDEX IF NOT EXISTS idx_companies_nse_symbol ON companies(nse_symbol);`,
  `CREATE INDEX IF NOT EXISTS idx_companies_bse_security_id ON companies(bse_security_id);`,
  `CREATE INDEX IF NOT EXISTS idx_companies_nse_security_id ON companies(nse_security_id);`,
  `CREATE INDEX IF NOT EXISTS idx_companies_sector ON companies(sector_name);`,
  `CREATE INDEX IF NOT EXISTS idx_companies_industry ON companies(industry_name);`,
];

async function setupDatabase() {
  try {
    console.log("🔌 Connecting to PostgreSQL database...");
    await client.connect();
    console.log("✅ Connected to database");

    // Create table
    console.log("📋 Creating companies table...");
    await client.query(createTableSQL);
    console.log("✅ Companies table created/verified");

    // Create indexes
    console.log("🔍 Creating database indexes...");
    for (const indexSQL of createIndexesSQL) {
      await client.query(indexSQL);
    }
    console.log("✅ Database indexes created");

    // Check if data already exists
    const countResult = await client.query("SELECT COUNT(*) FROM companies");
    const existingCount = parseInt(countResult.rows[0].count);

    if (existingCount > 0) {
      console.log(`📊 Found ${existingCount} existing records in database`);
      const answer = await askQuestion(
        "Do you want to clear existing data and reload? (y/N): "
      );
      if (answer.toLowerCase() !== "y") {
        console.log("✅ Keeping existing data");
        return;
      }

      console.log("🗑️ Clearing existing data...");
      await client.query("DELETE FROM companies");
      console.log("✅ Existing data cleared");
    }

    // Read Excel file
    console.log("📖 Reading Excel file...");
    const filePath = path.join(__dirname, "..", "Companydetails.xlsx");

    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel file not found at: ${filePath}`);
    }

    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    console.log(`📊 Found ${jsonData.length} records in Excel file`);

    // Insert data in batches
    const batchSize = 100;
    let insertedCount = 0;
    let errorCount = 0;

    console.log("💾 Inserting data into database...");

    for (let i = 0; i < jsonData.length; i += batchSize) {
      const batch = jsonData.slice(i, i + batchSize);

      try {
        await client.query("BEGIN");

        for (const row of batch) {
          try {
            const insertSQL = `
              INSERT INTO companies (
                face_value, company_name, group_bse, isin_no, instrument,
                sector_name, industry_name, sub_sector, micro_category,
                bse_security_id, nse_security_id, nse_symbol, nse_series
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
              ON CONFLICT (isin_no) DO UPDATE SET
                company_name = EXCLUDED.company_name,
                sector_name = EXCLUDED.sector_name,
                industry_name = EXCLUDED.industry_name,
                updated_at = CURRENT_TIMESTAMP
            `;

            const values = [
              parseFloat(row["Face Value"]) || null,
              row["Company Name"] || null,
              row["Group BSE"] || null,
              row["ISIN No"] || null,
              row["Instrument"] || null,
              row["Sector Name"] || null,
              row["Industry Name"] || null,
              row["Sub-Sector"] || null,
              row["Micro Category"] || null,
              row["BSE_SECURITY_ID"] || null,
              row["NSE_SECURITY_ID"] || null,
              row["NSE_SYMBOL"] || null,
              row["NSE_SERIES"] || null,
            ];

            await client.query(insertSQL, values);
            insertedCount++;
          } catch (rowError) {
            console.error(
              `Error inserting row ${insertedCount + errorCount + 1}:`,
              rowError.message
            );
            errorCount++;
          }
        }

        await client.query("COMMIT");

        // Progress update
        const progress = Math.round(((i + batchSize) / jsonData.length) * 100);
        console.log(
          `📈 Progress: ${progress}% (${insertedCount} inserted, ${errorCount} errors)`
        );
      } catch (batchError) {
        await client.query("ROLLBACK");
        console.error(
          `Error processing batch starting at row ${i + 1}:`,
          batchError.message
        );
        errorCount += batch.length;
      }
    }

    console.log("\n🎉 Database setup completed!");
    console.log(`✅ Successfully inserted: ${insertedCount} records`);
    console.log(`❌ Errors: ${errorCount} records`);

    // Verify data
    const finalCount = await client.query("SELECT COUNT(*) FROM companies");
    console.log(`📊 Total records in database: ${finalCount.rows[0].count}`);

    // Show sample data
    const sampleData = await client.query("SELECT * FROM companies LIMIT 3");
    console.log("\n📋 Sample data:");
    sampleData.rows.forEach((row, index) => {
      console.log(
        `${index + 1}. ${row.company_name} (${row.nse_symbol}) - ${row.sector_name}`
      );
    });
  } catch (error) {
    console.error("❌ Database setup failed:", error.message);
    process.exit(1);
  } finally {
    await client.end();
    console.log("🔌 Database connection closed");
  }
}

// Helper function to ask questions in terminal
function askQuestion(question) {
  const readline = require("readline");
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

// Run the setup
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
