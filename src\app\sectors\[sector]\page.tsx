"use client";

import React, { useState, useEffect } from "react";
import { useParams, useSearchParams, useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  TrendingUp,
  TrendingDown,
  ArrowLeft,
  Search,
  Building2,
  BarChart3,
} from "lucide-react";

interface StockData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
  companyName?: string;
  sector?: string;
  industry?: string;
}

interface CompanyInfo {
  company_name: string;
  nse_symbol?: string;
  bse_symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_name?: string;
}

export default function SectorDetailPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();

  const sectorName = decodeURIComponent(params.sector as string);
  const exchange = searchParams.get("exchange") || "NSE";

  const [stocks, setStocks] = useState<StockData[]>([]);
  const [filteredStocks, setFilteredStocks] = useState<StockData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"change" | "volume" | "price">("change");
  const [companies, setCompanies] = useState<{ [key: string]: CompanyInfo }>(
    {}
  );

  // Fetch company information
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await fetch("/api/companies?limit=5000");
        const data = await response.json();

        if (data.success) {
          const companyMap: { [key: string]: CompanyInfo } = {};
          data.data.forEach((company: CompanyInfo) => {
            if (company.nse_symbol) {
              companyMap[company.nse_symbol] = company;
            }
            if (
              company.bse_symbol &&
              company.bse_symbol !== company.nse_symbol
            ) {
              companyMap[company.bse_symbol] = company;
            }
          });
          setCompanies(companyMap);
        }
      } catch (error) {
        console.error("Error fetching companies:", error);
      }
    };

    fetchCompanies();
  }, []);

  // Fetch market data for the sector
  useEffect(() => {
    const fetchSectorData = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/data?exchange=${exchange}_EQ&limit=25000`
        );
        const data = await response.json();

        if (data.latestData) {
          const sectorStocks = data.latestData
            .map((stock: StockData) => {
              const companyInfo = companies[stock.ticker];
              return {
                ...stock,
                companyName: companyInfo?.company_name || stock.ticker,
                sector: companyInfo?.sector_name || "Others",
                industry: companyInfo?.industry_name || "Unknown",
              };
            })
            .filter(
              (stock: StockData) =>
                stock.sector === sectorName ||
                (sectorName === "Others" &&
                  (!stock.sector || stock.sector === "Others"))
            );

          setStocks(sectorStocks);
          setFilteredStocks(sectorStocks);
        }
      } catch (error) {
        console.error("Error fetching sector data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (Object.keys(companies).length > 0) {
      fetchSectorData();
    }
  }, [sectorName, exchange, companies]);

  // Filter and sort stocks
  useEffect(() => {
    let filtered = stocks.filter(
      (stock) =>
        stock.ticker.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stock.companyName?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort stocks
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "change":
          return Math.abs(b.changePercent) - Math.abs(a.changePercent);
        case "volume":
          return b.volume - a.volume;
        case "price":
          return b.ltp - a.ltp;
        default:
          return 0;
      }
    });

    setFilteredStocks(filtered);
  }, [stocks, searchTerm, sortBy]);

  const formatPrice = (price: number) => `₹${price.toFixed(2)}`;
  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
    if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toString();
  };

  const gainers = filteredStocks.filter((s) => s.change > 0).length;
  const losers = filteredStocks.filter((s) => s.change < 0).length;
  const avgChange =
    filteredStocks.length > 0
      ? filteredStocks.reduce((sum, s) => sum + s.changePercent, 0) /
        filteredStocks.length
      : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading {sectorName} sector data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Building2 className="h-8 w-8" />
              {sectorName}
            </h1>
            <p className="text-muted-foreground">{exchange} Exchange</p>
          </div>
        </div>
      </div>

      {/* Sector Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Stocks</p>
                <p className="text-2xl font-bold">{filteredStocks.length}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Average Change</p>
                <p
                  className={`text-2xl font-bold ${avgChange >= 0 ? "text-green-600" : "text-red-600"}`}
                >
                  {avgChange >= 0 ? "+" : ""}
                  {avgChange.toFixed(2)}%
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Gainers</p>
                <p className="text-2xl font-bold text-green-600">{gainers}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Losers</p>
                <p className="text-2xl font-bold text-red-600">{losers}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search stocks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Button
            variant={sortBy === "change" ? "default" : "outline"}
            size="sm"
            onClick={() => setSortBy("change")}
          >
            Sort by Change
          </Button>
          <Button
            variant={sortBy === "volume" ? "default" : "outline"}
            size="sm"
            onClick={() => setSortBy("volume")}
          >
            Sort by Volume
          </Button>
          <Button
            variant={sortBy === "price" ? "default" : "outline"}
            size="sm"
            onClick={() => setSortBy("price")}
          >
            Sort by Price
          </Button>
        </div>
      </div>

      {/* Stocks List */}
      <div className="grid gap-4">
        {filteredStocks.map((stock) => (
          <Card
            key={stock.securityId}
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => router.push(`/stock/${stock.ticker}`)}
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div>
                      <h3 className="font-semibold text-lg">{stock.ticker}</h3>
                      <p className="text-sm text-muted-foreground truncate max-w-xs">
                        {stock.companyName}
                      </p>
                      {stock.industry && stock.industry !== "Unknown" && (
                        <Badge variant="outline" className="text-xs mt-1">
                          {stock.industry}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="text-right space-y-1">
                  <div className="text-xl font-bold">
                    {formatPrice(stock.ltp)}
                  </div>
                  <div
                    className={`text-sm font-medium ${stock.change >= 0 ? "text-green-600" : "text-red-600"}`}
                  >
                    {formatChange(stock.change, stock.changePercent)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Vol: {formatVolume(stock.volume)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredStocks.length === 0 && !loading && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            No stocks found in {sectorName} sector.
          </p>
        </div>
      )}
    </div>
  );
}
