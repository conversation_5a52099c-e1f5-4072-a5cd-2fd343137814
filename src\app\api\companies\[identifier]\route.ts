import { NextRequest, NextResponse } from 'next/server';
import DatabaseService from '../../../../services/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { identifier: string } }
) {
  try {
    const identifier = params.identifier;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'symbol'; // symbol, isin, security_id

    let company = null;

    switch (type) {
      case 'isin':
        company = await DatabaseService.getCompanyByISIN(identifier);
        break;
      case 'security_id':
        company = await DatabaseService.getCompanyBySecurityId(identifier);
        break;
      case 'symbol':
      default:
        company = await DatabaseService.getCompanyBySymbol(identifier);
        break;
    }

    if (!company) {
      return NextResponse.json({
        success: false,
        error: 'Company not found',
        identifier,
        type
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: company
    });

  } catch (error) {
    console.error('Company lookup API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
