import DatabaseService from "../../../../services/database";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ identifier: string }> }
) {
  try {
    const resolvedParams = await params;
    const identifier = resolvedParams.identifier;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type") || "symbol"; // symbol, isin, security_id

    let company = null;

    switch (type) {
      case "isin":
        company = await DatabaseService.getCompanyByISIN(identifier);
        break;
      case "security_id":
        company = await DatabaseService.getCompanyBySecurityId(identifier);
        break;
      case "symbol":
      default:
        company = await DatabaseService.getCompanyBySymbol(identifier);
        break;
    }

    if (!company) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Company not found",
          identifier,
          type,
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: company,
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Company lookup API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
