"use client";

import React, { useEffect, useState, useCallback } from "react";
import { io, Socket } from "socket.io-client";
import { MarketData as MarketDataType } from "../types";
import MarketData from "./MarketData";

interface DatabaseStats {
  totalCompanies: number;
  totalSectors: number;
  totalIndustries: number;
  companiesWithNSESymbol: number;
  companiesWithBSEId: number;
  sectorDistribution: Array<{
    sector: string;
    count: number;
    percentage: number;
  }>;
}

interface ServerStats {
  connected: boolean;
  instruments: number;
  subscriptionType: string;
  activeInstruments: number;
  totalInstruments: number;
}

const Dashboard: React.FC = () => {
  const [marketData, setMarketData] = useState<Map<string, MarketDataType>>(
    new Map()
  );
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [databaseStats, setDatabaseStats] = useState<DatabaseStats | null>(
    null
  );
  const [serverStats, setServerStats] = useState<ServerStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedSector, setSelectedSector] = useState<string>("all");

  // Fetch database statistics
  const fetchDatabaseStats = useCallback(async () => {
    try {
      const response = await fetch("http://localhost:8080/api/database-stats");
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setDatabaseStats(result.data);
        }
      }
    } catch (error) {
      console.error("Error fetching database stats:", error);
    }
  }, []);

  // Fetch server statistics
  const fetchServerStats = useCallback(async () => {
    try {
      const response = await fetch("http://localhost:8080/api/data");
      if (response.ok) {
        const result = await response.json();
        setServerStats({
          connected: result.connected,
          instruments: result.instruments,
          subscriptionType: result.subscriptionType,
          activeInstruments: result.activeInstruments,
          totalInstruments: result.totalInstruments,
        });
      }
    } catch (error) {
      console.error("Error fetching server stats:", error);
    }
  }, []);

  // Reload instruments from database
  const reloadInstruments = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(
        "http://localhost:8080/api/reload-instruments",
        {
          method: "POST",
        }
      );
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          await fetchServerStats();
          console.log("Instruments reloaded successfully");
        }
      }
    } catch (error) {
      console.error("Error reloading instruments:", error);
    } finally {
      setLoading(false);
    }
  }, [fetchServerStats]);

  const connectSocketIO = useCallback(() => {
    // Ensure any previous socket is disconnected before creating a new one
    if (socket) {
      socket.disconnect();
    }

    const newSocket = io("http://localhost:8080", {
      reconnectionAttempts: 5,
      reconnectionDelay: 5000,
    });

    newSocket.on("connect", () => {
      setIsConnected(true);
      setError(null);
      console.log("Socket.IO connected");
      // Subscribe to all symbols - assuming server expects a 'subscribe' event
      // The structure of subscribeMessage might need adjustment based on server implementation
      const subscribeMessage = {
        type: "subscribe", // This might need to be an array of instruments or specific format
        instruments: Array.from(marketData.keys()).map((key) => ({
          symbol: key,
        })), // Example, adjust as per server
      };
      newSocket.emit("subscribe", subscribeMessage);
    });

    newSocket.on("marketData", (message: MarketDataType) => {
      // Assuming server emits 'marketData' with MarketDataType
      if (message && message.symbol) {
        setMarketData((prev) => {
          const newMap = new Map(prev);
          newMap.set(message.symbol, message);
          return newMap;
        });
      } else {
        // Handle cases where message might not be structured as expected
        // console.warn("Received marketData without symbol or data:", message);
      }
    });

    // Listen for 'liveDataUpdate' which seems to be what the server emits in handleMessage
    newSocket.on(
      "liveDataUpdate",
      (data: { symbol: string; [key: string]: any }) => {
        if (data && data.symbol) {
          setMarketData((prev) => {
            const newMap = new Map(prev);
            // Map server data to the MarketDataType structure
            const marketUpdate: MarketDataType = {
              symbol: data.symbol,
              exchange: data.exchangeName || "", // Ensure exchange is always a string
              lastPrice: typeof data.ltp === "number" ? data.ltp : 0,
              change:
                typeof data.priceChange === "number" ? data.priceChange : 0,
              volume: typeof data.volume === "number" ? data.volume : 0,
              high: typeof data.dayHigh === "number" ? data.dayHigh : 0,
              low: typeof data.dayLow === "number" ? data.dayLow : 0,
              open: typeof data.dayOpen === "number" ? data.dayOpen : 0,
              close: typeof data.dayClose === "number" ? data.dayClose : 0, // Or data.previousClose if available
              timestamp: data.timestamp
                ? typeof data.timestamp === "string"
                  ? parseInt(data.timestamp, 10)
                  : typeof data.timestamp === "number"
                    ? data.timestamp
                    : Date.now()
                : Date.now(),
              // ltq, ltt, atp, totalSellQuantity, totalBuyQuantity, changePercent, lastUpdateTime are not in MarketDataType
              // If these are needed, MarketDataType in src/types/index.ts should be updated.
            };
            newMap.set(data.symbol, marketUpdate);
            return newMap;
          });
        }
      }
    );

    newSocket.on("connect_error", (err) => {
      setError(`Socket.IO connection error: ${err.message}`);
      console.error("Socket.IO connection error:", err);
      setIsConnected(false); // Ensure disconnected state is set
    });

    newSocket.on("disconnect", (reason) => {
      setIsConnected(false);
      console.log(`Socket.IO disconnected: ${reason}`);
      if (reason === "io server disconnect") {
        // The server deliberately disconnected the socket
        newSocket.connect(); // Optionally attempt to reconnect
      }
      // else the socket will automatically try to reconnect if reconnectionAttempts > 0
    });

    setSocket(newSocket);
  }, [marketData, socket]); // Added socket to dependency array

  useEffect(() => {
    const initializeDashboard = async () => {
      setLoading(true);
      await Promise.all([fetchDatabaseStats(), fetchServerStats()]);
      setLoading(false);
    };

    initializeDashboard();
    connectSocketIO();

    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, [connectSocketIO, fetchDatabaseStats, fetchServerStats]);

  // Filter market data by sector
  const filteredMarketData =
    selectedSector === "all"
      ? Array.from(marketData.entries())
      : Array.from(marketData.entries()).filter(([symbol, data]) => {
          // This would need sector mapping from database
          return true; // For now, show all
        });

  const topGainers = Array.from(marketData.values())
    .filter((data) => data.change > 0)
    .sort((a, b) => b.change - a.change)
    .slice(0, 5);

  const topLosers = Array.from(marketData.values())
    .filter((data) => data.change < 0)
    .sort((a, b) => a.change - b.change)
    .slice(0, 5);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Market Dashboard
              </h1>
              <p className="text-gray-600 mt-1">
                Real-time market data powered by database
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={reloadInstruments}
                disabled={loading}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                <RefreshCwIcon
                  className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
                />
                Reload
              </button>
              <div className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`}
                />
                <span className="text-sm text-gray-600">
                  {isConnected ? "Connected" : "Disconnected"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-6">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Database Stats */}
          {databaseStats && (
            <>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <DatabaseIcon className="w-8 h-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Total Companies
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {databaseStats.totalCompanies.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <PieChartIcon className="w-8 h-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Sectors</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {databaseStats.totalSectors}
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Server Stats */}
          {serverStats && (
            <>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <BarChart3Icon className="w-8 h-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Active Instruments
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {serverStats.activeInstruments}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <TrendingUpIcon className="w-8 h-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Subscribed
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {serverStats.instruments}
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Key Indices */}
        <div className="mb-8">
          <KeyIndices />
        </div>

        {/* Top Gainers and Losers */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Top Gainers */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center mb-4">
              <TrendingUpIcon className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">
                Top Gainers
              </h3>
            </div>
            <div className="space-y-3">
              {topGainers.length > 0 ? (
                topGainers.map((stock, index) => (
                  <div
                    key={stock.symbol}
                    className="flex items-center justify-between p-3 bg-green-50 rounded-lg"
                  >
                    <div>
                      <div className="font-medium text-gray-900">
                        {stock.symbol}
                      </div>
                      <div className="text-sm text-gray-600">
                        ₹{stock.lastPrice.toFixed(2)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-600">
                        +{stock.change.toFixed(2)} (+
                        {(
                          (stock.change / (stock.lastPrice - stock.change)) *
                          100
                        ).toFixed(2)}
                        %)
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">
                  No gainers data available
                </p>
              )}
            </div>
          </div>

          {/* Top Losers */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center mb-4">
              <TrendingDownIcon className="w-5 h-5 text-red-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">
                Top Losers
              </h3>
            </div>
            <div className="space-y-3">
              {topLosers.length > 0 ? (
                topLosers.map((stock, index) => (
                  <div
                    key={stock.symbol}
                    className="flex items-center justify-between p-3 bg-red-50 rounded-lg"
                  >
                    <div>
                      <div className="font-medium text-gray-900">
                        {stock.symbol}
                      </div>
                      <div className="text-sm text-gray-600">
                        ₹{stock.lastPrice.toFixed(2)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-red-600">
                        {stock.change.toFixed(2)} (
                        {(
                          (stock.change / (stock.lastPrice - stock.change)) *
                          100
                        ).toFixed(2)}
                        %)
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">
                  No losers data available
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Sector Distribution */}
        {databaseStats && databaseStats.sectorDistribution && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Sector Distribution
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {databaseStats.sectorDistribution
                .slice(0, 9)
                .map((sector, index) => (
                  <div
                    key={sector.sector}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div>
                      <div className="font-medium text-gray-900">
                        {sector.sector}
                      </div>
                      <div className="text-sm text-gray-600">
                        {sector.count} companies
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-blue-600">
                        {sector.percentage}%
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Live Market Data */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Live Market Data
            </h3>
            <div className="text-sm text-gray-600">
              {marketData.size} instruments • Updated in real-time
            </div>
          </div>

          {marketData.size > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredMarketData.slice(0, 12).map(([symbol, data]) => (
                <MarketData key={symbol} symbol={symbol} data={data} />
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-2">
                No live market data available
              </div>
              <div className="text-sm text-gray-400">
                {isConnected
                  ? "Waiting for market data..."
                  : "Please check connection"}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
