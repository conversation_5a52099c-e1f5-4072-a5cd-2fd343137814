const fs = require('fs');
const path = require('path');

// Function to fix NextResponse.json calls with status codes
function fixNextResponseCalls(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Pattern to match NextResponse.json calls with status codes
  const pattern = /NextResponse\.json\s*\(\s*({[\s\S]*?})\s*,\s*{\s*status:\s*(\d+)\s*}\s*\)/g;
  
  content = content.replace(pattern, (match, jsonContent, statusCode) => {
    return `new Response(
      JSON.stringify(${jsonContent}),
      { 
        status: ${statusCode},
        headers: { 'Content-Type': 'application/json' }
      }
    )`;
  });
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`Fixed ${filePath}`);
}

// Files to fix
const filesToFix = [
  'src/app/api/websocket-instruments/route.ts'
];

filesToFix.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  if (fs.existsSync(fullPath)) {
    fixNextResponseCalls(fullPath);
  } else {
    console.log(`File not found: ${fullPath}`);
  }
});

console.log('All NextResponse.json calls with status codes have been fixed!');
