const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

// Read the Excel file
const filePath = path.join(__dirname, '..', 'Companydetails.xlsx');

try {
  console.log('Reading Excel file:', filePath);
  
  // Read the workbook
  const workbook = XLSX.readFile(filePath);
  
  // Get sheet names
  const sheetNames = workbook.SheetNames;
  console.log('Available sheets:', sheetNames);
  
  // Process each sheet
  sheetNames.forEach((sheetName, index) => {
    console.log(`\n=== Sheet ${index + 1}: ${sheetName} ===`);
    
    const worksheet = workbook.Sheets[sheetName];
    
    // Convert to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    console.log(`Total rows: ${jsonData.length}`);
    
    if (jsonData.length > 0) {
      // Show headers
      console.log('Headers:', jsonData[0]);
      
      // Show first few data rows
      console.log('\nFirst 5 data rows:');
      for (let i = 1; i <= Math.min(5, jsonData.length - 1); i++) {
        console.log(`Row ${i}:`, jsonData[i]);
      }
      
      // Convert to JSON with headers
      const jsonWithHeaders = XLSX.utils.sheet_to_json(worksheet);
      
      // Save to JSON file
      const outputPath = path.join(__dirname, '..', `${sheetName.replace(/[^a-zA-Z0-9]/g, '_')}_data.json`);
      fs.writeFileSync(outputPath, JSON.stringify(jsonWithHeaders, null, 2));
      console.log(`\nData saved to: ${outputPath}`);
      
      // Show sample of structured data
      if (jsonWithHeaders.length > 0) {
        console.log('\nSample structured data:');
        console.log(JSON.stringify(jsonWithHeaders.slice(0, 3), null, 2));
      }
    }
  });
  
} catch (error) {
  console.error('Error reading Excel file:', error.message);
  
  // Check if file exists
  if (!fs.existsSync(filePath)) {
    console.error('File does not exist at:', filePath);
    
    // List files in the directory
    const dir = path.dirname(filePath);
    console.log('\nFiles in directory:');
    try {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        if (file.toLowerCase().includes('excel') || file.toLowerCase().includes('.xlsx') || file.toLowerCase().includes('.xls')) {
          console.log('  -', file);
        }
      });
    } catch (dirError) {
      console.error('Could not read directory:', dirError.message);
    }
  }
}
