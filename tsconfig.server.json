{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/types": ["src/types"], "@/services/*": ["src/services/*"], "@/server/*": ["src/server/*"], "@/lib/*": ["src/lib/*"], "@/middleware/*": ["src/middleware/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["src/**/*", "src/types/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "src/app/**/*", "src/components/**/*", "src/pages/**/*"], "ts-node": {"require": ["tsconfig-paths/register"]}}