const fs = require("fs");
const path = require("path");

// Function to analyze the indexdata JSON file
function analyzeIndexData() {
  try {
    const filePath = path.join(__dirname, "..", "indexdata_Sheet1.json");
    
    console.log("📊 Analyzing IndexData JSON file...");
    console.log(`📁 File path: ${filePath}`);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    
    // Read the JSON file
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    console.log(`\n📋 Total Records: ${data.length}`);
    
    // Analyze by Exchange
    const exchangeStats = {};
    data.forEach(record => {
      const exchange = record.SEM_EXM_EXCH_ID;
      if (!exchangeStats[exchange]) {
        exchangeStats[exchange] = {
          count: 0,
          indices: []
        };
      }
      exchangeStats[exchange].count++;
      exchangeStats[exchange].indices.push({
        id: record.SEM_SMST_SECURITY_ID,
        symbol: record.SEM_TRADING_SYMBOL,
        name: record.SEM_CUSTOM_SYMBOL,
        lotUnits: record.SEM_LOT_UNITS
      });
    });
    
    console.log("\n🏢 Exchange-wise Distribution:");
    Object.entries(exchangeStats).forEach(([exchange, stats]) => {
      console.log(`  📈 ${exchange}: ${stats.count} indices`);
    });
    
    // Analyze BSE Indices
    console.log("\n🔵 BSE INDICES:");
    console.log("=" .repeat(60));
    
    const bseIndices = exchangeStats.BSE?.indices || [];
    
    // Group BSE indices by category
    const bseCategories = {
      'Broad Market': [],
      'Sectoral': [],
      'Thematic': [],
      'Size-based': [],
      'Other': []
    };
    
    bseIndices.forEach(index => {
      const name = index.name.toLowerCase();
      const symbol = index.symbol.toLowerCase();
      
      if (name.includes('sensex') || name.includes('100') || name.includes('200') || name.includes('500') || name.includes('1000')) {
        bseCategories['Broad Market'].push(index);
      } else if (name.includes('auto') || name.includes('bank') || name.includes('it') || name.includes('healthcare') || 
                 name.includes('metal') || name.includes('oil') || name.includes('power') || name.includes('realty') ||
                 name.includes('telecom') || name.includes('fmcg') || name.includes('energy') || name.includes('finance')) {
        bseCategories['Sectoral'].push(index);
      } else if (name.includes('esg') || name.includes('green') || name.includes('carbon') || name.includes('psu') ||
                 name.includes('cpse') || name.includes('infrastructure') || name.includes('manufacturing')) {
        bseCategories['Thematic'].push(index);
      } else if (name.includes('large') || name.includes('mid') || name.includes('small') || name.includes('cap')) {
        bseCategories['Size-based'].push(index);
      } else {
        bseCategories['Other'].push(index);
      }
    });
    
    Object.entries(bseCategories).forEach(([category, indices]) => {
      if (indices.length > 0) {
        console.log(`\n📊 ${category} (${indices.length} indices):`);
        indices.forEach(index => {
          console.log(`  • ${index.symbol} - ${index.name} (ID: ${index.id})`);
        });
      }
    });
    
    // Analyze NSE Indices
    console.log("\n\n🟡 NSE INDICES:");
    console.log("=" .repeat(60));
    
    const nseIndices = exchangeStats.NSE?.indices || [];
    
    // Group NSE indices by category
    const nseCategories = {
      'Nifty Core': [],
      'Sectoral Nifty': [],
      'Thematic Nifty': [],
      'Strategy Indices': [],
      'Currency': [],
      'Other': []
    };
    
    nseIndices.forEach(index => {
      const name = index.name.toLowerCase();
      const symbol = index.symbol.toLowerCase();
      
      if (name.includes('nifty 50') || name.includes('nifty next 50') || name.includes('nifty 100') || 
          name.includes('nifty 200') || name.includes('nifty 500') || name.includes('nifty midcap') || 
          name.includes('nifty smallcap')) {
        nseCategories['Nifty Core'].push(index);
      } else if (name.includes('bank') || name.includes('auto') || name.includes('it') || name.includes('pharma') ||
                 name.includes('metal') || name.includes('realty') || name.includes('energy') || name.includes('fmcg') ||
                 name.includes('media') || name.includes('psu') || name.includes('financial')) {
        nseCategories['Sectoral Nifty'].push(index);
      } else if (name.includes('dividend') || name.includes('growth') || name.includes('value') || name.includes('quality') ||
                 name.includes('momentum') || name.includes('alpha') || name.includes('consumption') || name.includes('commodities')) {
        nseCategories['Thematic Nifty'].push(index);
      } else if (name.includes('leverage') || name.includes('inverse') || name.includes('equal weight') || name.includes('beta')) {
        nseCategories['Strategy Indices'].push(index);
      } else if (name.includes('inr') || symbol.includes('inr')) {
        nseCategories['Currency'].push(index);
      } else {
        nseCategories['Other'].push(index);
      }
    });
    
    Object.entries(nseCategories).forEach(([category, indices]) => {
      if (indices.length > 0) {
        console.log(`\n📊 ${category} (${indices.length} indices):`);
        indices.forEach(index => {
          console.log(`  • ${index.symbol} - ${index.name} (ID: ${index.id})`);
        });
      }
    });
    
    // Lot Units Analysis
    console.log("\n\n📦 LOT UNITS ANALYSIS:");
    console.log("=" .repeat(60));
    
    const lotUnitsStats = {};
    data.forEach(record => {
      const lotUnits = record.SEM_LOT_UNITS;
      if (!lotUnitsStats[lotUnits]) {
        lotUnitsStats[lotUnits] = 0;
      }
      lotUnitsStats[lotUnits]++;
    });
    
    Object.entries(lotUnitsStats).forEach(([lotUnits, count]) => {
      console.log(`  📦 Lot Units ${lotUnits}: ${count} indices`);
    });
    
    // Security ID Range Analysis
    console.log("\n\n🔢 SECURITY ID ANALYSIS:");
    console.log("=" .repeat(60));
    
    const securityIds = data.map(record => record.SEM_SMST_SECURITY_ID).sort((a, b) => a - b);
    console.log(`  📊 Security ID Range: ${securityIds[0]} to ${securityIds[securityIds.length - 1]}`);
    console.log(`  📊 Total Unique IDs: ${new Set(securityIds).size}`);
    
    // Popular Index Names
    console.log("\n\n⭐ NOTABLE INDICES:");
    console.log("=" .repeat(60));
    
    const notableIndices = data.filter(record => {
      const name = record.SEM_CUSTOM_SYMBOL.toLowerCase();
      return name.includes('sensex') || name.includes('nifty 50') || name.includes('bank') || 
             name.includes('it') || name.includes('auto') || name.includes('pharma');
    });
    
    notableIndices.forEach(index => {
      console.log(`  ⭐ ${index.SEM_EXM_EXCH_ID} - ${index.SEM_TRADING_SYMBOL} - ${index.SEM_CUSTOM_SYMBOL}`);
    });
    
    console.log("\n🎉 IndexData analysis completed!");
    
  } catch (error) {
    console.error("❌ Error analyzing IndexData:", error.message);
    throw error;
  }
}

// Run the analysis
if (require.main === module) {
  analyzeIndexData();
}

module.exports = { analyzeIndexData };
