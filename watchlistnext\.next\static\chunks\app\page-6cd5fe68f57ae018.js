(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1058:(e,t,s)=>{Promise.resolve().then(s.bind(s,9322))},9322:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(5155),r=s(2115),l=s(4298),n=s(6874),i=s.n(n);let c=e=>{let{data:t,sortField:s,sortDirection:r,onSort:l}=e,n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return e.toLocaleString("en-IN",{minimumFractionDigits:t,maximumFractionDigits:t})},c=e=>e>=1e7?"".concat((e/1e7).toFixed(1),"Cr"):e>=1e5?"".concat((e/1e5).toFixed(1),"L"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),o=e=>{let{field:t,children:n,className:i=""}=e;return(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors ".concat(i),onClick:()=>l(t),children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:n}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("svg",{className:"w-3 h-3 ".concat(s===t&&"asc"===r?"text-blue-600":"text-gray-400"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})}),(0,a.jsx)("svg",{className:"w-3 h-3 -mt-1 ".concat(s===t&&"desc"===r?"text-blue-600":"text-gray-400"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})})};return 0===t.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-lg font-medium",children:"No market data available"}),(0,a.jsx)("div",{className:"text-sm",children:"Waiting for real-time updates from the market feed..."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)(o,{field:"ticker",children:"Symbol"}),(0,a.jsx)(o,{field:"exchange",children:"Exchange"}),(0,a.jsx)(o,{field:"ltp",className:"text-right",children:"LTP"}),(0,a.jsx)(o,{field:"change",className:"text-right",children:"Change"}),(0,a.jsx)(o,{field:"changePercent",className:"text-right",children:"Change %"}),(0,a.jsx)(o,{field:"volume",className:"text-right",children:"Volume"}),(0,a.jsx)(o,{field:"high",className:"text-right",children:"High"}),(0,a.jsx)(o,{field:"low",className:"text-right",children:"Low"}),(0,a.jsx)(o,{field:"open",className:"text-right",children:"Open"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(i(),{href:"/stock/".concat(e.ticker),className:"text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline transition-colors cursor-pointer",children:e.ticker}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:e.securityId})]})}),(0,a.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.exchange})}),(0,a.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm font-medium text-gray-900",children:["₹",n(e.ltp)]}),(0,a.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm",children:(0,a.jsxs)("span",{className:"font-medium ".concat(e.change>=0?"text-green-600":"text-red-600"),children:[e.change>=0?"+":"",n(e.change)]})}),(0,a.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.changePercent>=0?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:[e.changePercent>=0?"+":"",n(e.changePercent),"%"]})}),(0,a.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900",children:c(e.volume)}),(0,a.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900",children:["₹",n(e.high)]}),(0,a.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900",children:["₹",n(e.low)]}),(0,a.jsxs)("td",{className:"px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900",children:["₹",n(e.open)]})]},e.securityId))})]})})},o=e=>{let{currentPage:t,totalPages:s,onPageChange:l,itemsPerPage:n,onItemsPerPageChange:i,totalItems:c}=e,o=(t-1)*n+1,d=Math.min(t*n,c);return s<=1?(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("label",{htmlFor:"itemsPerPage",className:"mr-2 text-sm text-gray-700",children:"Show:"}),(0,a.jsxs)("select",{id:"itemsPerPage",value:n,onChange:e=>i(Number(e.target.value)),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:10,children:"10"}),(0,a.jsx)("option",{value:25,children:"25"}),(0,a.jsx)("option",{value:50,children:"50"}),(0,a.jsx)("option",{value:100,children:"100"})]}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"per page"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",o," to ",d," of ",c," results"]})]}):(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("label",{htmlFor:"itemsPerPage",className:"mr-2 text-sm text-gray-700",children:"Show:"}),(0,a.jsxs)("select",{id:"itemsPerPage",value:n,onChange:e=>i(Number(e.target.value)),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:10,children:"10"}),(0,a.jsx)("option",{value:25,children:"25"}),(0,a.jsx)("option",{value:50,children:"50"}),(0,a.jsx)("option",{value:100,children:"100"})]}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"per page"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Showing ",o," to ",d," of ",c," results"]}),(0,a.jsxs)("nav",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{type:"button",onClick:()=>l(t-1),disabled:1===t,className:"px-3 py-2 text-sm font-medium rounded-md ".concat(1===t?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),children:"‹"}),(()=>{let e=[],a=[];for(let a=Math.max(2,t-2);a<=Math.min(s-1,t+2);a++)e.push(a);return t-2>2?a.push(1,"..."):a.push(1),a.push(...e),t+2<s-1?a.push("...",s):s>1&&a.push(s),a})().map((e,s)=>(0,a.jsx)(r.Fragment,{children:"..."===e?(0,a.jsx)("span",{className:"px-3 py-2 text-sm text-gray-500",children:"..."}):(0,a.jsx)("button",{type:"button",onClick:()=>l(e),className:"px-3 py-2 text-sm font-medium rounded-md ".concat(t===e?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-100"),children:e})},s)),(0,a.jsx)("button",{type:"button",onClick:()=>l(t+1),disabled:t===s,className:"px-3 py-2 text-sm font-medium rounded-md ".concat(t===s?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),children:"›"})]})]})]})};function d(e){let{stats:t}=e;return(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Instruments"}),(0,a.jsx)("div",{className:"text-2xl font-semibold text-blue-600",children:t.instrumentCount})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Updates/sec"}),(0,a.jsx)("div",{className:"text-2xl font-semibold text-green-600",children:t.updateRate})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Latency"}),(0,a.jsxs)("div",{className:"text-2xl font-semibold text-purple-600",children:[t.latency,"ms"]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Last Update"}),(0,a.jsx)("div",{className:"text-2xl font-semibold text-gray-600",children:t.lastUpdate})]})]})}let x=e=>{let{isConnected:t}=e;return(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full mr-2 ".concat(t?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:t?"Connected":"Disconnected"})]})};function m(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!1),[i,m]=(0,r.useState)(new Map),[h,u]=(0,r.useState)(""),[g,p]=(0,r.useState)(""),[f,j]=(0,r.useState)(1),[y,N]=(0,r.useState)(10),[b,v]=(0,r.useState)("ticker"),[w,C]=(0,r.useState)("asc"),[k,S]=(0,r.useState)({instrumentCount:0,updateRate:0,latency:0,lastUpdate:"Never"}),E=(0,r.useRef)(0),P=(0,r.useRef)(Date.now()),M=(0,r.useRef)(0);(0,r.useRef)(null);let D=e=>{let t=Date.now();E.current++,M.current++,t-P.current>=1e3&&(S(t=>({...t,updateRate:M.current,latency:e,lastUpdate:new Date().toLocaleTimeString()})),M.current=0,P.current=t)},F=(0,r.useCallback)(e=>{D(Date.now()-e.timestamp),m(t=>{let s=t.get(e.securityId);if(s&&s.timestamp>=e.timestamp)return t;let a=new Map(t);return a.set(e.securityId,e),a})},[]),I=(0,r.useCallback)(e=>{0!==e.length&&(D(Date.now()-e[0].timestamp),m(t=>{let s=new Map(t),a=!1;return e.forEach(e=>{let t=s.get(e.securityId);(!t||t.timestamp<e.timestamp)&&(s.set(e.securityId,e),a=!0)}),a?s:t}))},[]),L=(0,r.useCallback)(e=>{b===e?C("asc"===w?"desc":"asc"):(v(e),C("asc")),j(1)},[b,w]),R=(0,r.useMemo)(()=>{let e=Array.from(i.values());if(h){let t=h.toLowerCase();e=e.filter(e=>e.ticker.toLowerCase().includes(t)||e.securityId.includes(t))}return g&&(e=e.filter(e=>e.exchange===g)),e.sort((e,t)=>{let s=e[b],a=t[b];if("string"==typeof s&&"string"==typeof a){let e=s.localeCompare(a);return"asc"===w?e:-e}if("number"==typeof s&&"number"==typeof a){let e=s-a;return"asc"===w?e:-e}return 0}),e},[i,h,g,b,w]),_=(0,r.useMemo)(()=>{let e=(f-1)*y,t=e+y;return R.slice(e,t)},[R,f,y]),U=Math.ceil(R.length/y);return(0,r.useEffect)(()=>{j(1)},[h,g,y]),(0,r.useEffect)(()=>{let e=(0,l.io)("http://localhost:8080",{transports:["websocket","polling"],upgrade:!0,rememberUpgrade:!1,timeout:2e4,forceNew:!0});return e.on("initialData",e=>{S(t=>({...t,instrumentCount:e.instruments.length})),m(t=>{let s=new Map(t);return e.liveData.forEach(e=>{s.set(e.securityId,e)}),s})}),e.on("marketData",e=>{F(e)}),e.on("marketDataBatch",e=>{I(e)}),e.on("connect",()=>{console.log("✅ Connected to WebSocket server"),n(!0)}),e.on("disconnect",()=>{console.log("❌ Disconnected from WebSocket server"),n(!1)}),e.on("connect_error",e=>{console.error("❌ Connection error:",e),n(!1)}),t(e),fetch("http://localhost:8080/api/data").then(e=>e.json()).then(e=>{S(t=>{var s;return{...t,instrumentCount:(null==(s=e.instruments)?void 0:s.length)||0}})}).catch(e=>console.error("Error fetching stats:",e)),()=>{e.disconnect()}},[]),(0,a.jsx)("main",{className:"min-h-screen bg-gray-50 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"⚡ Ultra-Fast Market Dashboard"}),(0,a.jsx)(x,{isConnected:s})]}),(0,a.jsx)(d,{stats:k}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Instruments"}),(0,a.jsx)("input",{type:"text",id:"search",placeholder:"Search by ticker or security ID...",value:h,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"md:w-48",children:[(0,a.jsx)("label",{htmlFor:"exchange",className:"block text-sm font-medium text-gray-700 mb-2",children:"Exchange"}),(0,a.jsxs)("select",{id:"exchange",value:g,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Exchanges"}),(0,a.jsx)("option",{value:"NSE_EQ",children:"NSE Equity"}),(0,a.jsx)("option",{value:"NSE_FNO",children:"NSE F&O"}),(0,a.jsx)("option",{value:"BSE_EQ",children:"BSE Equity"}),(0,a.jsx)("option",{value:"MCX_COMM",children:"MCX Commodity"})]})]}),(0,a.jsx)("div",{className:"md:w-32 flex items-end",children:(0,a.jsx)("button",{type:"button",onClick:()=>{u(""),p("")},className:"w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500",children:"Clear"})})]}),(0,a.jsxs)("div",{className:"mt-2 text-sm text-gray-600",children:["Showing ",_.length," of ",R.length," instruments",R.length!==i.size&&(0,a.jsxs)("span",{className:"text-gray-500",children:[" ","(filtered from ",i.size," total)"]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)(c,{data:_,sortField:b,sortDirection:w,onSort:L}),(0,a.jsx)(o,{currentPage:f,totalPages:U,onPageChange:j,itemsPerPage:y,onItemsPerPageChange:N,totalItems:R.length})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[161,441,684,358],()=>t(1058)),_N_E=e.O()}]);