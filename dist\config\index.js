"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.appConfig = void 0;
const dotenv_1 = require("dotenv");
const zod_1 = require("zod");
// Load environment variables
(0, dotenv_1.config)();
// Define configuration schema
const configSchema = zod_1.z.object({
    // Database
    database: zod_1.z.object({
        url: zod_1.z.string().url("Invalid database URL"),
        ssl: zod_1.z.boolean().default(false),
        maxConnections: zod_1.z.number().default(20),
        idleTimeout: zod_1.z.number().default(30000),
        connectionTimeout: zod_1.z.number().default(5000),
    }),
    // API
    api: zod_1.z.object({
        port: zod_1.z.number().default(3000),
        accessToken: zod_1.z.string().min(1, "Access token is required"),
        clientId: zod_1.z.string().min(1, "Client ID is required"),
    }),
    // WebSocket
    websocket: zod_1.z.object({
        subscriptionType: zod_1.z.enum(["ticker", "quote", "full"]).default("quote"),
        maxInstruments: zod_1.z.number().default(25000),
        preferredExchange: zod_1.z.enum(["NSE", "BSE"]).default("NSE"),
        heartbeatInterval: zod_1.z.number().default(30000),
        reconnectAttempts: zod_1.z.number().default(5),
        reconnectDelay: zod_1.z.number().default(5000),
    }),
    // Security
    security: zod_1.z.object({
        allowedOrigins: zod_1.z
            .array(zod_1.z.string().url())
            .default(["http://localhost:3000"]),
        rateLimitWindowMs: zod_1.z.number().default(900000), // 15 minutes
        rateLimitMaxRequests: zod_1.z.number().default(100),
    }),
    // File Upload
    upload: zod_1.z.object({
        maxFileSize: zod_1.z.number().default(5242880), // 5MB
        uploadDir: zod_1.z.string().default("uploads"),
        allowedExtensions: zod_1.z.array(zod_1.z.string()).default([".xlsx", ".xls"]),
    }),
    // Logging
    logging: zod_1.z.object({
        level: zod_1.z.enum(["error", "warn", "info", "debug"]).default("info"),
    }),
});
// Parse and validate configuration
const parseConfig = () => {
    try {
        return configSchema.parse({
            database: {
                url: process.env.POSTGRES_DATABASE_URL,
                ssl: process.env.POSTGRES_SSL === "true",
                maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || "20"),
                idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || "30000"),
                connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || "5000"),
            },
            api: {
                port: parseInt(process.env.PORT || "3000"),
                accessToken: process.env.ACCESS_TOKEN,
                clientId: process.env.CLIENT_ID,
            },
            websocket: {
                subscriptionType: process.env.SUBSCRIPTION_TYPE || "quote",
                maxInstruments: parseInt(process.env.MAX_INSTRUMENTS || "25000"),
                preferredExchange: process.env.PREFERRED_EXCHANGE || "NSE",
                heartbeatInterval: parseInt(process.env.WS_HEARTBEAT_INTERVAL || "30000"),
                reconnectAttempts: parseInt(process.env.WS_RECONNECT_ATTEMPTS || "5"),
                reconnectDelay: parseInt(process.env.WS_RECONNECT_DELAY || "5000"),
            },
            security: {
                allowedOrigins: process.env.ALLOWED_ORIGINS?.split(",") || [
                    "http://localhost:3000",
                    "http://localhost:3001",
                ],
                rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"),
                rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100"),
            },
            upload: {
                maxFileSize: parseInt(process.env.MAX_FILE_SIZE || "5242880"),
                uploadDir: process.env.UPLOAD_DIR || "uploads",
                allowedExtensions: process.env.ALLOWED_EXTENSIONS?.split(",") || [
                    ".xlsx",
                    ".xls",
                ],
            },
            logging: {
                level: process.env.LOG_LEVEL || "info",
            },
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            console.error("Configuration validation failed:", error.errors);
        }
        else {
            console.error("Failed to parse configuration:", error);
        }
        process.exit(1);
    }
};
exports.appConfig = parseConfig();
