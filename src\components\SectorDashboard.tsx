'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrendingUp, TrendingDown, BarChart3, Building2 } from 'lucide-react';

interface StockData {
  ticker: string;
  securityId: string;
  exchange: string;
  exchangeCode: number;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
  companyName?: string;
  sector?: string;
  industry?: string;
}

interface SectorData {
  sector: string;
  stocks: StockData[];
  totalStocks: number;
  gainers: number;
  losers: number;
  avgChange: number;
  totalVolume: number;
}

interface CompanyInfo {
  company_name: string;
  nse_symbol?: string;
  bse_symbol?: string;
  nse_security_id?: string;
  bse_security_id?: string;
  sector_name?: string;
  industry_name?: string;
}

export default function SectorDashboard() {
  const [selectedExchange, setSelectedExchange] = useState<'NSE' | 'BSE'>('NSE');
  const [sectorData, setSectorData] = useState<SectorData[]>([]);
  const [loading, setLoading] = useState(true);
  const [companies, setCompanies] = useState<{ [key: string]: CompanyInfo }>({});

  // Fetch company information
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await fetch('/api/companies');
        const data = await response.json();
        
        if (data.success) {
          const companyMap: { [key: string]: CompanyInfo } = {};
          data.companies.forEach((company: CompanyInfo) => {
            // Map by NSE symbol
            if (company.nse_symbol) {
              companyMap[company.nse_symbol] = company;
            }
            // Map by BSE symbol if different
            if (company.bse_symbol && company.bse_symbol !== company.nse_symbol) {
              companyMap[company.bse_symbol] = company;
            }
          });
          setCompanies(companyMap);
        }
      } catch (error) {
        console.error('Error fetching companies:', error);
      }
    };

    fetchCompanies();
  }, []);

  // Fetch market data and organize by sectors
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/data?exchange=${selectedExchange}_EQ&limit=1000`);
        const data = await response.json();

        if (data.latestData) {
          const sectorMap: { [key: string]: StockData[] } = {};

          data.latestData.forEach((stock: StockData) => {
            // Get company info for this stock
            const companyInfo = companies[stock.ticker];
            const sector = companyInfo?.sector_name || 'Others';

            if (!sectorMap[sector]) {
              sectorMap[sector] = [];
            }

            // Add company info to stock data
            const enrichedStock = {
              ...stock,
              companyName: companyInfo?.company_name || stock.ticker,
              sector: companyInfo?.sector_name,
              industry: companyInfo?.industry_name
            };

            sectorMap[sector].push(enrichedStock);
          });

          // Convert to sector data with statistics
          const sectors: SectorData[] = Object.entries(sectorMap).map(([sector, stocks]) => {
            const gainers = stocks.filter(s => s.change > 0).length;
            const losers = stocks.filter(s => s.change < 0).length;
            const avgChange = stocks.reduce((sum, s) => sum + s.changePercent, 0) / stocks.length;
            const totalVolume = stocks.reduce((sum, s) => sum + s.volume, 0);

            return {
              sector,
              stocks: stocks.sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent)), // Sort by highest change
              totalStocks: stocks.length,
              gainers,
              losers,
              avgChange,
              totalVolume
            };
          });

          // Sort sectors by average change
          sectors.sort((a, b) => Math.abs(b.avgChange) - Math.abs(a.avgChange));
          setSectorData(sectors);
        }
      } catch (error) {
        console.error('Error fetching market data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (Object.keys(companies).length > 0) {
      fetchMarketData();
    }
  }, [selectedExchange, companies]);

  const formatPrice = (price: number) => {
    return `₹${price.toFixed(2)}`;
  };

  const formatChange = (change: number, changePercent: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 10000000) return `${(volume / 10000000).toFixed(1)}Cr`;
    if (volume >= 100000) return `${(volume / 100000).toFixed(1)}L`;
    if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
    return volume.toString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading sector data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Exchange Toggle */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Building2 className="h-8 w-8" />
          Sector Dashboard
        </h1>
        
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Exchange:</span>
          <div className="flex rounded-lg border">
            <Button
              variant={selectedExchange === 'NSE' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedExchange('NSE')}
              className="rounded-r-none"
            >
              NSE
            </Button>
            <Button
              variant={selectedExchange === 'BSE' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedExchange('BSE')}
              className="rounded-l-none"
            >
              BSE
            </Button>
          </div>
        </div>
      </div>

      {/* Sector Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Sectors</p>
                <p className="text-2xl font-bold">{sectorData.length}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Stocks</p>
                <p className="text-2xl font-bold">
                  {sectorData.reduce((sum, sector) => sum + sector.totalStocks, 0)}
                </p>
              </div>
              <Building2 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Gainers</p>
                <p className="text-2xl font-bold text-green-600">
                  {sectorData.reduce((sum, sector) => sum + sector.gainers, 0)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Losers</p>
                <p className="text-2xl font-bold text-red-600">
                  {sectorData.reduce((sum, sector) => sum + sector.losers, 0)}
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sector Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {sectorData.map((sector) => (
          <Card key={sector.sector} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{sector.sector}</CardTitle>
                <Badge 
                  variant={sector.avgChange >= 0 ? "default" : "destructive"}
                  className={sector.avgChange >= 0 ? "bg-green-100 text-green-800" : ""}
                >
                  {sector.avgChange >= 0 ? '+' : ''}{sector.avgChange.toFixed(2)}%
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>{sector.totalStocks} stocks</span>
                <span className="text-green-600">{sector.gainers} ↑</span>
                <span className="text-red-600">{sector.losers} ↓</span>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              {/* Top 3 stocks in sector */}
              {sector.stocks.slice(0, 3).map((stock) => (
                <div key={stock.securityId} className="flex items-center justify-between p-2 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{stock.ticker}</div>
                    <div className="text-xs text-muted-foreground truncate">
                      {stock.companyName}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-medium text-sm">{formatPrice(stock.ltp)}</div>
                    <div className={`text-xs ${stock.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatChange(stock.change, stock.changePercent)}
                    </div>
                  </div>
                </div>
              ))}
              
              {sector.stocks.length > 3 && (
                <div className="text-center pt-2">
                  <Button variant="ghost" size="sm" className="text-xs">
                    View all {sector.stocks.length} stocks
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
