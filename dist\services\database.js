"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const pg_1 = require("pg");
const config_1 = require("../config");
class DatabaseService {
    constructor() {
        this.isInitialized = false;
        // Skip database connection during build
        if (process.env.NODE_ENV === "development" &&
            process.env.NEXT_PHASE === "phase-production-build") {
            this.pool = {}; // Mock pool during build
            return;
        }
        this.pool = new pg_1.Pool({
            connectionString: config_1.appConfig.database.url,
            ssl: process.env.NODE_ENV === "production"
                ? { rejectUnauthorized: false }
                : false,
        });
    }
    static getInstance() {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }
    async initialize() {
        // Skip initialization during build
        if (process.env.NODE_ENV === "development" &&
            process.env.NEXT_PHASE === "phase-production-build") {
            console.log("⏭️ Skipping database initialization during build");
            return;
        }
        if (this.isInitialized)
            return;
        try {
            const client = await this.pool.connect();
            client.release();
            // Create tables if they don't exist
            await this.query(`
        CREATE TABLE IF NOT EXISTS watchlists (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          type VARCHAR(50) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS sector_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          sector_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS industry_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          industry_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS sub_sector_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          sub_sector_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS micro_category_watchlist (
          id SERIAL PRIMARY KEY,
          watchlist_id INTEGER REFERENCES watchlists(id),
          micro_category_name VARCHAR(255) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
            this.isInitialized = true;
        }
        catch (error) {
            console.error("Error initializing database:", error);
            // Don't throw error during build time
            if (process.env.NODE_ENV !== "production") {
                console.warn("⚠️ Database initialization failed, continuing without database features");
            }
            else {
                throw error;
            }
        }
    }
    async query(text, params) {
        // Return mock result during build
        if (process.env.NODE_ENV === "development" &&
            process.env.NEXT_PHASE === "phase-production-build") {
            return {
                rows: [],
                rowCount: 0,
                command: "",
                oid: 0,
                fields: [],
            };
        }
        try {
            return await this.pool.query(text, params);
        }
        catch (error) {
            console.error("Error executing query:", error);
            throw error;
        }
    }
    // Company lookup methods
    async getCompanyBySymbol(symbol) {
        try {
            const result = await this.query("SELECT * FROM companies WHERE nse_symbol = $1 LIMIT 1", [symbol]);
            return result.rows[0] || null;
        }
        catch (error) {
            console.error("Error fetching company by symbol:", error);
            throw error;
        }
    }
    async getCompanyByISIN(isin) {
        try {
            const result = await this.query("SELECT * FROM companies WHERE isin_no = $1 LIMIT 1", [isin]);
            return result.rows[0] || null;
        }
        catch (error) {
            console.error("Error fetching company by ISIN:", error);
            throw error;
        }
    }
    async getCompanyBySecurityId(securityId) {
        try {
            const result = await this.query("SELECT * FROM companies WHERE bse_security_id = $1 OR nse_security_id = $1 LIMIT 1", [securityId]);
            return result.rows[0] || null;
        }
        catch (error) {
            console.error("Error fetching company by security ID:", error);
            throw error;
        }
    }
    async searchCompanies(filters) {
        try {
            const limit = filters.limit || 50;
            const offset = filters.offset || 0;
            let whereConditions = [];
            let queryParams = [];
            let paramIndex = 1;
            if (filters.sector) {
                whereConditions.push(`sector_name ILIKE $${paramIndex}`);
                queryParams.push(`%${filters.sector}%`);
                paramIndex++;
            }
            if (filters.industry) {
                whereConditions.push(`industry_name ILIKE $${paramIndex}`);
                queryParams.push(`%${filters.industry}%`);
                paramIndex++;
            }
            if (filters.search) {
                whereConditions.push(`(
          company_name ILIKE $${paramIndex} OR 
          nse_symbol ILIKE $${paramIndex} OR 
          bse_security_id ILIKE $${paramIndex} OR
          nse_security_id ILIKE $${paramIndex}
        )`);
                queryParams.push(`%${filters.search}%`);
                paramIndex++;
            }
            const whereClause = whereConditions.length > 0
                ? `WHERE ${whereConditions.join(" AND ")}`
                : "";
            const countQuery = `SELECT COUNT(*) FROM companies ${whereClause}`;
            const countResult = await this.query(countQuery, queryParams);
            const total = parseInt(countResult.rows[0]?.count || "0");
            const dataQuery = `
        SELECT * FROM companies 
        ${whereClause}
        ORDER BY company_name 
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
            queryParams.push(limit, offset);
            const dataResult = await this.query(dataQuery, queryParams);
            return {
                companies: dataResult.rows,
                total,
                page: Math.floor(offset / limit) + 1,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            console.error("Error searching companies:", error);
            throw error;
        }
    }
    async getSectors() {
        try {
            const result = await this.query("SELECT DISTINCT sector_name FROM companies WHERE sector_name IS NOT NULL ORDER BY sector_name");
            return result.rows.map((row) => row.sector_name);
        }
        catch (error) {
            console.error("Error fetching sectors:", error);
            throw error;
        }
    }
    async getIndustries(sector) {
        try {
            let query = "SELECT DISTINCT industry_name FROM companies WHERE industry_name IS NOT NULL";
            let params = [];
            if (sector) {
                query += " AND sector_name = $1";
                params.push(sector);
            }
            query += " ORDER BY industry_name";
            const result = await this.query(query, params);
            return result.rows.map((row) => row.industry_name);
        }
        catch (error) {
            console.error("Error fetching industries:", error);
            throw error;
        }
    }
    async getCompaniesBySector(sector) {
        try {
            const result = await this.query("SELECT * FROM companies WHERE sector_name = $1 ORDER BY company_name", [sector]);
            return result.rows;
        }
        catch (error) {
            console.error("Error fetching companies by sector:", error);
            throw error;
        }
    }
    async getSectorDistribution() {
        try {
            const result = await this.query("SELECT sector_name, COUNT(*) as count FROM companies WHERE sector_name IS NOT NULL GROUP BY sector_name ORDER BY count DESC");
            return result.rows.map((row) => ({
                sector: row.sector_name,
                count: parseInt(row.count),
            }));
        }
        catch (error) {
            console.error("Error fetching sector distribution:", error);
            throw error;
        }
    }
    // Watchlist methods
    async createWatchlist(name, type) {
        const result = await this.query("INSERT INTO watchlists (name, type) VALUES ($1, $2) RETURNING id", [name, type]);
        return result.rows[0]?.id || 0;
    }
    async getWatchlists() {
        const result = await this.query("SELECT id, name, type FROM watchlists ORDER BY created_at DESC");
        return result.rows;
    }
    async deleteWatchlist(id) {
        await this.query("DELETE FROM watchlists WHERE id = $1", [id]);
    }
    // Sector watchlist methods
    async addToSectorWatchlist(watchlistId, sectorName) {
        await this.query("INSERT INTO sector_watchlist (watchlist_id, sector_name) VALUES ($1, $2)", [watchlistId, sectorName]);
    }
    async getSectorWatchlist(watchlistId) {
        const result = await this.query("SELECT sector_name FROM sector_watchlist WHERE watchlist_id = $1", [watchlistId]);
        return result.rows.map((row) => row.sector_name);
    }
    // Industry watchlist methods
    async addToIndustryWatchlist(watchlistId, industryName) {
        await this.query("INSERT INTO industry_watchlist (watchlist_id, industry_name) VALUES ($1, $2)", [watchlistId, industryName]);
    }
    async getIndustryWatchlist(watchlistId) {
        const result = await this.query("SELECT industry_name FROM industry_watchlist WHERE watchlist_id = $1", [watchlistId]);
        return result.rows.map((row) => row.industry_name);
    }
    // Sub-sector watchlist methods
    async addToSubSectorWatchlist(watchlistId, subSectorName) {
        await this.query("INSERT INTO sub_sector_watchlist (watchlist_id, sub_sector_name) VALUES ($1, $2)", [watchlistId, subSectorName]);
    }
    async getSubSectorWatchlist(watchlistId) {
        const result = await this.query("SELECT sub_sector_name FROM sub_sector_watchlist WHERE watchlist_id = $1", [watchlistId]);
        return result.rows.map((row) => row.sub_sector_name);
    }
    // Micro category watchlist methods
    async addToMicroCategoryWatchlist(watchlistId, microCategoryName) {
        await this.query("INSERT INTO micro_category_watchlist (watchlist_id, micro_category_name) VALUES ($1, $2)", [watchlistId, microCategoryName]);
    }
    async getMicroCategoryWatchlist(watchlistId) {
        const result = await this.query("SELECT micro_category_name FROM micro_category_watchlist WHERE watchlist_id = $1", [watchlistId]);
        return result.rows.map((row) => row.micro_category_name);
    }
    // Health check
    async healthCheck() {
        try {
            const result = await this.query("SELECT 1");
            return result.rowCount === 1;
        }
        catch (error) {
            console.error("Error checking database health:", error);
            return false;
        }
    }
    // Get database statistics
    async getStats() {
        try {
            const [watchlistsCount, sectorWatchlistCount, industryWatchlistCount, subSectorWatchlistCount, microCategoryWatchlistCount,] = await Promise.all([
                this.query("SELECT COUNT(*) as count FROM watchlists"),
                this.query("SELECT COUNT(*) as count FROM sector_watchlist"),
                this.query("SELECT COUNT(*) as count FROM industry_watchlist"),
                this.query("SELECT COUNT(*) as count FROM sub_sector_watchlist"),
                this.query("SELECT COUNT(*) as count FROM micro_category_watchlist"),
            ]);
            return {
                watchlists: parseInt(watchlistsCount.rows[0]?.count || "0"),
                sectorWatchlist: parseInt(sectorWatchlistCount.rows[0]?.count || "0"),
                industryWatchlist: parseInt(industryWatchlistCount.rows[0]?.count || "0"),
                subSectorWatchlist: parseInt(subSectorWatchlistCount.rows[0]?.count || "0"),
                microCategoryWatchlist: parseInt(microCategoryWatchlistCount.rows[0]?.count || "0"),
            };
        }
        catch (error) {
            console.error("Error getting database stats:", error);
            throw error;
        }
    }
}
exports.DatabaseService = DatabaseService;
// Don't initialize during build
exports.default = DatabaseService;
