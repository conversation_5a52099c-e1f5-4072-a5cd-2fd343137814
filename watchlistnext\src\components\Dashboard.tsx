import React, { useEffect, useState, useCallback } from "react";
import { io, Socket } from "socket.io-client";
import { MarketData as MarketDataType, WebSocketMessage } from "../types"; // Assuming WebSocketMessage might still be useful for structure
import MarketData from "./MarketData";

const Dashboard: React.FC = () => {
  const [marketData, setMarketData] = useState<Map<string, MarketDataType>>(
    new Map()
  );
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const connectSocketIO = useCallback(() => {
    // Ensure any previous socket is disconnected before creating a new one
    if (socket) {
      socket.disconnect();
    }

    const newSocket = io("http://localhost:8080", {
      reconnectionAttempts: 5,
      reconnectionDelay: 5000,
    });

    newSocket.on("connect", () => {
      setIsConnected(true);
      setError(null);
      console.log("Socket.IO connected");
      // Subscribe to all symbols - assuming server expects a 'subscribe' event
      // The structure of subscribeMessage might need adjustment based on server implementation
      const subscribeMessage = {
        type: "subscribe", // This might need to be an array of instruments or specific format
        instruments: Array.from(marketData.keys()).map((key) => ({
          symbol: key,
        })), // Example, adjust as per server
      };
      newSocket.emit("subscribe", subscribeMessage);
    });

    newSocket.on("marketData", (message: MarketDataType) => {
      // Assuming server emits 'marketData' with MarketDataType
      if (message && message.symbol) {
        setMarketData((prev) => {
          const newMap = new Map(prev);
          newMap.set(message.symbol, message);
          return newMap;
        });
      } else {
        // Handle cases where message might not be structured as expected
        // console.warn("Received marketData without symbol or data:", message);
      }
    });

    // Listen for 'liveDataUpdate' which seems to be what the server emits in handleMessage
    newSocket.on(
      "liveDataUpdate",
      (data: { symbol: string; [key: string]: any }) => {
        if (data && data.symbol) {
          setMarketData((prev) => {
            const newMap = new Map(prev);
            // Map server data to the MarketDataType structure
            const marketUpdate: MarketDataType = {
              symbol: data.symbol,
              exchange: data.exchangeName || "", // Ensure exchange is always a string
              lastPrice: typeof data.ltp === "number" ? data.ltp : 0,
              change:
                typeof data.priceChange === "number" ? data.priceChange : 0,
              volume: typeof data.volume === "number" ? data.volume : 0,
              high: typeof data.dayHigh === "number" ? data.dayHigh : 0,
              low: typeof data.dayLow === "number" ? data.dayLow : 0,
              open: typeof data.dayOpen === "number" ? data.dayOpen : 0,
              close: typeof data.dayClose === "number" ? data.dayClose : 0, // Or data.previousClose if available
              timestamp: data.timestamp
                ? typeof data.timestamp === "string"
                  ? parseInt(data.timestamp, 10)
                  : typeof data.timestamp === "number"
                  ? data.timestamp
                  : Date.now()
                : Date.now(),
              // ltq, ltt, atp, totalSellQuantity, totalBuyQuantity, changePercent, lastUpdateTime are not in MarketDataType
              // If these are needed, MarketDataType in src/types/index.ts should be updated.
            };
            newMap.set(data.symbol, marketUpdate);
            return newMap;
          });
        }
      }
    );

    newSocket.on("connect_error", (err) => {
      setError(`Socket.IO connection error: ${err.message}`);
      console.error("Socket.IO connection error:", err);
      setIsConnected(false); // Ensure disconnected state is set
    });

    newSocket.on("disconnect", (reason) => {
      setIsConnected(false);
      console.log(`Socket.IO disconnected: ${reason}`);
      if (reason === "io server disconnect") {
        // The server deliberately disconnected the socket
        newSocket.connect(); // Optionally attempt to reconnect
      }
      // else the socket will automatically try to reconnect if reconnectionAttempts > 0
    });

    setSocket(newSocket);
  }, [marketData, socket]); // Added socket to dependency array

  useEffect(() => {
    connectSocketIO();
    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, [connectSocketIO]); // connectSocketIO is memoized

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Market Dashboard</h1>
          <div className="flex items-center space-x-2">
            <div
              className={`w-3 h-3 rounded-full ${
                isConnected ? "bg-green-500" : "bg-red-500"
              }`}
            />
            <span className="text-sm text-gray-600">
              {isConnected ? "Connected" : "Disconnected"}
            </span>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from(marketData.entries()).map(([symbol, data]) => (
            <MarketData key={symbol} symbol={symbol} data={data} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
