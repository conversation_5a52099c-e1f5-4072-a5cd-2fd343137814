import DatabaseService from "../../../services/database";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);

    // Get query parameters
    const sector = searchParams.get("sector");
    const industry = searchParams.get("industry");
    const nse_symbol = searchParams.get("symbol");
    const isin_no = searchParams.get("isin");
    const search = searchParams.get("search");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const action = searchParams.get("action");

    // Handle different actions
    switch (action) {
      case "stats":
        const stats = await DatabaseService.getStats();
        return new Response(
          JSON.stringify({
            success: true,
            data: stats,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "sectors":
        const sectors = await DatabaseService.getSectors();
        return new Response(
          JSON.stringify({
            success: true,
            data: sectors,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "industries":
        const industries = await DatabaseService.getIndustries(
          sector || undefined
        );
        return new Response(
          JSON.stringify({
            success: true,
            data: industries,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "sector-distribution":
        const distribution = await DatabaseService.getSectorDistribution();
        return new Response(
          JSON.stringify({
            success: true,
            data: distribution,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      case "by-sector":
        if (!sector) {
          return new Response(
            JSON.stringify({
              success: false,
              error: "Sector parameter is required",
            }),
            {
              status: 400,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
        const sectorCompanies =
          await DatabaseService.getCompaniesBySector(sector);
        return new Response(
          JSON.stringify({
            success: true,
            data: sectorCompanies,
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );

      default:
        // Search companies with filters
        const offset = (page - 1) * limit;
        const result = await DatabaseService.searchCompanies({
          sector: sector || undefined,
          industry: industry || undefined,
          nse_symbol: nse_symbol || undefined,
          isin_no: isin_no || undefined,
          search: search || undefined,
          limit,
          offset,
        });

        return new Response(
          JSON.stringify({
            success: true,
            data: result.companies,
            pagination: {
              page: result.page,
              limit,
              total: result.total,
              totalPages: result.totalPages,
            },
          }),
          {
            headers: { "Content-Type": "application/json" },
          }
        );
    }
  } catch (error) {
    console.error("Companies API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { symbols } = body;

    if (!symbols || !Array.isArray(symbols)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Symbols array is required",
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Get multiple companies by symbols
    const companies = await Promise.all(
      symbols.map((symbol) => DatabaseService.getCompanyBySymbol(symbol))
    );

    const validCompanies = companies.filter((company) => company !== null);

    return new Response(
      JSON.stringify({
        success: true,
        data: validCompanies,
        found: validCompanies.length,
        requested: symbols.length,
      }),
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Companies POST API error:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
