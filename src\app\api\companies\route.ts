import { NextRequest, NextResponse } from 'next/server';
import DatabaseService from '../../../services/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const sector = searchParams.get('sector');
    const industry = searchParams.get('industry');
    const nse_symbol = searchParams.get('symbol');
    const isin_no = searchParams.get('isin');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const action = searchParams.get('action');

    // Handle different actions
    switch (action) {
      case 'stats':
        const stats = await DatabaseService.getStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      case 'sectors':
        const sectors = await DatabaseService.getSectors();
        return NextResponse.json({
          success: true,
          data: sectors
        });

      case 'industries':
        const industries = await DatabaseService.getIndustries(sector || undefined);
        return NextResponse.json({
          success: true,
          data: industries
        });

      case 'sector-distribution':
        const distribution = await DatabaseService.getSectorDistribution();
        return NextResponse.json({
          success: true,
          data: distribution
        });

      case 'by-sector':
        if (!sector) {
          return NextResponse.json({
            success: false,
            error: 'Sector parameter is required'
          }, { status: 400 });
        }
        const sectorCompanies = await DatabaseService.getCompaniesBySector(sector);
        return NextResponse.json({
          success: true,
          data: sectorCompanies
        });

      default:
        // Search companies with filters
        const offset = (page - 1) * limit;
        const result = await DatabaseService.searchCompanies({
          sector: sector || undefined,
          industry: industry || undefined,
          nse_symbol: nse_symbol || undefined,
          isin_no: isin_no || undefined,
          search: search || undefined,
          limit,
          offset
        });

        return NextResponse.json({
          success: true,
          data: result.companies,
          pagination: {
            page: result.page,
            limit,
            total: result.total,
            totalPages: result.totalPages
          }
        });
    }

  } catch (error) {
    console.error('Companies API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbols } = body;

    if (!symbols || !Array.isArray(symbols)) {
      return NextResponse.json({
        success: false,
        error: 'Symbols array is required'
      }, { status: 400 });
    }

    // Get multiple companies by symbols
    const companies = await Promise.all(
      symbols.map(symbol => DatabaseService.getCompanyBySymbol(symbol))
    );

    const validCompanies = companies.filter(company => company !== null);

    return NextResponse.json({
      success: true,
      data: validCompanies,
      found: validCompanies.length,
      requested: symbols.length
    });

  } catch (error) {
    console.error('Companies POST API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
