{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "3c8e2423d857715a42902548e3709095", "previewModeSigningKey": "e04e42139d5ce550d24f49292da0c7eeb0911de4b35451e74ac162512f326839", "previewModeEncryptionKey": "b4fbdfe3de65ab1e8e0d3c1d96d4b3a303ca13e391b4d1ee140955301af61218"}}