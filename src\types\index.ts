import { WebSocket as WS } from "ws";

export interface WebSocketMessage {
  type:
    | "subscribe"
    | "unsubscribe"
    | "connection"
    | "subscription"
    | "error"
    | "marketData";
  symbols?: string[];
  status?: string;
  message?: string;
  data?: MarketData;
}

export interface MarketData {
  symbol: string;
  exchange: string;
  lastPrice: number;
  change: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
  timestamp: number;
}

export interface Symbol {
  exchange: string;
  symbol: string;
}

export interface Client {
  id: string;
  ws: WS;
  subscriptions: Set<string>;
}
