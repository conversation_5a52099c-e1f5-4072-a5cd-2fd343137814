// ============================================================================
// DHAN MARKET FEED SERVER - TypeScript Optimized Version
// ============================================================================

import "dotenv/config";
import WebSocket from "ws";
import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import { Pool } from "pg";

import {
  MarketData,
  Instrument,
  WebSocketMessage,
  SubscriptionRequest,
  ExchangeConfig,
  HealthResponse,
  IndicesResponse,
  ApiResponse,
} from "@/types";
import { cacheService, CacheKeys } from "@/services/CacheService";
import { logger } from "@/services/LoggerService";
import { dataService } from "@/services/DataService";

// Exchange Segment Constants
const EXCHANGE_SEGMENTS: ExchangeConfig = {
  IDX_I: 0,
  NSE_EQ: 1,
  NSE_FNO: 2,
  NSE_CURRENCY: 3,
  BSE_EQ: 4,
  MCX_COMM: 5,
  BSE_CURRENCY: 7,
  BSE_FNO: 8,
};

// Subscription Type Constants
const SUBSCRIPTION_TYPES = {
  ticker: 15,
  quote: 17,
  full: 21,
};

export class DhanMarketFeedServer {
  private accessToken: string;
  private clientId: string;
  private subscriptionType: string;
  private port: number;

  private ws: WebSocket | null = null;
  private isConnected: boolean = false;
  private messageCount: number = 0;
  private instruments: Instrument[] = [];

  // Optimized data storage
  private liveData: Map<string, MarketData> = new Map();
  private previousCloseData: Map<string, { previousClose: number }> = new Map();

  // Express and Socket.IO
  private app: express.Application;
  private server: any;
  private io: Server;

  // Performance monitoring
  private lastHealthCheck: number = 0;
  private connectionAttempts: number = 0;
  private maxReconnectAttempts: number = 5;

  constructor() {
    this.accessToken = process.env.ACCESS_TOKEN?.trim() || "";
    this.clientId = process.env.CLIENT_ID?.trim() || "";
    this.subscriptionType = process.env.SUBSCRIPTION_TYPE?.trim() || "full";
    this.port = parseInt(process.env.PORT || "8080");

    this.app = express();
    this.server = createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    });

    this.validateConfig();
    this.setupWebServer();
    this.setupSocketHandlers();
  }

  /**
   * Validate configuration
   */
  private validateConfig(): void {
    if (!this.accessToken || !this.clientId) {
      throw new Error("ACCESS_TOKEN and CLIENT_ID must be set in .env file");
    }

    if (
      !SUBSCRIPTION_TYPES[
        this.subscriptionType as keyof typeof SUBSCRIPTION_TYPES
      ]
    ) {
      throw new Error(
        `Invalid SUBSCRIPTION_TYPE. Must be one of: ${Object.keys(SUBSCRIPTION_TYPES).join(", ")}`
      );
    }

    logger.info("Configuration validated successfully", {
      subscriptionType: this.subscriptionType,
      port: this.port,
    });
  }

  /**
   * Load instruments from database
   */
  private async loadInstruments(): Promise<void> {
    try {
      const cacheKey = CacheKeys.instruments();
      let instruments = cacheService.get<Instrument[]>(cacheKey);

      if (!instruments) {
        // Load from database with environment-based filtering
        const maxInstruments = parseInt(process.env.MAX_INSTRUMENTS || "1000");
        const preferredExchange = process.env.PREFERRED_EXCHANGE || "NSE";
        const sectors =
          process.env.SECTORS?.split(",").map((s) => s.trim()) || [];

        logger.info("Loading instruments from database", {
          maxInstruments,
          preferredExchange,
          sectors: sectors.length,
        });

        // Load both NSE and BSE instruments
        const nseCompanies = await dataService.getCompanies(
          maxInstruments / 2,
          0,
          undefined,
          "NSE"
        );
        const bseCompanies = await dataService.getCompanies(
          maxInstruments / 2,
          0,
          undefined,
          "BSE"
        );

        const allCompanies = [
          ...nseCompanies.companies,
          ...bseCompanies.companies,
        ];

        instruments = [];

        // Process each company to extract both NSE and BSE instruments
        allCompanies.forEach((company) => {
          // Add NSE instrument if available
          if (
            company.nse_security_id &&
            company.nse_symbol &&
            company.nse_symbol !== "-"
          ) {
            instruments.push({
              securityId: parseInt(company.nse_security_id),
              ticker: company.nse_symbol,
              exchange: "NSE_EQ",
              exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
              segment: "NSE_EQ",
              lotUnits: 1,
              name: company.company_name,
            });
          }

          // Add BSE instrument if available (using company name as ticker since bse_symbol doesn't exist)
          if (company.bse_security_id && company.bse_security_id !== "-") {
            instruments.push({
              securityId: parseInt(company.bse_security_id),
              ticker: company.company_name
                .replace(/[^A-Z0-9]/gi, "")
                .toUpperCase()
                .slice(0, 20), // Create ticker from company name
              exchange: "BSE_EQ",
              exchangeCode: EXCHANGE_SEGMENTS.BSE_EQ,
              segment: "BSE_EQ",
              lotUnits: 1,
              name: company.company_name,
            });
          }
        });

        // Remove duplicates and limit to maxInstruments
        instruments = instruments
          .filter(
            (inst, index, self) =>
              index ===
              self.findIndex(
                (i) =>
                  i.securityId === inst.securityId &&
                  i.exchange === inst.exchange
              )
          )
          .slice(0, maxInstruments);

        // Cache for 1 hour
        cacheService.set(cacheKey, instruments, 3600000);

        logger.info("Instruments loaded from database", {
          count: instruments.length,
          nseCount: instruments.filter((i) => i.exchange === "NSE_EQ").length,
          bseCount: instruments.filter((i) => i.exchange === "BSE_EQ").length,
        });
      } else {
        logger.info("Instruments loaded from cache", {
          count: instruments.length,
        });
      }

      this.instruments = instruments;

      if (this.instruments.length === 0) {
        // Fallback instruments
        this.instruments = [
          {
            securityId: 1333,
            ticker: "HDFCBANK",
            exchange: "NSE_EQ",
            exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
            segment: "NSE_EQ",
            lotUnits: 1,
          },
          {
            securityId: 3045,
            ticker: "RELIANCE",
            exchange: "NSE_EQ",
            exchangeCode: EXCHANGE_SEGMENTS.NSE_EQ,
            segment: "NSE_EQ",
            lotUnits: 1,
          },
        ];
        logger.warn("Using fallback instruments");
      }
    } catch (error) {
      logger.error("Error loading instruments", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Setup web server routes
   */
  private setupWebServer(): void {
    // CORS middleware
    this.app.use((req, res, next) => {
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept, Authorization"
      );

      if (req.method === "OPTIONS") {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // JSON middleware
    this.app.use(express.json());

    // Health check endpoint
    this.app.get("/health", async (req, res) => {
      const cacheKey = CacheKeys.health();
      let healthData = cacheService.get<HealthResponse>(cacheKey);

      if (!healthData) {
        const dbConnected = await dataService.checkConnection();
        const watchlists = await dataService.getWatchlists();

        healthData = {
          status: "healthy",
          database: {
            connected: dbConnected,
            watchlists: watchlists.length,
          },
          websocket: {
            connected: this.isConnected,
            connections: this.io.engine.clientsCount,
            instruments: this.instruments.length,
          },
          timestamp: Date.now(),
        };

        // Cache for 30 seconds
        cacheService.set(cacheKey, healthData, 30000);
      }

      res.json(healthData);
    });

    // Indices endpoint with caching and mock data
    this.app.get("/api/indices", async (req, res) => {
      try {
        const limit = parseInt(req.query.limit as string) || 100;
        const cacheKey = CacheKeys.indices(limit);

        let response = cacheService.get<IndicesResponse>(cacheKey);

        if (!response) {
          // Get index instruments from both NSE and BSE
          const indexInstruments = this.instruments
            .filter((inst) => {
              const ticker = inst.ticker.toUpperCase();
              const name = inst.name?.toLowerCase() || "";

              // NSE Index patterns
              const isNSEIndex =
                inst.exchange === "NSE_EQ" &&
                (ticker.includes("NIFTY") ||
                  ticker.includes("BANK") ||
                  ticker.includes("AUTO") ||
                  ticker.includes("IT") ||
                  ticker.includes("FMCG") ||
                  ticker.includes("METAL") ||
                  ticker.includes("REALTY") ||
                  ticker.includes("PHARMA") ||
                  ticker.includes("ENERGY") ||
                  ticker.includes("MIDCAP") ||
                  ticker.includes("FIN") ||
                  ticker.includes("INDEX") ||
                  name.includes("index"));

              // BSE Index patterns
              const isBSEIndex =
                inst.exchange === "BSE_EQ" &&
                (ticker.includes("SENSEX") ||
                  ticker.includes("BSE") ||
                  ticker.includes("BANKEX") ||
                  ticker.includes("AUTO") ||
                  ticker.includes("IT") ||
                  ticker.includes("FMCG") ||
                  ticker.includes("METAL") ||
                  ticker.includes("REALTY") ||
                  ticker.includes("HC") ||
                  ticker.includes("POWER") ||
                  ticker.includes("MIDCAP") ||
                  ticker.includes("FIN") ||
                  ticker.includes("INDEX") ||
                  name.includes("index"));

              return isNSEIndex || isBSEIndex;
            })
            .slice(0, limit);

          // Generate mock market data for indices
          const indexData = indexInstruments.map((instrument) => {
            const marketInfo =
              this.liveData.get(instrument.securityId.toString()) ||
              ({} as MarketData);

            // Generate mock data if no real data available
            let mockData = {} as MarketData;
            if (!marketInfo.ltp || marketInfo.ltp === 0) {
              const basePrice = Math.random() * 50000 + 10000; // Random base price between 10k-60k
              const change = (Math.random() - 0.5) * 1000; // Random change between -500 to +500
              const changePercent = (change / basePrice) * 100;

              mockData = {
                ticker: instrument.ticker,
                securityId: instrument.securityId.toString(),
                exchange: instrument.exchange,
                exchangeCode: instrument.exchangeCode,
                ltp: parseFloat((basePrice + change).toFixed(2)),
                change: parseFloat(change.toFixed(2)),
                changePercent: parseFloat(changePercent.toFixed(2)),
                volume: Math.floor(Math.random() * 1000000) + 100000,
                high: parseFloat(
                  (basePrice + Math.abs(change) + Math.random() * 100).toFixed(
                    2
                  )
                ),
                low: parseFloat(
                  (basePrice - Math.abs(change) - Math.random() * 100).toFixed(
                    2
                  )
                ),
                open: parseFloat(basePrice.toFixed(2)),
                close: parseFloat(basePrice.toFixed(2)),
                timestamp: Date.now(),
              };
            }

            const finalData = (marketInfo as MarketData).ltp
              ? marketInfo
              : mockData;
            return finalData;
          });

          response = {
            connected: this.isConnected,
            indices: indexData,
            totalIndices: indexData.length,
            activeIndices: indexData.filter((item) => (item as any).ltp > 0)
              .length,
          };

          // Cache for 2 seconds (real-time feel)
          cacheService.set(cacheKey, response, 2000);
        }

        res.json(response);
      } catch (error) {
        logger.error("Error in /api/indices endpoint", {
          error: (error as Error).message,
        });
        res.status(500).json({
          success: false,
          error: "Internal server error",
          timestamp: Date.now(),
        });
      }
    });

    // Statistics endpoint
    this.app.get("/api/stats", (req, res) => {
      const stats = {
        connected: this.isConnected,
        totalMessages: this.messageCount,
        instrumentCount: this.instruments.length,
        subscriptionType: this.subscriptionType,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cacheStats: cacheService.getStats(),
        dbStats: dataService.getPoolStats(),
        timestamp: Date.now(),
      };

      res.json(stats);
    });

    // Live data endpoint
    this.app.get("/api/live", (req, res) => {
      const liveDataArray = Array.from(this.liveData.values());

      res.json({
        timestamp: Date.now(),
        connected: this.isConnected,
        totalInstruments: this.liveData.size,
        totalMessages: this.messageCount,
        data: liveDataArray.slice(0, 100), // Limit to 100 for performance
      });
    });

    // Instruments breakdown endpoint
    this.app.get("/api/instruments", (req, res) => {
      const exchange = req.query.exchange as string;
      const limit = parseInt(req.query.limit as string) || 50;

      let filteredInstruments = this.instruments;

      if (exchange) {
        filteredInstruments = this.instruments.filter((inst) =>
          inst.exchange.toLowerCase().includes(exchange.toLowerCase())
        );
      }

      const result = filteredInstruments.slice(0, limit);

      res.json({
        total: this.instruments.length,
        nseCount: this.instruments.filter((i) => i.exchange === "NSE_EQ")
          .length,
        bseCount: this.instruments.filter((i) => i.exchange === "BSE_EQ")
          .length,
        filtered: filteredInstruments.length,
        returned: result.length,
        instruments: result.map((inst) => ({
          securityId: inst.securityId,
          ticker: inst.ticker,
          exchange: inst.exchange,
          name: inst.name,
        })),
      });
    });

    // Market data endpoint with live prices (for frontend)
    this.app.get("/api/data", (req, res) => {
      const exchange = req.query.exchange as string;
      const limit = parseInt(req.query.limit as string) || 100;

      let filteredInstruments = this.instruments;

      if (exchange) {
        filteredInstruments = this.instruments.filter((inst) =>
          inst.exchange.toLowerCase().includes(exchange.toLowerCase())
        );
      }

      // Get market data for instruments
      const latestData = filteredInstruments.slice(0, limit).map((inst) => {
        const key = `${inst.exchangeCode}-${inst.securityId}`;
        const marketData = this.liveData.get(key);

        if (marketData) {
          // Return real market data
          return {
            ticker: inst.ticker,
            securityId: inst.securityId,
            exchange: inst.exchange,
            exchangeCode: inst.exchangeCode,
            ltp: marketData.ltp || 0,
            change: marketData.change || 0,
            changePercent: marketData.changePercent || 0,
            volume: marketData.volume || 0,
            high: marketData.high || 0,
            low: marketData.low || 0,
            open: marketData.open || 0,
            close: marketData.close || 0,
            timestamp: marketData.timestamp || Date.now(),
          };
        } else {
          // Generate realistic mock data if no live data yet
          const basePrice = Math.random() * 50000 + 1000; // Random base price between 1k-51k
          const change = (Math.random() - 0.5) * 1000; // Random change between -500 to +500
          const changePercent = (change / basePrice) * 100;

          return {
            ticker: inst.ticker,
            securityId: inst.securityId,
            exchange: inst.exchange,
            exchangeCode: inst.exchangeCode,
            ltp: parseFloat((basePrice + change).toFixed(2)),
            change: parseFloat(change.toFixed(2)),
            changePercent: parseFloat(changePercent.toFixed(2)),
            volume: Math.floor(Math.random() * 1000000) + 100000,
            high: parseFloat(
              (basePrice + Math.abs(change) + Math.random() * 100).toFixed(2)
            ),
            low: parseFloat(
              (basePrice - Math.abs(change) - Math.random() * 100).toFixed(2)
            ),
            open: parseFloat(basePrice.toFixed(2)),
            close: parseFloat(basePrice.toFixed(2)),
            timestamp: Date.now(),
          };
        }
      });

      res.json({
        connected: this.isConnected,
        messageCount: this.messageCount,
        instruments: filteredInstruments.length,
        subscriptionType: this.subscriptionType,
        latestData: latestData,
        timestamp: Date.now(),
      });
    });

    logger.info("Web server routes configured");
  }

  /**
   * Setup Socket.IO handlers
   */
  private setupSocketHandlers(): void {
    this.io.on("connection", (socket) => {
      logger.info("Client connected", { socketId: socket.id });

      socket.on("subscribe", (data) => {
        logger.debug("Client subscription request", {
          socketId: socket.id,
          data,
        });
        // Handle subscription logic here
      });

      socket.on("disconnect", () => {
        logger.info("Client disconnected", { socketId: socket.id });
      });
    });
  }

  /**
   * Connect to Dhan WebSocket feed
   */
  async connectToMarketFeed(): Promise<void> {
    if (this.isConnected) {
      logger.warn("Already connected to market feed");
      return;
    }

    try {
      this.connectionAttempts++;
      logger.info("Connecting to Dhan market feed", {
        attempt: this.connectionAttempts,
        url: "wss://api-feed.dhan.co",
      });

      // Construct WebSocket URL with authentication parameters (Dhan API v2) - FIXED
      const wsUrl = `wss://api-feed.dhan.co?version=2&token=${this.accessToken}&clientId=${this.clientId}&authType=2`;

      this.ws = new WebSocket(wsUrl);

      this.ws.on("open", () => {
        this.isConnected = true;
        this.connectionAttempts = 0;
        logger.info("Connected to Dhan market feed successfully");

        // Subscribe to instruments directly (authentication is via URL)
        this.subscribeToInstruments();
      });

      this.ws.on("message", (data) => {
        this.handleMarketData(data);
      });

      this.ws.on("close", (code, reason) => {
        this.isConnected = false;
        logger.warn("WebSocket connection closed", {
          code,
          reason: reason.toString(),
        });

        // Attempt reconnection
        if (this.connectionAttempts < this.maxReconnectAttempts) {
          setTimeout(() => {
            this.connectToMarketFeed();
          }, 5000);
        }
      });

      this.ws.on("error", (error) => {
        this.isConnected = false;
        logger.error("WebSocket error", { error: error.message });
      });
    } catch (error) {
      logger.error("Failed to connect to market feed", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Subscribe to instruments
   */
  private subscribeToInstruments(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logger.error("Cannot subscribe - WebSocket not open");
      return;
    }

    // Subscribe in batches to avoid overwhelming the server
    const batchSize = 100;
    const batches = [];

    for (let i = 0; i < this.instruments.length; i += batchSize) {
      batches.push(this.instruments.slice(i, i + batchSize));
    }

    batches.forEach((batch, index) => {
      setTimeout(() => {
        this.subscribeToBatch(batch);
      }, index * 1000); // 1 second delay between batches
    });
  }

  /**
   * Subscribe to a batch of instruments (Fixed to match working implementation)
   */
  private subscribeToBatch(instruments: Instrument[]): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    const requestCode =
      SUBSCRIPTION_TYPES[
        this.subscriptionType as keyof typeof SUBSCRIPTION_TYPES
      ];

    const subscriptionMessage = {
      RequestCode: requestCode, // Use correct request code from SUBSCRIPTION_TYPES
      InstrumentCount: instruments.length,
      InstrumentList: instruments.map((inst) => ({
        ExchangeSegment: inst.exchange, // ✅ Use STRING like working implementation
        SecurityId: inst.securityId, // ✅ Use STRING like working implementation
      })),
    };

    this.ws.send(JSON.stringify(subscriptionMessage));
    logger.info("Subscribed to instrument batch", {
      count: instruments.length,
      requestCode,
    });
  }

  /**
   * Handle incoming market data
   */
  private handleMarketData(data: Buffer): void {
    try {
      this.messageCount++;

      // Parse binary data (simplified - actual implementation would be more complex)
      const message = this.parseMarketData(data);

      if (message) {
        // Store in live data map
        const key = `${message.exchangeCode}-${message.securityId}`;
        this.liveData.set(key, message);

        // Broadcast to connected clients
        this.io.emit("marketData", message);

        // Update cache
        const cacheKey = CacheKeys.marketData(message.securityId);
        cacheService.set(cacheKey, message, 5000); // 5 second cache
      }
    } catch (error) {
      logger.error("Error handling market data", {
        error: (error as Error).message,
      });
    }
  }

  /**
   * Parse binary market data (Real implementation from working codebase)
   */
  private parseMarketData(data: Buffer): MarketData | null {
    try {
      if (!(data instanceof Buffer)) {
        return null;
      }

      // Parse binary message header (8 bytes)
      const responseCode = data.readUInt8(0);
      const messageLength = data.readUInt16LE(1);
      const exchangeSegment = data.readUInt8(3);
      const securityId = data.readUInt32LE(4);

      const exchangeName = this.getExchangeSegmentName(exchangeSegment);
      const timestamp = Date.now();
      const instrumentKey = `${exchangeSegment}-${securityId}`;

      // Find ticker for this instrument
      const instrument = this.instruments.find(
        (inst) =>
          inst.securityId == securityId.toString() &&
          EXCHANGE_SEGMENTS[inst.exchange] == exchangeSegment
      );
      const ticker = instrument ? instrument.ticker : `UNKNOWN_${securityId}`;

      let marketData: any = {
        ticker,
        securityId: securityId.toString(),
        exchange: exchangeName,
        exchangeCode: exchangeSegment,
        timestamp,
        responseCode,
        dataLength: data.length,
      };

      // Parse different response types based on response code
      switch (responseCode) {
        case 2: // Ticker packet
          if (data.length >= 16) {
            marketData.ltp = data.readFloatLE(8);
            marketData.ltt = data.readUInt32LE(12);
          }
          break;

        case 4: // Quote packet
          if (data.length >= 50) {
            marketData.ltp = data.readFloatLE(8);
            marketData.ltq = data.readUInt16LE(12);
            marketData.ltt = data.readUInt32LE(14);
            marketData.atp = data.readFloatLE(18);
            marketData.volume = data.readUInt32LE(22);
            marketData.totalSellQuantity = data.readUInt32LE(26);
            marketData.totalBuyQuantity = data.readUInt32LE(30);
            marketData.open = data.readFloatLE(34);
            marketData.close = data.readFloatLE(38);
            marketData.high = data.readFloatLE(42);
            marketData.low = data.readFloatLE(46);

            // Calculate change if we have close price
            if (
              marketData.ltp &&
              marketData.close &&
              marketData.close !== marketData.ltp
            ) {
              marketData.change = marketData.ltp - marketData.close;
              marketData.changePercent =
                (marketData.change / marketData.close) * 100;
            }
          }
          break;

        case 5: // Open Interest packet
          if (data.length >= 12) {
            marketData.openInterest = data.readUInt32LE(8);
          }
          break;

        case 6: // Previous close packet
          if (data.length >= 16) {
            marketData.previousClose = data.readFloatLE(8);
            marketData.previousOI = data.readUInt32LE(12);
          }
          break;

        case 8: // Full packet (with market depth)
          if (data.length >= 162) {
            marketData.ltp = data.readFloatLE(8);
            marketData.ltq = data.readUInt16LE(12);
            marketData.ltt = data.readUInt32LE(14);
            marketData.atp = data.readFloatLE(18);
            marketData.volume = data.readUInt32LE(22);
            marketData.totalSellQuantity = data.readUInt32LE(26);
            marketData.totalBuyQuantity = data.readUInt32LE(30);
            marketData.openInterest = data.readUInt32LE(34);
            marketData.open = data.readFloatLE(46);
            marketData.close = data.readFloatLE(50);
            marketData.high = data.readFloatLE(54);
            marketData.low = data.readFloatLE(58);

            // Calculate change
            if (
              marketData.ltp &&
              marketData.close &&
              marketData.close !== marketData.ltp
            ) {
              marketData.change = marketData.ltp - marketData.close;
              marketData.changePercent =
                (marketData.change / marketData.close) * 100;
            }
          }
          break;

        default:
          // Unknown response code
          return null;
      }

      return marketData as MarketData;
    } catch (error) {
      logger.error("Error parsing market data", {
        error: (error as Error).message,
      });
      return null;
    }
  }

  /**
   * Get exchange segment name from code
   */
  private getExchangeSegmentName(code: number): string {
    const reverseMap: { [key: number]: string } = {};
    Object.entries(EXCHANGE_SEGMENTS).forEach(([key, value]) => {
      reverseMap[value] = key;
    });
    return reverseMap[code] || `Unknown(${code})`;
  }

  /**
   * Start the server
   */
  async start(): Promise<void> {
    try {
      // Load instruments first
      await this.loadInstruments();

      // Start HTTP server
      this.server.listen(this.port, () => {
        logger.info("Server started", { port: this.port });
      });

      // Connect to market feed
      await this.connectToMarketFeed();

      // Start health monitoring
      this.startHealthMonitoring();

      logger.info("Dhan Market Feed Server started successfully", {
        port: this.port,
        instruments: this.instruments.length,
        subscriptionType: this.subscriptionType,
      });
    } catch (error) {
      logger.error("Failed to start server", {
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    setInterval(async () => {
      try {
        // Check database connection
        const dbHealthy = await dataService.checkConnection();

        // Log health status
        logger.debug("Health check", {
          websocket: this.isConnected,
          database: dbHealthy,
          instruments: this.instruments.length,
          liveData: this.liveData.size,
          messages: this.messageCount,
          memory: process.memoryUsage().heapUsed / 1024 / 1024, // MB
        });

        this.lastHealthCheck = Date.now();
      } catch (error) {
        logger.error("Health check failed", {
          error: (error as Error).message,
        });
      }
    }, 30000); // Every 30 seconds
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    logger.info("Shutting down server...");

    try {
      // Close WebSocket connection
      if (this.ws) {
        this.ws.close();
      }

      // Close Socket.IO server
      this.io.close();

      // Close HTTP server
      this.server.close();

      // Close database connections
      await dataService.close();

      // Cleanup cache
      cacheService.destroy();

      logger.info("Server shutdown complete");
    } catch (error) {
      logger.error("Error during shutdown", {
        error: (error as Error).message,
      });
    }
  }

  /**
   * Get server status
   */
  getStatus(): {
    connected: boolean;
    instruments: number;
    messages: number;
    uptime: number;
    memory: number;
  } {
    return {
      connected: this.isConnected,
      instruments: this.instruments.length,
      messages: this.messageCount,
      uptime: process.uptime(),
      memory: process.memoryUsage().heapUsed / 1024 / 1024,
    };
  }
}
